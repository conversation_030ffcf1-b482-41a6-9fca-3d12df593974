# ThinkPartnership Marketplace APIs - Postman Testing Guide

## Overview

This guide explains how to use the comprehensive Postman collection for testing all ThinkPartnership Marketplace APIs across four different portals:

- 🛒 **Customer Portal** - For service consumers
- 🔧 **Vendor Portal** - For service providers  
- 🏢 **Client Portal** - For marketplace owners
- 👑 **Admin Portal** - For platform administrators

## Quick Setup

### 1. Import the Collection

1. Open Postman
2. Click **Import** button
3. Select the `ThinkPartnership_Marketplace_APIs.postman_collection.json` file
4. Click **Import**

### 2. Set Up Environment

The collection includes automatic environment setup, but you can also manually create an environment:

1. Click **Environments** in Postman
2. Click **Create Environment**
3. Name it "ThinkPartnership Local"
4. Add these variables:

```
BASE_URL = http://localhost:5172
CUSTOMER_TOKEN = (will be set automatically after login)
VENDOR_TOKEN = (will be set automatically after login)
CLIENT_TOKEN = (will be set automatically after login)
ADMIN_TOKEN = (will be set automatically after login)
```

### 3. Start Your Backend Server

Make sure your backend server is running on `http://localhost:5172`

```bash
cd mkd-backend-flow-builder
npm start
```

## Testing Workflow

### 🛒 Customer Portal Testing

1. **Register a Customer**
   - Use "Customer Register" endpoint
   - Token will be automatically saved to environment

2. **Login (Alternative)**
   - Use "Customer Login" endpoint if you already have an account
   - Token will be automatically saved

3. **Test Customer Features**
   - Get Services
   - Get Service Categories
   - Create Service Request
   - Get Customer Requests
   - Get Customer Orders

### 🔧 Vendor Portal Testing

1. **Register a Vendor**
   - Use "Vendor Register" endpoint
   - Token and vendor_id will be automatically saved

2. **Login (Alternative)**
   - Use "Vendor Login" endpoint
   - Token and vendor_id will be automatically saved

3. **Test Vendor Features**
   - Get/Update Vendor Profile
   - Create/Manage Services
   - View Service Requests
   - Send Quotes
   - View Analytics

### 🏢 Client Portal Testing

1. **Register a Client**
   - Use "Client Register" endpoint
   - Token and client_id will be automatically saved

2. **Login (Alternative)**
   - Use "Client Login" endpoint

3. **Test Client Features**
   - Dashboard Statistics
   - Vendor Management
   - Customer Analytics
   - Branding Settings
   - Invite System
   - Wallet & Transactions

### 👑 Admin Portal Testing

1. **Admin Login**
   - Use "Admin Login" endpoint
   - **Note**: You need to create an admin user in the database first
   - Token will be automatically saved

2. **Test Admin Features**
   - Platform Statistics
   - Revenue Analytics
   - Manage All Clients
   - Manage All Vendors
   - Manage All Customers
   - Revenue Rules
   - Manual Commissions
   - Reports & Data Export
   - System Management

## Authentication Flow

### Automatic Token Management

The collection includes automatic token management:

- After successful login/register, tokens are automatically saved to environment variables
- All subsequent requests use the appropriate token automatically
- No manual token copying required!

### Manual Token Setup (if needed)

If automatic token saving doesn't work:

1. Copy the token from login response
2. Go to Environment variables
3. Set the appropriate token variable:
   - `CUSTOMER_TOKEN` for customer APIs
   - `VENDOR_TOKEN` for vendor APIs
   - `CLIENT_TOKEN` for client APIs
   - `ADMIN_TOKEN` for admin APIs

## Sample Test Data

### Customer Registration
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "first_name": "John",
  "last_name": "Doe",
  "phone": "******-123-4567",
  "address": "123 Main Street",
  "city": "Toronto",
  "province": "Ontario",
  "postal_code": "M5V 3A8",
  "is_refresh": true
}
```

### Vendor Registration
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "business_name": "ABC Plumbing Services",
  "contact_name": "John Smith",
  "phone": "******-123-4567",
  "business_address": "123 Main Street",
  "city": "Toronto",
  "province": "Ontario",
  "postal_code": "M5V 3A8",
  "description": "Professional plumbing services with 10+ years experience",
  "is_refresh": true
}
```

### Client Registration
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "company_name": "TechCorp Marketplace",
  "contact_name": "Jane Smith",
  "phone": "******-987-6543",
  "business_address": "456 Business Ave",
  "city": "Toronto",
  "province": "Ontario",
  "postal_code": "M4B 1B3",
  "business_type": "Technology Services",
  "is_refresh": true
}
```

## Creating Admin User

To test admin APIs, you need to create an admin user in the database:

```sql
INSERT INTO thinkpartnership_user (email, password, role_id, status, data, created_at, updated_at) 
VALUES (
  '<EMAIL>', 
  '$2b$10$hashedpassword', -- Use proper password hashing
  'admin', 
  1, 
  '{"name": "Platform Admin"}',
  NOW(), 
  NOW()
);
```

## API Response Codes

- **200** - Success (GET, PUT requests)
- **201** - Created (POST requests)
- **400** - Bad Request (validation errors)
- **401** - Unauthorized (invalid/missing token)
- **403** - Forbidden (insufficient permissions)
- **404** - Not Found
- **500** - Internal Server Error

## Troubleshooting

### Common Issues

1. **401 Unauthorized**
   - Check if you're logged in
   - Verify token is set in environment
   - Check token expiration

2. **403 Forbidden**
   - Verify user role permissions
   - Admin endpoints require admin role
   - Client endpoints require client role

3. **404 Not Found**
   - Check if backend server is running
   - Verify endpoint URLs
   - Check if resources exist

4. **500 Internal Server Error**
   - Check backend server logs
   - Verify database connection
   - Check for missing required fields

### Debug Tips

1. **Check Environment Variables**
   - Click the eye icon next to environment name
   - Verify tokens are set correctly

2. **View Request/Response**
   - Check the request body and headers
   - Review response for error details

3. **Console Logs**
   - Open Postman Console (View → Show Postman Console)
   - Check for script execution logs

## Collection Features

- ✅ Automatic token management
- ✅ Environment variable setup
- ✅ Organized by portal type
- ✅ Sample request bodies
- ✅ Query parameter examples
- ✅ Response validation scripts
- ✅ Comprehensive coverage of all endpoints

## Next Steps

1. Import the collection
2. Set up your environment
3. Start with Customer Portal testing
4. Progress through Vendor, Client, and Admin portals
5. Test the complete marketplace workflow

Happy testing! 🚀
