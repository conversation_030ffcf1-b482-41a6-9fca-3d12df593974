{"info": {"name": "Marketplace Customer APIs", "description": "Complete API collection for the marketplace customer portal", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://127.0.0.1:5172", "type": "string"}, {"key": "authToken", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "Customer Registration", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\",\n  \"phone\": \"******-123-4567\",\n  \"is_refresh\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/marketplace/auth/register", "host": ["{{baseUrl}}"], "path": ["api", "marketplace", "auth", "register"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.token) {", "        pm.collectionVariables.set('authToken', response.token);", "        console.log('<PERSON><PERSON> saved:', response.token);", "    }", "}"]}}]}, {"name": "Customer <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"is_refresh\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/marketplace/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "marketplace", "auth", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.token) {", "        pm.collectionVariables.set('authToken', response.token);", "        console.log('<PERSON><PERSON> saved:', response.token);", "    }", "}"]}}]}, {"name": "Get Customer Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/marketplace/auth/profile", "host": ["{{baseUrl}}"], "path": ["api", "marketplace", "auth", "profile"]}}}]}, {"name": "Marketplace Catalog", "item": [{"name": "Get Service Categories", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/marketplace/categories", "host": ["{{baseUrl}}"], "path": ["api", "marketplace", "categories"]}}}, {"name": "Get Services", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/marketplace/services", "host": ["{{baseUrl}}"], "path": ["api", "marketplace", "services"]}}}, {"name": "Get Services with Filters", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/marketplace/services?category_id=1&page=1&limit=5&status=1", "host": ["{{baseUrl}}"], "path": ["api", "marketplace", "services"], "query": [{"key": "category_id", "value": "1", "description": "Filter by category ID"}, {"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "5", "description": "Items per page"}, {"key": "status", "value": "1", "description": "Service status (1 = active)"}]}}}]}, {"name": "Service Requests", "item": [{"name": "Create Service Request", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"service_id\": 1,\n  \"description\": \"Need plumbing repair for kitchen sink\",\n  \"preferred_date\": \"2024-02-15\",\n  \"budget_range\": \"$100-200\",\n  \"location\": \"Downtown Toronto\",\n  \"urgency\": \"normal\"\n}"}, "url": {"raw": "{{baseUrl}}/api/marketplace/service-requests", "host": ["{{baseUrl}}"], "path": ["api", "marketplace", "service-requests"]}}}, {"name": "Get Customer Service Requests", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/marketplace/service-requests", "host": ["{{baseUrl}}"], "path": ["api", "marketplace", "service-requests"]}}}]}, {"name": "Order Management", "item": [{"name": "Get Customer Orders", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/marketplace/orders", "host": ["{{baseUrl}}"], "path": ["api", "marketplace", "orders"]}}}]}]}