@tailwind base;
@tailwind components;
@tailwind utilities;

.react-flow__node {
  @apply select-none;
}

.react-flow__handle {
  width: 8px !important;
  height: 8px !important;
  background: #3b82f6 !important;
  border: 2px solid white !important;
}

.react-flow__handle-left {
  left: -4px !important;
}

.react-flow__handle-right {
  right: -4px !important;
}

.react-flow__node-default {
  width: auto !important;
  padding: 8px 16px;
  border-radius: 6px;
}

.react-flow__edge-path {
  stroke: #64748b;
  stroke-width: 2;
}

.react-flow__edge.selected .react-flow__edge-path {
  stroke: #3b82f6;
  stroke-width: 3;
}

.react-flow__node.selected {
  box-shadow: 0 0 0 2px #3b82f6;
}

.react-flow__controls {
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.react-flow__panel {
  background: white;
  border-radius: 4px;
}

.react-flow {
  background-color: #f8fafc;
}