export const Endpoint = (id, prefix) => {
    return {
        id: id, 
        name: '', 
        description: '',
        method: 'GET', 
        route: `/v3/api/custom/${prefix}/`, 
        inputs: [{name: '', type: 'query', rules: '', dataType: "String"}], 
        response: [], 
        response_model: [], 
        logic: '',
        code: '', 
        doc: '', 
        unit: '',
        protected: true,
        authorizedRoles: [],
        imports: '',
        type:'custom'
    }
}