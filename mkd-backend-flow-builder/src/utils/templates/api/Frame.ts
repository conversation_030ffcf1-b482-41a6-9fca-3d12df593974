export default (apis, postman = []) => `
/**
 * So when we save the new lambda, we save the file path to server and we just read this file.
 * Then we trigger reload of server somehow.
 * @param {*} app
 */

/**
 * So when we save the new lambda, we save the file path to server and we just read this file.
 * Then we trigger reload of server somehow.
 * @param {*} app
 */
const ProjectMiddleware = require("../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../middleware/UrlMiddleware");
const HostMiddleware = require("../../middleware/HostMiddleware");
const TokenMiddleware = require("../../middleware/TokenMiddleware");

const config = require("../../config");
const { default: axios } = require("axios");
const ValidationService = require("../../services/ValidationService");
const {
  sqlDateFormat,
  sqlDateTimeFormat,
  filterEmptyFields
} = require("../../services/UtilService");
const { validateInputMethod, validateObject } = require('../../services/ValidationService');



const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  //TokenMiddleware(),
  // PermissionMiddleware,
  // RateLimitMiddleware,
]

const public = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  // TokenMiddleware(),
  // PermissionMiddleware,
  // RateLimitMiddleware,
];


module.exports = function (app) {    

    ${apis}


    return ${postman};
}

`
    

