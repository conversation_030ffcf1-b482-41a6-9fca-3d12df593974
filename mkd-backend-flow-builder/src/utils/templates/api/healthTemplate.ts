const healthTemplate = `
/**
 * So when we save the new lambda, we save the file path to server and we just read this file.
 * Then we trigger reload of server somehow.
 * @param {*} app
*/
module.exports = function (app) {

    app.get("/v2/api/custom/sample/health", function (req, res) {
        try {
            return res.status(200).json({ message: "Sample OK" });
        } catch (err) {
      console.error(err);
      res.status(404);
      res.json({
        message: err.message,
    });
    }
});

  app.post("/v2/api/custom/sample/post", function (req, res) {
      try {
          return res.status(200).json({ message: "Sample Post" });
    } catch (err) {
        console.error(err);
        res.status(404);
        res.json({
            message: err.message,
      });
    }
});
  return [];
}

`
export default healthTemplate; 