export const workflowConfig = [
  {
    name: "API_Route",
    type: "Route",
  },
  {
    name: "if_control",
    type: "Control Flow",
    conditions: [],
    action: {
      true: null,
      false: null,
    },
  },
  // {
  //   name: "switch_control",
  //   type: "Control Flow",
  // },
  {
    name: "variables",
    type: "Control Flow",
    variables: [],
  },
  {
    name: "db_query",
    type: "Actions",
    variables: [],
    query: null,
  },
  {
    name: "custom_code",
    type: "Actions",
    code: null,
  },
  {
    name: "http_request",
    type: "Actions",
  },
  {
    name: "success_response",
    type: "Responses",
  },
  {
    name: "error_response",
    type: "Responses",
  },
];
