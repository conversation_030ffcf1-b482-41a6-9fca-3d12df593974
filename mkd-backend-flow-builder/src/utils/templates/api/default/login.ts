export const login = [
    {
        id: '_loginLambda',
        name: '<PERSON>gin Lambda',
        method: 'POST',
        description: '',
        route: '/v2/api/lambda/login',
        inputs: [
          { name: 'email', type: 'body', rules: 'required', dataType: 'String' },
          { name: 'password', type: 'body', rules: 'required', dataType: 'String' },
          { name: 'role', type: 'body', rules: 'required', dataType: 'String' },
          { name: 'is_refresh', type: 'body', rules: '', dataType: 'Boolean' }
        ],
        response: [
          { id: 'error', key: 'error', value: 'false', valueType: 'Boolean', parent: '' },
          { id: 'role', key: 'role', value: 'role', valueType: 'String', parent: '' },
          { id: 'token', key: 'token', value: '', valueType: 'String', parent: '' },
          { id: 'refresh_token', key: 'refresh_token', value: 'refreshToken', valueType: 'String', parent: '' },
          { id: 'expire_at', key: 'expire_at', value: '', valueType: 'Number', parent: '' },
          { id: 'user_id', key: 'user_id', value: '', valueType: 'Int', parent: '' },
          { id: 'first_name', key: 'first_name', value: '', valueType: 'String', parent: '' },
          { id: 'last_name', key: 'last_name', value: ' ', valueType: 'String', parent: '' },
          { id: 'photo', key: 'photo', value: '', valueType: 'String', parent: '' },
          { id: 'two_factor_enabled', key: 'two_factor_enabled', value: '', valueType: 'Boolean', parent: '' }
        ],
        code: '',
        doc: '',
        unit: '',
        protected: true,
        authorizedRoles: [],
        imports: ``,
        type: 'default',
        group: 'login'
      },
      {
        id: '_marketingLoginLambda',
        name: 'Marketing Login Lambda',
        method: 'POST',
        description: '',
        route: '/v2/api/lambda/marketing-login',
        inputs: [
          { name: 'email', type: 'body', rules: 'required', dataType: 'String' },
          { name: 'password', type: 'body', rules: 'required', dataType: 'String' },
          { name: 'role', type: 'body', rules: 'required', dataType: 'String' },
          { name: 'is_refresh', type: 'body', rules: '', dataType: 'Boolean' }
        ],
        response: [
          { id: 'error', key: 'error', value: 'false', valueType: 'Boolean', parent: '' },
          { id: 'role', key: 'role', value: 'role', valueType: 'String', parent: '' },
          { id: 'token', key: 'token', value: 'JwtService.createAccessToken({ user_id: result.id, role }, config.jwt_expire, config.jwt_key)', valueType: 'String', parent: '' },
          { id: 'refresh_token', key: 'refresh_token', value: 'refreshToken', valueType: 'String', parent: '' },
          { id: 'expire_at', key: 'expire_at', value: 'config.jwt_expire', valueType: 'Number', parent: '' },
          { id: 'user_id', key: 'user_id', value: 'result.id', valueType: 'String', parent: '' },
          { id: 'two_factor_enabled', key: 'two_factor_enabled', value: 'result.two_factor_authentication === 1 ? true : false', valueType: 'Boolean', parent: '' }
        ],
        code: '',
        doc: '',
        unit: '',
        protected: true,
        authorizedRoles: [],
        imports: ``,
        type: 'default',
        group: 'login'
      }
      
      
]