export const profile = [
    {
        id: '_profile',
        name: 'profile',
        method: 'GET',
        description: '',
        route: '/v2/api/lambda/profile',
        inputs: [],
        response: [
          { id: 'id', key: 'id', value: 'result[0].id', valueType: 'String', parent: '' },
          { id: 'first_name', key: 'first_name', value: 'result[0].first_name', valueType: 'String', parent: '' },
          { id: 'email', key: 'email', value: 'result[0].email', valueType: 'String', parent: '' },
          { id: 'role', key: 'role', value: 'result[0].role', valueType: 'String', parent: '' },
          { id: 'last_name', key: 'last_name', value: 'result[0].last_name', valueType: 'String', parent: '' },
          { id: 'phone', key: 'phone', value: 'result[0].phone ?? ""', valueType: 'String', parent: '' },
          { id: 'photo', key: 'photo', value: 'result[0].photo ?? ""', valueType: 'String', parent: '' },
        ],
        code: ``,
        doc: '',
        unit: '',
        protected: true,
        authorizedRoles: [],
        imports: ``,
        type: 'default',
        group: 'profile'
    },
    {
        id: '_profileUpdate',
        name: 'Profile Update',
        method: 'POST',
        description: '',
        route: '/v2/api/lambda/profile',
        inputs: [
          { name: 'first_name', type: 'body', rules: '', dataType: 'String' },
          { name: 'last_name', type: 'body', rules: '', dataType: 'String' },
          { name: 'photo', type: 'body', rules: '', dataType: 'String', defaultValue: 'null' }
        ],
        response: [
          { id: 'error', key: 'error', value: 'false', valueType: 'Boolean', parent: '' },
          { id: 'message', key: 'message', value: 'Updated', valueType: 'String', parent: '' }
        ],
        code: '',
        doc: '',
        unit: '',
        protected: true,
        authorizedRoles: [],
        imports: ``,
        type: 'default',
        group: 'profile'
      }
      
      
]