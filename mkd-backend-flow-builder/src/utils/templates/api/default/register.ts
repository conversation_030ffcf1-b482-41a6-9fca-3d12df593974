export const register = [
    {
        id: '_registerLambda',
        name: 'Register Lambda',
        method: 'POST',
        description: '',
        route: '/v2/api/lambda/register',
        inputs: [
          { name: 'email', type: 'body', rules: 'required', dataType: 'String' },
          { name: 'role', type: 'body', rules: 'required', dataType: 'String' },
          { name: 'verify', type: 'body', rules: '', dataType: 'Boolean' },
          { name: 'is_refresh', type: 'body', rules: '', dataType: 'Boolean' },
          { name: 'password', type: 'body', rules: 'required', dataType: 'String' },
          { name: 'first_name', type: 'body', rules: '', dataType: 'String' },
          { name: 'last_name', type: 'body', rules: '', dataType: 'String' },
          { name: 'photo', type: 'body', rules: '', dataType: 'String' },
          { name: 'phone', type: 'body', rules: '', dataType: 'String' }
        ],
        response: [
          { id: 'error', key: 'error', value: 'false', valueType: 'Boolean', parent: '' },
          { id: 'role', key: 'role', value: 'req.body.role', valueType: 'String', parent: '' },
          { id: 'token', key: 'token', value: '', valueType: 'String', parent: '' },
          { id: 'refresh_token', key: 'refresh_token', value: 'refreshToken', valueType: 'String', parent: '' },
          { id: 'expire_at', key: 'expire_at', value: '', valueType: 'Number', parent: '' },
          { id: 'user_id', key: 'user_id', value: 'result', valueType: 'String', parent: '' }
        ],
        code: '',
        doc: '',
        unit: '',
        protected: true,
        authorizedRoles: [],
        imports: `const TokenMiddleware = require("../../../middleware/TokenMiddleware");`,
        type: 'default',
        group: 'register'
      }
      
]