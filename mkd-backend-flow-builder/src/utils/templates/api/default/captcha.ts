export const captcha = [
    {
        id: "_captchaTest",
        name: "Captcha Test",
        description: "Endpoint for generating a captcha test",
        method: "GET",
        route: "/v2/api/lambda/test/:width?/:height?/",
        inputs: [
          { name: "width", type: "path", rules: "", dataType: "Integer" },
          { name: "height", type: "path", rules: "", dataType: "Integer" }
        ],
        response: [
          {
            id: "error",
            key: "error",
            value: "false",
            valueType: "Boolean",
            parent: ""
          },
          {
            id: "model",
            key: "model",
            value: "<img class=\"generated-captcha\" src=\"{image}\">",
            valueType: "String",
            parent: ""
          }
        ],
        code: "",
        doc: "",
        unit: "",
        protected: false,
        authorizedRoles: [],
        imports: "",
        type: "default",
        group: "captcha"
      },
      {
        id: "_captchaGenerate",
        name: "Captcha Generate",
        description: "Endpoint for generating a captcha",
        method: "GET",
        route: "/v2/api/lambda/captcha/:width?/:height?/",
        inputs: [
          { name: "width", type: "path", rules: "", dataType: "Integer" },
          { name: "height", type: "path", rules: "", dataType: "Integer" }
        ],
        response: [
          {
            id: "error",
            key: "error",
            value: "false",
            valueType: "Boolean",
            parent: ""
          },
          {
            id: "model",
            key: "model",
            value: "",
            valueType: "Object",
            parent: ""
          },
          {
            id: "image",
            key: "image",
            value: "",
            valueType: "String",
            parent: "model"
          },
          {
            id: "text",
            key: "text",
            value: "",
            valueType: "String",
            parent: "model"
          }
        ],
        code: "",
        doc: "",
        unit: "",
        protected: false,
        authorizedRoles: [],
        imports: "",
        type: "default",
        group: "captcha"
      },
      {
        id: "_googleCaptchaVerify",
        name: "Google Captcha Verify",
        description: "Endpoint for verifying Google reCAPTCHA token",
        method: "POST",
        route: "/v2/api/lambda/google-captcha/",
        inputs: [
          { name: "formData", type: "body", rules: "", dataType: "Object" },
          { name: "captchaToken", type: "body", rules: "", dataType: "String" }
        ],
        response: [
          {
            id: "error",
            key: "error",
            value: "false",
            valueType: "Boolean",
            parent: ""
          },
          {
            id: "message",
            key: "message",
            value: "",
            valueType: "String",
            parent: ""
          }
        ],
        code: "",
        doc: "",
        unit: "",
        protected: false,
        authorizedRoles: [],
        imports: "",
        type: "default",
        group: "captcha"
      }
      
      
      
]