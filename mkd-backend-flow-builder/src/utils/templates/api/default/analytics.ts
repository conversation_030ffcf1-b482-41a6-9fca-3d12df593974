export const analytics = [
    {
        id: '_analyticsLog',
        name: 'Analytics Log',
        description: 'Endpoint for logging analytics data',
        method: 'POST',
        route: '/v2/api/lambda/analytics/',
        inputs: [
            { name: 'user_id', type: 'body', rules: 'required', dataType: 'String' },
            { name: 'session_id', type: 'body', rules: '', dataType: 'String' },
            { name: 'status', type: 'body', rules: 'required', dataType: 'String' },
            { name: 'user_agent', type: 'body', rules: '', dataType: 'String' },
            { name: 'application', type: 'body', rules: '', dataType: 'String' },
            { name: 'document', type: 'body', rules: '', dataType: 'String' },
            { name: 'url', type: 'body', rules: '', dataType: 'Array' },
            { name: 'link_clicks', type: 'body', rules: '', dataType: 'Number' },
            { name: 'clicked_buttons', type: 'body', rules: '', dataType: 'Array' },
            { name: 'client_ip', type: 'body', rules: '', dataType: 'String' },
            { name: 'events', type: 'body', rules: '', dataType: 'Array' },
            { name: 'total_time', type: 'body', rules: '', dataType: 'Number' },
            { name: 'data', type: 'body', rules: '', dataType: 'Object' }
        ],
        response: [
            { id: 'error', key: 'error', value: 'false', valueType: 'Boolean', parent: '' },
            { id: 'message', key: 'message', value: 'Added successfully', valueType: 'String', parent: '' }
        ],
        code: ``,
        doc: '',
        unit: '',
        protected: false,
        authorizedRoles: [],
        imports: ``,
        type: 'default',
        group: 'analytics'
    },
    {
        id: '_analyticsEndpoint',
        name: 'Get Analytics',
        description: 'Endpoint for getting analytics data',
        method: 'GET',
        route: '/v2/api/lambda/analytics/data',
        inputs: [],
        response: [
            { id: 'error', key: 'error', value: 'false', valueType: 'Boolean', parent: '' },
            { id: 'model', key: 'model', value: '', valueType: 'Object', parent: '' },
            { id: 'user_analytics', key: 'user_analytics', value: '', valueType: 'Object', parent: 'model' },
            { id: 'guest_analytics', key: 'guest_analytics', value: '', valueType: 'Object', parent: 'model' },
            { id: 'click_analytics', key: 'click_analytics', value: '', valueType: 'Object', parent: 'model' }
        ],
        code: ``,
        doc: '',
        unit: '',
        protected: false,
        authorizedRoles: [],
        imports: ``,
        type: 'default',
        group: 'analytics'
    },
    {
        id: '_heatmapAnalytics',
        name: 'Log Heatmap Analytics',
        description: 'Endpoint for logging heatmap data',
        method: 'POST',
        route: '/v2/api/lambda/heatmap',
        inputs: [
            { name: 'user_id', type: 'body', rules: 'required', dataType: 'String' },
            { name: 'session_id', type: 'body', rules: '', dataType: 'String' },
            { name: 'user_agent', type: 'body', rules: '', dataType: 'String' },
            { name: 'scroll_position', type: 'body', rules: 'required', dataType: 'Object' },
            { name: 'coordinates', type: 'body', rules: 'required', dataType: 'Object' },
            { name: 'status', type: 'body', rules: '', dataType: 'String' },
            { name: 'client_ip', type: 'body', rules: '', dataType: 'String' },
            { name: 'screen_size', type: 'body', rules: '', dataType: 'Number' },
            { name: 'screen_width', type: 'body', rules: '', dataType: 'Number' },
            { name: 'page', type: 'body', rules: '', dataType: 'String' },
            { name: 'screen_height', type: 'body', rules: '', dataType: 'Number' },
            { name: 'snapshot', type: 'body', rules: '', dataType: 'String' },
            { name: 'total_time', type: 'body', rules: '', dataType: 'Number' },
            { name: 'data', type: 'body', rules: '', dataType: 'Object' }
        ],
        response: [
            { id: 'error', key: 'error', value: 'false', valueType: 'Boolean', parent: '' },
            { id: 'message', key: 'message', value: 'Added successfully', valueType: 'String', parent: '' }
        ],
        code: '',
        doc: '',
        unit: '',
        protected: false,
        authorizedRoles: [],
        imports: '',
        type: 'default',
        group: 'analytics'
    },
    {
        id: "_heatmapDataEndpoint",
        name: "Get Heatmap Data ",
        description: "Endpoint for retrieving heatmap data",
        method: "GET",
        route: "/v2/api/lambda/heatmap/data",
        inputs: [
            {
                name: "custom_date",
                type: "query",
                rules: "",
                dataType: "String"
            }
        ],
        response: [
            {
                id: "error",
                key: "error",
                value: "false",
                valueType: "Boolean",
                parent: ""
            },
            {
                id: "model",
                key: "model",
                value: "false",
                valueType: "Object",
                parent: ""
            },
            {
                id: "user_analytics",
                key: "user_analytics",
                value: "",
                valueType: "Object",
                parent: "model"
            },
            {
                id: "guest_analytics",
                key: "guest_analytics",
                value: "",
                valueType: "Object",
                parent: "model"
            },
            {
                id: "heat_map_data",
                key: "heat_map_data",
                value: "",
                valueType: "Array",
                parent: "model"
            }
        ],
        code: "",
        doc: "",
        unit: "",
        protected: false,
        authorizedRoles: [],
        imports: "",
        type: "default",
        group: "analytics"
    },
    {
        id: "_userSessionsDataEndpoint",
        name: "User Sessions Data",
        description: "Endpoint for retrieving user session data",
        method: "GET",
        route: "/v2/api/lambda/user-sessions/data",
        inputs: [
          {
            name: "custom_date",
            type: "query",
            rules: "",
            dataType: "String"
          }
        ],
        response: [
          {
            id: "error",
            key: "error",
            value: "false",
            valueType: "Boolean",
            parent: ""
          },
          {
            id: "model",
            key: "model",
            value: "false",
            valueType: "Object",
            parent: ""
          },
          {
            id: "user_analytics",
            key: "user_analytics",
            value: "",
            valueType: "Object",
            parent: "model"
          },
          {
            id: "guest_analytics",
            key: "guest_analytics",
            value: "",
            valueType: "Object",
            parent: "model"
          },
          {
            id: "heat_map_data",
            key: "heat_map_data",
            value: "",
            valueType: "Array",
            parent: "model"
          }
        ],
        code: "",
        doc: "",
        unit: "",
        protected: false,
        authorizedRoles: [],
        imports: "",
        type: "default",
        group: "analytics"
      },
      {
        id: "_userSessionsAnalytics",
        name: "Create User Sessions Analytics",
        description: "Endpoint for creating user sessions analytics",
        method: "POST",
        route: "/v2/api/lambda/analytics/user-sessions/",
        inputs: [
          {
            name: "user_id",
            type: "body",
            rules: "",
            dataType: "Integer"
          },
          {
            name: "session_id",
            type: "body",
            rules: "",
            dataType: "Integer"
          },
          {
            name: "status",
            type: "body",
            rules: "",
            dataType: "String"
          },
          {
            name: "events",
            type: "body",
            rules: "",
            dataType: "Array"
          },
          {
            name: "screen_width",
            type: "body",
            rules: "",
            dataType: "Integer"
          },
          {
            name: "screen_height",
            type: "body",
            rules: "",
            dataType: "Integer"
          },
          {
            name: "screen_size",
            type: "body",
            rules: "",
            dataType: "String"
          },
          {
            name: "start_time",
            type: "body",
            rules: "",
            dataType: "String"
          },
          {
            name: "end_time",
            type: "body",
            rules: "",
            dataType: "String"
          },
          {
            name: "html_copy",
            type: "body",
            rules: "",
            dataType: "String"
          }
        ],
        response: [
          {
            id: "error",
            key: "error",
            value: "false",
            valueType: "Boolean",
            parent: ""
          },
          {
            id: "message",
            key: "message",
            value: "User session created successfully",
            valueType: "String",
            parent: ""
          }
        ],
        code: "",
        doc: "",
        unit: "",
        protected: false,
        authorizedRoles: [],
        imports: "",
        type: "default",
        group: "analytics"
      }
      
      



]