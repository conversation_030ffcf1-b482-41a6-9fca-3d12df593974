export const check = [
    {
        id: '_checkLambda',
        name: 'Lambda Check',
        description: '',
        method: 'POST',
        route: `/v2/api/lambda/check`,
        inputs: [{ name: 'role', type: 'body', rules: 'required', dataType: "String" }],
        response: [
            { id: "error", key: 'error', value: "false", valueType: 'Boolean', parent: '' },
            { id: "message", key: 'message', value: 'OK', valueType: 'String', parent: '' }
        ],
        code: ``,
        doc: '',
        unit: '',
        protected: true,
        authorizedRoles: [],
        imports: ``,
        type: 'default',
        group: 'check',
    },
]