export const blog = [
    {
        id: "_blogAll",
        name: "Blog All",
        description: "Endpoint for retrieving all blog posts",
        method: "GET",
        route: "/v2/api/lambda/blog/all",
        inputs: [
          {
            name: "limit",
            type: "query",
            rules: "",
            dataType: "Integer"
          },
          {
            name: "offset",
            type: "query",
            rules: "",
            dataType: "Integer"
          }
        ],
        response: [
          {
            id: "error",
            key: "error",
            value: "false",
            valueType: "Boolean",
            parent: ""
          },
          {
            id: "data",
            key: "data",
            value: "",
            valueType: "Array",
            parent: ""
          },
          {
            id: "limit",
            key: "limit",
            value: "",
            valueType: "Integer",
            parent: ""
          },
          {
            id: "offset",
            key: "offset",
            value: "",
            valueType: "Integer",
            parent: ""
          },
          {
            id: "count",
            key: "count",
            value: "",
            valueType: "Integer",
            parent: ""
          }
        ],
        code: "",
        doc: "",
        unit: "",
        protected: false,
        authorizedRoles: [],
        imports: "",
        type: "default",
        group: "blog"
      }, 
      {
        id: "_blogSimilar",
        name: "Blog Similar",
        description: "Endpoint for retrieving similar blog posts",
        method: "GET",
        route: "/v2/api/lambda/blog/similar/:id",
        inputs: [
          {
            name: "top",
            type: "query",
            rules: "",
            dataType: "Integer"
          }
        ],
        response: [
          {
            id: "error",
            key: "error",
            value: "false",
            valueType: "Boolean",
            parent: ""
          },
          {
            id: "data",
            key: "data",
            value: "",
            valueType: "Array",
            parent: ""
          }
        ],
        code: "",
        doc: "",
        unit: "",
        protected: false,
        authorizedRoles: [],
        imports: "",
        type: "default",
        group: "blog"
      },
      {
        id: "_blogFilter",
        name: "Blog Filter",
        description: "Endpoint for filtering blog posts",
        method: "GET",
        route: "/v2/api/lambda/blog/filter",
        inputs: [
          {
            name: "categories",
            type: "query",
            rules: "",
            dataType: "Array"
          },
          {
            name: "tags",
            type: "query",
            rules: "",
            dataType: "Array"
          },
          {
            name: "rule",
            type: "query",
            rules: "",
            dataType: "String"
          },
          {
            name: "search",
            type: "query",
            rules: "",
            dataType: "String"
          },
          {
            name: "limit",
            type: "query",
            rules: "",
            dataType: "Integer"
          },
          {
            name: "page",
            type: "query",
            rules: "",
            dataType: "Integer"
          }
        ],
        response: [
          {
            id: "error",
            key: "error",
            value: "false",
            valueType: "Boolean",
            parent: ""
          },
          {
            id: "data",
            key: "data",
            value: "",
            valueType: "Array",
            parent: ""
          }
        ],
        code: "",
        doc: "",
        unit: "",
        protected: false,
        authorizedRoles: [],
        imports: "",
        type: "default",
        group: "blog"
      },
      {
        id: "_blogCreate",
        name: "Blog Create",
        description: "Endpoint for creating a new blog post",
        method: "POST",
        route: "/v2/api/lambda/blog/create",
        inputs: [
          { name: "title", type: "body", rules: "", dataType: "String" },
          { name: "body", type: "body", rules: "", dataType: "String" },
          { name: "meta", type: "body", rules: "", dataType: "Object" },
          { name: "tags", type: "body", rules: "", dataType: "Array" },
          { name: "categories", type: "body", rules: "", dataType: "Array" },
          { name: "content", type: "body", rules: "", dataType: "String" },
          { name: "description", type: "body", rules: "", dataType: "String" },
          { name: "thumbnail", type: "body", rules: "", dataType: "String" }
        ],
        response: [
          { id: "error", key: "error", value: "false", valueType: "Boolean", parent: "" },
          { id: "data", key: "data", value: "Blog Created.", valueType: "String", parent: "" }
        ],
        code: "",
        doc: "",
        unit: "",
        protected: false,
        authorizedRoles: [],
        imports: "",
        type: "default",
        group: "blog"
      },
      {
        id: "_blogEdit",
        name: "Blog Edit",
        description: "Endpoint for editing an existing blog post",
        method: "POST",
        route: "/v2/api/lambda/blog/edit/:id",
        inputs: [
          { name: "id", type: "path", rules: "", dataType: "String" },
          { name: "title", type: "body", rules: "", dataType: "String" },
          { name: "content", type: "body", rules: "", dataType: "String" },
          { name: "description", type: "body", rules: "", dataType: "String" },
          { name: "meta", type: "body", rules: "", dataType: "Object" },
          { name: "tags", type: "body", rules: "", dataType: "Array" },
          { name: "categories", type: "body", rules: "", dataType: "Array" },
          { name: "thumbnail", type: "body", rules: "", dataType: "String" }
        ],
        response: [
          { id: "error", key: "error", value: "false", valueType: "Boolean", parent: "" },
          { id: "data", key: "data", value: "Blog Edited.", valueType: "String", parent: "" }
        ],
        code: "",
        doc: "",
        unit: "",
        protected: false,
        authorizedRoles: [],
        imports: "",
        type: "default",
        group: "blog"
      },
      {
        id: "_blogDelete",
        name: "Blog Delete",
        description: "Endpoint for deleting an existing blog post",
        method: "DELETE",
        route: "/v2/api/lambda/blog/delete/:id",
        inputs: [
          { name: "id", type: "path", rules: "", dataType: "String" }
        ],
        response: [
          { id: "error", key: "error", value: "false", valueType: "Boolean", parent: "" },
          { id: "data", key: "data", value: "Blog Deleted.", valueType: "String", parent: "" }
        ],
        code: "",
        doc: "",
        unit: "",
        protected: false,
        authorizedRoles: [],
        imports: "",
        type: "default",
        group: "blog"
      },
      {
        id: "_blogSingle",
        name: "Blog Single",
        description: "Endpoint for retrieving a single blog post",
        method: "GET",
        route: "/v2/api/lambda/blog/single/:id",
        inputs: [
          { name: "id", type: "path", rules: "", dataType: "String" }
        ],
        response: [
          { id: "error", key: "error", value: "false", valueType: "Boolean", parent: "" },
          { id: "data", key: "data", value: "", valueType: "Object", parent: "" },
          { id: "data_id", key: "data__id", value: "", valueType: "String", parent: "data" },
          { id: "data_title", key: "data__title", value: "", valueType: "String", parent: "data" },
          { id: "data_description", key: "data__description", value: "", valueType: "String", parent: "data" },
          { id: "data_content", key: "data__content", value: "", valueType: "String", parent: "data" },
          { id: "data_thumbnail", key: "data__thumbnail", value: "", valueType: "String", parent: "data" },
          { id: "data_author", key: "data__author", value: "", valueType: "String", parent: "data" },
          { id: "data_meta", key: "data__meta", value: "", valueType: "String", parent: "data" },
          { id: "data_create_at", key: "data__create_at", value: "", valueType: "String", parent: "data" },
          { id: "data_update_at", key: "data__update_at", value: "", valueType: "String", parent: "data" },
          { id: "data_tags", key: "data__tags", value: "", valueType: "Array", parent: "data" },
          { id: "data_tags_name", key: "data__tags_name", value: "", valueType: "String", parent: "data_tags" },
          { id: "data_tags_id", key: "data_tags__id", value: "", valueType: "String", parent: "data_tags" },
          { id: "data_categories", key: "data__categories", value: "", valueType: "Array", parent: "data" },
          { id: "data_categories_name", key: "data_categories__name", value: "", valueType: "String", parent: "data_categories" },
          { id: "data_categories_id", key: "data_categories__id", value: "", valueType: "String", parent: "data_categories" },
          { id: "data_views", key: "data__views", value: 0, valueType: "Number", parent: "data" }
        ],
        code: "",
        doc: "",
        unit: "",
        protected: false,
        authorizedRoles: [],
        imports: "",
        type: "default",
        group: "blog"
      },
      {
        id: "_blogTags",
        name: "Blog Tags",
        description: "Endpoint for retrieving a blog tag or creating if it does not exist ",
        method: "POST",
        route: "/v2/api/lambda/blog/tags",
        inputs: [
          { name: "name", type: "body", rules: "", dataType: "String" }
        ],
        response: [
          { id: "error", key: "error", value: "false", valueType: "Boolean", parent: "" },
          { id: "tag_id", key: "tag_id", value: "", valueType: "String", parent: "" }
        ],
        code: "",
        doc: "",
        unit: "",
        protected: false,
        authorizedRoles: [],
        imports: "",
        type: "default",
        group: "blog"
      },
      {
        id: "_blogTagsUpdate",
        name: "Blog Tags Update",
        description: "Endpoint for updating a blog tag by ID",
        method: "POST",
        route: "/v2/api/lambda/blog/tags/:id",
        inputs: [
          { name: "name", type: "body", rules: "", dataType: "String" }
        ],
        response: [
          { id: "error", key: "error", value: "false", valueType: "Boolean", parent: "" },
          { id: "message", key: "message", value: "Updated!", valueType: "String", parent: "" }
        ],
        code: "",
        doc: "",
        unit: "",
        protected: false,
        authorizedRoles: [],
        imports: "",
        type: "default",
        group: "blog"
      },
      {
        id: "_blogTagsRetrieve",
        name: "Blog Tags Retrieve",
        description: "Endpoint for retrieving blog tags",
        method: "GET",
        route: "/v2/api/lambda/blog/tags",
        inputs: [
          { name: "limit", type: "query", rules: "", dataType: "Integer" },
          { name: "page", type: "query", rules: "", dataType: "Integer" },
          { name: "name", type: "query", rules: "", dataType: "String" }
        ],
        response: [
          { id: "error", key: "error", value: false, valueType: "Boolean", parent: "" },
          { id: "data", key: "data", value: [], valueType: "Array", parent: "" },
          { id: "limit", key: "limit", value: 10, valueType: "Integer", parent: "" },
          { id: "page", key: "page", value: 1, valueType: "Integer", parent: "" },
          { id: "total", key: "total", value: 0, valueType: "Integer", parent: "" },
          { id: "num_pages", key: "num_pages", value: 0, valueType: "Integer", parent: "" }
        ],
        code: "",
        doc: "",
        unit: "",
        protected: false,
        authorizedRoles: [],
        imports: "",
        type: "default",
        group: "blog"
      },
      {
        id: "_blogTagsDelete",
        name: "Blog Tags Delete by ID",
        description: "Endpoint for deleting a blog tag by ID",
        method: "DELETE",
        route: "/v2/api/lambda/blog/tags/:id",
        inputs: [
          { name: "id", type: "path", rules: "", dataType: "String" }
        ],
        response: [
          { id: "error", key: "error", value: false, valueType: "Boolean", parent: "" },
          { id: "message", key: "message", value: "Tag Deleted", valueType: "String", parent: "" }
        ],
        code: "",
        doc: "",
        unit: "",
        protected: true,
        authorizedRoles: ["admin"],
        imports: "",
        type: "default",
        group: "blog"
      },
      {
        id: "_blogCategoryCreate",
        name: "Create Blog Category",
        description: "Endpoint for creating a new blog category",
        method: "POST",
        route: "/v2/api/lambda/blog/category",
        inputs: [
          { name: "name", type: "body", rules: "", dataType: "String" },
          { name: "parent_id", type: "body", rules: "", dataType: "String" }
        ],
        response: [
          { id: "error", key: "error", value: false, valueType: "Boolean", parent: "" },
          { id: "data", key: "data", value: "", valueType: "String", parent: "" }
        ],
        code: "",
        doc: "",
        unit: "",
        protected: false,
        authorizedRoles: [],
        imports: "",
        type: "default",
        group: "blog"
      },
      {
        id: "_blogCategoryUpdate",
        name: "Update Blog Category",
        description: "Endpoint for updating a blog category",
        method: "POST",
        route: "/v2/api/lambda/blog/category/:id",
        inputs: [
          { name: "name", type: "body", rules: "", dataType: "String" },
          { name: "parent_id", type: "body", rules: "", dataType: "String" }
        ],
        response: [
          { id: "error", key: "error", value: false, valueType: "Boolean", parent: "" },
          { id: "data", key: "data", value: "Updated", valueType: "String", parent: "" }
        ],
        code: "",
        doc: "",
        unit: "",
        protected: false,
        authorizedRoles: [],
        imports: "",
        type: "default",
        group: "blog"
      },
      {
        id: "_blogCategoryGet",
        name: "Get Blog Category",
        description: "Endpoint for retrieving blog categories",
        method: "GET",
        route: "/v2/api/lambda/blog/category",
        inputs: [
          { name: "limit", type: "query", rules: "", dataType: "Integer" },
          { name: "page", type: "query", rules: "", dataType: "Integer" },
          { name: "name", type: "query", rules: "", dataType: "String" }
        ],
        response: [
          { id: "error", key: "error", value: "false", valueType: "Boolean", parent: "" },
          { id: "data", key: "data", value: "", valueType: "Array", parent: "" },
          { id: "limit", key: "limit", value: "", valueType: "Integer", parent: "" },
          { id: "page", key: "page", value: "", valueType: "Integer", parent: "" },
          { id: "total", key: "total", value: "", valueType: "Integer", parent: "" },
          { id: "num_pages", key: "num_pages", value: "", valueType: "String", parent: "" }
        ],
        code: "",
        doc: "",
        unit: "",
        protected: false,
        authorizedRoles: [],
        imports: "",
        type: "default",
        group: "blog"
      },
      {
        id: "_blogSubcategoryGet",
        name: "Get Blog Subcategory",
        description: "Endpoint for retrieving subcategories of a blog category",
        method: "GET",
        route: "/v2/api/lambda/blog/subcategory/:id",
        inputs: [
          { name: "id", type: "path", rules: "", dataType: "String" }
        ],
        response: [
          { id: "error", key: "error", value: "false", valueType: "Boolean", parent: "" },
          { id: "data", key: "data", value: "", valueType: "Array", parent: "" }
        ],
        code: "",
        doc: "",
        unit: "",
        protected: false,
        authorizedRoles: [],
        imports: "",
        type: "default",
        group: "blog"
      },
      {
        id: "_blogDeleteCategory",
        name: "Delete Blog Category",
        description: "Endpoint for deleting a blog category",
        method: "DELETE",
        route: "/v2/api/lambda/blog/category/:id",
        inputs: [
          { name: "id", type: "path", rules: "", dataType: "String" }
        ],
        response: [
          { id: "error", key: "error", value: "false", valueType: "Boolean", parent: "" },
          { id: "data", key: "data", value: "Deleted", valueType: "String", parent: "" }
        ],
        code: "",
        doc: "",
        unit: "",
        protected: false,
        authorizedRoles: [],
        imports: "",
        type: "default",
        group: "blog"
      }         
      
      
];