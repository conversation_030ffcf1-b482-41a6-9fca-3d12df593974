export const upload = [
    {
        id: '_uploadImageLocalDefault',
        name: 'Upload Image Local Default',
        method: 'POST',
        description: '',
        route: '/v2/api/lambda/upload',
        inputs: [
          { name: 'file', type: 'multipart', rules: '', dataType: 'String' },
        ],
        response: [
          { id: 'id', key: 'id', value: '', valueType: 'String', parent: '' },
          { id: 'url', key: 'url', value: '', valueType: 'String', parent: '' }
        ],
        code: '',
        doc: '',
        unit: '',
        protected: true,
        authorizedRoles: [],
        imports: `
          const imageMiddlewares = require("../../../middleware/imageMiddlewares");
          const sizeOf = require("image-size");
          const fs = require("fs");
          const logService = require("../../../services/logService");
          const { sqlDateFormat, sqlDateTimeFormat } = require("../../../utils/dateUtils");
        `,
        type: 'default',
        group: 'upload'
      },
      {
        id: '_uploadImageS3',
        name: 'UploadImageS3',
        method: 'POST',
        description: '',
        route: '/v2/api/lambda/s3/upload',
        inputs: [
          { name: 'file', type: 'multipart', rules: '', dataType: 'String' },
        ],
        response: [
          { id: 'id', key: 'id', value: '', valueType: 'String', parent: '' },
          { id: 'url', key: 'url', value: '', valueType: 'String', parent: '' }
        ],
        code: '',
        doc: '',
        unit: '',
        protected: true,
        authorizedRoles: [],
        imports: ``,
        type: 'default',
        group: 'upload'
      }
      
      
]