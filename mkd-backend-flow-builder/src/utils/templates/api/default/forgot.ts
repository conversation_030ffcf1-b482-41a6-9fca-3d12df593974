export const forgot = [
    {
        id: '_forgotPassword',
        name: 'Forgot Password',
        method: 'POST',
        description: '',
        route: '/v2/api/lambda/forgot',
        inputs: [
            { id: 'email', name: 'email', type: 'body', rules: 'required', dataType: 'String', value: '' },
            { id: 'role', name: 'role', type: 'body', rules: 'required', dataType: 'String', value: '' },
        ],
        response: [ 
            { id: "error", key: 'error', value: "false", valueType: 'Boolean', parent: '' },
            { id: "message", key: 'message', value: '', valueType: 'String', parent: '' },
        ],
        code: '',
        doc: '',
        unit: '',
        protected: false,
        authorizedRoles: [],
        imports: '',
        type: 'default',
        group: 'forgot'
      },
    {
        id: '_forgotPasswordMobile',
        name: 'Forgot Password Mobile',
        method: 'POSt',
        description: '',
        route: '/v2/api/lambda/mobile/forgot',
        inputs: [
            { id: 'email', name: 'email', type: 'body', rules: 'required', dataType: 'String', value: '' },
        ],
        response: [ 
            { id: "error", key: 'error', value: "false", valueType: 'Boolean', parent: '' },
            { id: "message", key: 'message', value: '', valueType: 'String', parent: '' },
        ],
        code: '',
        doc: '',
        unit: '',
        protected: false,
        authorizedRoles: [],
        imports: '',
        type: 'default',
        group: 'forgot'
      },
      
      
      
      
]
