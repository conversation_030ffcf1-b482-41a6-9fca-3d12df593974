// export const stripe = [
//   {
//     id: "_getStripeData",
//     name: "Get Stripe Data",
//     method: "POST",
//     description: "Default function for generating stripe data for checkout.",
//     route: "/v2/api/lambda/stripe/mobile/intent/",
//     inputs: [
//       {
//         id: "user_id",
//         name: "user_id",
//         type: "body",
//         rules: "required",
//         dataType: "String",
//         value: "",
//       },
//       {
//         id: "amount",
//         name: "amount",
//         type: "body",
//         rules: "required",
//         dataType: "String",
//         value: "",
//       },
//       {
//         id: "currency",
//         name: "currency",
//         type: "body",
//         rules: "optional",
//         dataType: "String",
//         value: "",
//       },
//     ],
//     response: [
//       {
//         id: "error",
//         key: "error",
//         value: "false",
//         valueType: "Boolean",
//         parent: "",
//       },
//       {
//         id: "message",
//         key: "message",
//         value: "OK",
//         valueType: "String",
//         parent: "",
//       },
//       {
//         id: "paymentIntent",
//         key: "paymentIntent",
//         value: "result[0].paymentIntent ?? ''",
//         valueType: "String",
//         parent: "",
//       },
//       {
//         id: "ephemeralKeyRaw",
//         key: "ephemeralKeyRaw",
//         value: "result[0].ephemeralKeyRaw ?? ''",
//         valueType: "String",
//         parent: "",
//       },
//       {
//         id: "customer",
//         key: "customer",
//         value: "result[0].customer ?? ''",
//         valueType: "String",
//         parent: "",
//       },
//       {
//         id: "publishableKey",
//         key: "publishableKey",
//         value: "result[0].publishableKey ?? ''",
//         valueType: "String",
//         parent: "",
//       },
//     ],
//     code: "",
//     doc: "",
//     unit: "",
//     protected: false,
//     authorizedRoles: [],
//     imports: "",
//     type: "default",
//     group: "stripe",
//   },
// ];
