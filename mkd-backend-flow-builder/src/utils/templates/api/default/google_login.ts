export const google_login = [
    {
        id: '_googleCode',
        name: 'Google Code',
        method: 'GET',
        description: '',
        route: '/v2/api/lambda/google/code',
        inputs: [
          { id: 'state', name: 'state', value: '', datType: 'String', rules: "optional", type: 'query' }
        ],
        response: [],
        code: '',
        doc: '',
        unit: '',
        protected: false,
        authorizedRoles: [],
        imports: '',
        type: 'default',
        group: 'google'
      },
      {
        id: '_googleCodeMobile',
        name: 'Google Code Mobile',
        method: 'GET',
        description: 'Default function for the mobile app Google login endpoint with code exchange.',
        route: '/v2/api/lambda/google/code/mobile',
        inputs: [
          { id: 'role', name: 'role', type: 'query', rules: 'optional', dataType: 'String', value: 'user' },
          { id: 'is_refresh', name: 'is_refresh', type: 'query', rules: 'optional', dataType: 'Boolean', value: 'false' },
          { id: 'code', name: 'code', type: 'query', rules: 'required', dataType: 'String', value: '' }
        ],
        response: [ 
            { id: "error", key: 'error', value: "false", valueType: 'Boolean', parent: '' },
            { id: "message", key: 'message', value: '', valueType: 'String', parent: '' },
            { id: "role", key: 'role', value: '', valueType: 'String', parent: '' },
            { id: "token", key: 'token', value: '', valueType: 'String', parent: '' },
            { id: "expire_at", key: 'expire_at', value: '', valueType: 'String', parent: '' },
            { id: "user_id", key: 'user_id', value: '', valueType: 'Number', parent: '' },
            { id: "refrsh_token", key: 'refresh_token', value: '', valueType: 'String', parent: '' }
        ],
        code: '',
        doc: '',
        unit: '',
        protected: false,
        authorizedRoles: [],
        imports: '',
        type: 'default',
        group: 'google_login'
      },
      {
        id: '_googleLogin',
        name: 'Google Login',
        method: 'GET',
        description: 'Default function for generating Google login URL for user registration.',
        route: '/v2/api/lambda/google/login',
        inputs: [
          { id: 'role', name: 'role', type: 'query', rules: 'required', dataType: 'String', value: '' },
          { id: 'company_id', name: 'company_id', type: 'query', rules: 'optional', dataType: 'String', value: '' },
          { id: 'is_refresh', name: 'is_refresh', type: 'query', rules: 'optional', dataType: 'Boolean', value: '' }
        ],
        response: [],
        code: '',
        doc: '',
        unit: '',
        protected: false,
        authorizedRoles: [],
        imports: '',
        type: 'default',
        group: 'google_login'
      }
      
      
      
]
