export const preference = [
    {
        id: '_preferenceFetch',
        name: 'Preference Fetch',
        method: 'GET',
        description: '',
        route: '/v2/api/lambda/preference',
        inputs: [],
        response: [
            { id: 'user_id', key: 'user_id', value: '', valueType: 'Number', parent: '' },
            { id: 'fcm_token', key: 'fcm_token', value: '', valueType: 'String', parent: '' },
            { id: 'first_name', key: 'first_name', value: '', valueType: 'String', parent: '' },
            { id: 'last_name', key: 'last_name', value: '', valueType: 'String', parent: '' },
            { id: 'email', key: 'email', value: '', valueType: 'String', parent: '' },
            { id: 'role', key: 'role', value: '', valueType: 'String', parent: '' },
            { id: 'phone', key: 'phone', value: '', valueType: 'String', parent: '' },
            { id: 'photo', key: 'photo', value: '', valueType: 'String', parent: '' },
            { id: 'mapping', key: 'mapping', value: '', valueType: 'String', parent: '' },
        ],
        code: '',
        doc: '',
        unit: '',
        protected: true,
        authorizedRoles: [],
        imports: ``,
        type: 'default',
        group: 'preference'
      },
      {
        id: '_preferenceUpdate',
        name: 'Preference Update',
        method: 'POST',
        description: '',
        route: '/v2/api/lambda/preference',
        inputs: [
            { name: 'payload', type: 'body', rules: 'required', dataType: 'Object' },
        ],
        response: [
            { id: 'error', key: 'error', value: '', valueType: 'Boolean', parent: '' },
            { id: 'message', key: 'message', value: '', valueType: 'String', parent: '' },
        ],
        code: '',
        doc: '',
        unit: '',
        protected: true,
        authorizedRoles: [],
        imports: '',
        type: 'default',
        group: 'preference'
      },
      {
        id: '_treeSow',
        name: 'Get Sow Tree',
        method: 'GET',
        description: '',
        route: '/v4/api/records/sow',
        inputs: [
            { name: 'order', type: 'query', rules: 'required', dataType: 'String' },
            { name: 'page', type: 'query', rules: 'required', dataType: 'String' },
        ],
        response: [
            { id: 'error', key: 'error', value: '', valueType: 'Boolean', parent: '' },
            { id: 'message', key: 'message', value: '', valueType: 'String', parent: '' },
            { id: 'list', key: 'list', value: '', valueType: 'Array', parent: '' },
            { id: 'page', key: 'page', value: '', valueType: 'Number', parent: '' },
            { id: 'limit', key: 'limit', value: '', valueType: 'Number', parent: '' },
            { id: 'total', key: 'total', value: '', valueType: 'Number', parent: '' },
            { id: 'mapping', key: 'mapping', value: '', valueType: 'Object', parent: '' },
        ],
        code: '',
        doc: '',
        unit: '',
        protected: true,
        authorizedRoles: [],
        imports: '',
        type: 'default',
        group: 'preference'
      },
      {
        id: '_appAlertsList',
        name: 'App Alerts List',
        method: 'GET',
        description: '',
        route: '/v4/api/records/alerts',
        inputs: [
            { name: 'order', type: 'query', rules: 'required', dataType: 'String' },
            { name: 'page', type: 'query', rules: 'required', dataType: 'String' },
            { name: 'filter', type: 'query', rules: '', dataType: 'String' },
        ],
        response: [
            { id: 'error', key: 'error', value: '', valueType: 'Boolean', parent: '' },
            { id: 'message', key: 'message', value: '', valueType: 'String', parent: '' },
            { id: 'list', key: 'list', value: '', valueType: 'Array', parent: '' },
            { id: 'page', key: 'page', value: '', valueType: 'Number', parent: '' },
            { id: 'limit', key: 'limit', value: '', valueType: 'Number', parent: '' },
            { id: 'total', key: 'total', value: '', valueType: 'Number', parent: '' },
            { id: 'mapping', key: 'mapping', value: '', valueType: 'Object', parent: '' },
        ],
        code: '',
        doc: '',
        unit: '',
        protected: true,
        authorizedRoles: [],
        imports: '',
        type: 'default',
        group: 'preference'
      },
      {
        id: '_appAlertsUpdate',
        name: 'App Alerts Update',
        method: 'PUT',
        description: '',
        route: '/v4/api/records/alerts/:id',
        inputs: [
            { name: 'id', type: 'path', rules: 'required', dataType: 'String' },
            { name: 'is_read', type: 'body', rules: 'required', dataType: 'Int' },
        ],
        response: [
            { id: 'error', key: 'error', value: '', valueType: 'Boolean', parent: '' },
            { id: 'message', key: 'message', value: '', valueType: 'String', parent: '' },
            { id: 'list', key: 'list', value: '', valueType: 'Array', parent: '' },
            { id: 'page', key: 'page', value: '', valueType: 'Number', parent: '' },
            { id: 'limit', key: 'limit', value: '', valueType: 'Number', parent: '' },
            { id: 'total', key: 'total', value: '', valueType: 'Number', parent: '' },
            { id: 'mapping', key: 'mapping', value: '', valueType: 'Object', parent: '' },
        ],
        code: '',
        doc: '',
        unit: '',
        protected: true,
        authorizedRoles: [],
        imports: '',
        type: 'default',
        group: 'preference'
      }
      
      
];