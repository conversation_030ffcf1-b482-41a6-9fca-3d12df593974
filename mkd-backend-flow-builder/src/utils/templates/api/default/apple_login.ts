export const apple_login = [
  {
    id: "_appleLoginMobileEndpoint",
    name: "Apple Login Mobile Endpoint",
    description: "Endpoint for login via iOS app",
    method: "POST",
    route: "/v2/api/lambda/apple/login/mobile",
    inputs: [
      {
        name: "first_name",
        type: "body",
        rules: "",
        dataType: "String",
      },
      {
        name: "last_name",
        type: "body",
        rules: "",
        dataType: "String",
      },
      {
        name: "identityToken",
        type: "body",
        rules: "",
        dataType: "String",
      },
      {
        name: "apple_id",
        type: "body",
        rules: "",
        dataType: "String",
      },
      {
        name: "role",
        type: "body",
        rules: "",
        dataType: "String",
      },
    ],
    response: [
      {
        id: "message",
        key: "message",
        value: "",
        valueType: "String",
        parent: "",
      },
      {
        id: "error",
        key: "error",
        value: "false",
        valueType: "Boolean",
        parent: "",
      },
      {
        id: "role",
        key: "role",
        value: "",
        valueType: "String",
        parent: "",
      },
      {
        id: "access_token",
        key: "access_token",
        value: "",
        valueType: "String",
        parent: "",
      },
      {
        id: "refresh_token",
        key: "refresh_token",
        value: "",
        valueType: "String",
        parent: "",
      },
      {
        id: "expire_at",
        key: "expire_at",
        value: "",
        valueType: "String",
        parent: "",
      },
      {
        id: "user_id",
        key: "user_id",
        value: "",
        valueType: "String",
        parent: "",
      },
    ],
    code: "",
    doc: "",
    unit: "",
    protected: false,
    authorizedRoles: [],
    imports: "",
    type: "default",
    group: "apple_login",
  },
  {
    id: "_appleLogin",
    name: "Apple Login",
    description: "Endpoint for Apple login",
    method: "GET",
    route: "/v2/api/lambda/apple/login",
    inputs: [],
    response: [
      {
        id: "error",
        key: "error",
        value: "true",
        valueType: "Boolean",
        parent: "",
      },
      {
        id: "message",
        key: "message",
        value: "",
        valueType: "String",
        parent: "",
      },
    ],
    code: "",
    doc: "",
    unit: "",
    protected: false,
    authorizedRoles: [],
    imports: "",
    type: "default",
    group: "apple_login",
  },
  {
    id: "_appleAuthCode",
    name: "Apple Auth Code ",
    description: "Endpoint for handling Apple authorization code",
    method: "POST",
    route: "/v2/api/lambda/apple/code",
    inputs: [
      {
        id: "state",
        name: "state",
        type: "body",
        rules: "required",
        dataType: "String",
        value: "",
      },
      {
        id: "code",
        name: "code",
        type: "body",
        rules: "required",
        dataType: "String",
        value: "",
      },
    ],
    response: [
      {
        id: "error",
        key: "error",
        value: "true",
        valueType: "Boolean",
        parent: "",
      },
      {
        id: "message",
        key: "message",
        value: "",
        valueType: "String",
        parent: "",
      },
    ],
    code: "",
    doc: "",
    unit: "",
    protected: false,
    authorizedRoles: [],
    imports: "",
    type: "default",
    group: "apple_login",
  },
];
