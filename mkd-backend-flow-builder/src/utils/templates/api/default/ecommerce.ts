export const ecommerce = [
  {
    id: "_ecomProductDefault",
    name: "Retrieve Product Default",
    method: "POST",
    description:
      "Default function for retrieving e-commerce products with pagination.",
    route: "/v2/api/lambda/ecom/product/",
    inputs: [
      {
        id: "page",
        name: "page",
        type: "body",
        rules: "required",
        dataType: "Number",
        value: "",
      },
      {
        id: "limit",
        name: "limit",
        type: "body",
        rules: "required",
        dataType: "Number",
        value: "",
      },
      {
        id: "sortId",
        name: "sortId",
        type: "body",
        rules: "optional",
        dataType: "String",
        value: "",
      },
      {
        id: "direction",
        name: "direction",
        type: "body",
        rules: "optional",
        dataType: "String",
        value: "",
      },
    ],
    response: [
      {
        id: "error",
        key: "error",
        value: "false",
        valueType: "Boolean",
        parent: "",
      },
      {
        id: "message",
        key: "message",
        value: "",
        valueType: "String",
        parent: "",
      },
      { id: "page", key: "page", value: "", valueType: "Number", parent: "" },
      { id: "limit", key: "limit", value: "", valueType: "Number", parent: "" },
      { id: "total", key: "total", value: "", valueType: "Number", parent: "" },
      {
        id: "num_pages",
        key: "num_pages",
        value: "",
        valueType: "Number",
        parent: "",
      },
    ],
    code: "",
    doc: "",
    unit: "",
    protected: false,
    authorizedRoles: [],
    imports: "",
    type: "default",
    group: "ecom",
  },
  {
    id: "_ecomProductByIdDefault",
    name: "Ecom Product by ID Default",
    method: "GET",
    description:
      "Default function for retrieving a specific e-commerce product by ID or slug.",
    route: "/v2/api/lambda/ecom/product/:product_identifier",
    inputs: [
      {
        id: "product_identifier",
        name: "product_identifier",
        type: "params",
        rules: "required",
        dataType: "String",
        value: "",
      },
    ],
    response: [
      {
        id: "error",
        key: "error",
        value: "false",
        valueType: "Boolean",
        parent: "",
      },
      {
        id: "model",
        key: "message",
        value: "",
        valueType: "Object",
        parent: "",
      },
    ],
    code: "",
    doc: "",
    unit: "",
    protected: false,
    authorizedRoles: [],
    imports: "",
    type: "default",
    group: "ecom",
  },
  {
    id: "_ecomProductAddLambda",
    name: "Add Ecom Product Lambda",
    method: "POST",
    description: "Create Product.",
    route: "/v3/api/custom/lambda/ecom/product/add",
    inputs: [
      {
        id: "slug",
        name: "slug",
        type: "body",
        rules: "required",
        dataType: "String",
        value: "",
      },
      {
        id: "category_id",
        name: "category_id",
        type: "body",
        rules: "required",
        dataType: "String",
        value: "",
      },
      {
        id: "type",
        name: "type",
        type: "body",
        rules: "required",
        dataType: "String",
        value: "",
      },
      {
        id: "quantity",
        name: "quantity",
        type: "body",
        rules: "required",
        dataType: "String",
        value: "",
      },
      {
        id: "data",
        name: "data",
        type: "body",
        rules: "required",
        dataType: "String",
        value: "",
      },
      {
        id: "name",
        name: "name",
        type: "body",
        rules: "required",
        dataType: "String",
        value: "",
      },
      {
        id: "is_taxable",
        name: "is_taxable",
        type: "body",
        rules: "",
        dataType: "Boolean",
        value: "",
      },
      {
        id: "is_shipping",
        name: "is_shipping",
        type: "body",
        rules: "",
        dataType: "Boolean",
        value: "",
      },
      {
        id: "is_sticky",
        name: "is_sticky",
        type: "body",
        rules: "",
        dataType: "Boolean",
        value: "",
      },
      {
        id: "is_featured",
        name: "is_featured",
        type: "body",
        rules: "",
        dataType: "Boolean",
        value: "",
      },
      {
        id: "is_downloadable",
        name: "is_downloadable",
        type: "body",
        rules: "",
        dataType: "Boolean",
        value: "",
      },
      {
        id: "download_limit",
        name: "download_limit",
        type: "body",
        rules: "",
        dataType: "String",
        value: "",
      },
      {
        id: "is_backorder",
        name: "is_backorder",
        type: "body",
        rules: "",
        dataType: "Boolean",
        value: "",
      },
      {
        id: "sold_single",
        name: "sold_single",
        type: "body",
        rules: "",
        dataType: "Boolean",
        value: "",
      },
      {
        id: "manage_stock",
        name: "manage_stock",
        type: "body",
        rules: "",
        dataType: "Boolean",
        value: "",
      },
      {
        id: "thumbnail_image",
        name: "thumbnail_image",
        type: "body",
        rules: "",
        dataType: "String",
        value: "",
      },
      {
        id: "featured_image",
        name: "featured_image",
        type: "body",
        rules: "",
        dataType: "Boolean",
        value: "",
      },
      {
        id: "image",
        name: "image",
        type: "body",
        rules: "",
        dataType: "String",
        value: "",
      },
      {
        id: "sku",
        name: "sku",
        type: "body",
        rules: "",
        dataType: "String",
        value: "",
      },
      {
        id: "weight",
        name: "weight",
        type: "body",
        rules: "",
        dataType: "String",
        value: "",
      },
      {
        id: "height",
        name: "height",
        type: "body",
        rules: "",
        dataType: "String",
        value: "",
      },
      {
        id: "length",
        name: "length",
        type: "body",
        rules: "",
        dataType: "String",
        value: "",
      },
      {
        id: "weight_unit",
        name: "weight_unit",
        type: "body",
        rules: "",
        dataType: "String",
        value: "",
      },
      {
        id: "height_unit",
        name: "height_unit",
        type: "body",
        rules: "",
        dataType: "String",
        value: "",
      },
      {
        id: "length_unit",
        name: "length_unit",
        type: "body",
        rules: "",
        dataType: "String",
        value: "",
      },
      {
        id: "avg_review",
        name: "avg_review",
        type: "body",
        rules: "",
        dataType: "String",
        value: "",
      },
      {
        id: "sale_price",
        name: "sale_price",
        type: "body",
        rules: "",
        dataType: "String",
        value: "",
      },
      {
        id: "shipping_price",
        name: "shipping_price",
        type: "body",
        rules: "",
        dataType: "String",
        value: "",
      },
      {
        id: "regular_price",
        name: "regular_price",
        type: "body",
        rules: "",
        dataType: "String",
        value: "",
      },
      {
        id: "position",
        name: "position",
        type: "body",
        rules: "",
        dataType: "String",
        value: "",
      },
      {
        id: "download_expire_at",
        name: "download_expire_at",
        type: "body",
        rules: "",
        dataType: "String",
        value: "",
      },
      {
        id: "schedule_sale_at",
        name: "schedule_sale_at",
        type: "body",
        rules: "",
        dataType: "String",
        value: "",
      },
      {
        id: "schedule_sale_end",
        name: "schedule_sale_end",
        type: "body",
        rules: "",
        dataType: "String",
        value: "",
      },
      {
        id: "description",
        name: "description",
        type: "body",
        rules: "",
        dataType: "String",
        value: "",
      },
      {
        id: "is_virtual",
        name: "is_virtual",
        type: "body",
        rules: "",
        dataType: "Boolean",
        value: "",
      },
    ],
    response: [
      {
        id: "error",
        key: "error",
        value: "false",
        valueType: "Boolean",
        parent: "",
      },
      {
        id: "message",
        key: "message",
        value: "",
        valueType: "String",
        parent: "",
      },
    ],
    code: ``,
    doc: "",
    unit: "",
    protected: true,
    authorizedRoles: [],
    imports: "",
    type: "default",
    group: "ecom",
  },
  {
    id: "_ecomProductEditLambda",
    name: "Edit Ecom Product Lambda",
    method: "PUT",
    description: "Edit Product.",
    route: "/v2/api/lambda/ecom/product/:id",
    inputs: [
      {
        id: "id",
        name: "id",
        type: "path",
        rules: "required",
        dataType: "Number",
        value: "",
      },
      {
        id: "payload",
        name: "payload",
        type: "body",
        rules: "required",
        dataType: "Object",
        value: "",
      },
    ],
    response: [
      {
        id: "error",
        key: "error",
        value: "false",
        valueType: "Boolean",
        parent: "",
      },
      {
        id: "message",
        key: "message",
        value: "",
        valueType: "String",
        parent: "",
      },
    ],
    code: ``,
    doc: "",
    unit: "",
    protected: true,
    authorizedRoles: [],
    imports: "",
    type: "default",
    group: "ecom",
  },
  {
    id: "_ecomProductDeleteLambda",
    name: "Delete Ecom Product Lambda",
    method: "DELETE",
    description: "Delete Product.",
    route: "/v2/api/lambda/ecom/product/:id",
    inputs: [
      {
        id: "id",
        name: "id",
        type: "path",
        rules: "required",
        dataType: "Number",
        value: "",
      },
    ],
    response: [
      {
        id: "error",
        key: "error",
        value: "false",
        valueType: "Boolean",
        parent: "",
      },
      {
        id: "message",
        key: "message",
        value: "",
        valueType: "String",
        parent: "",
      },
    ],
    code: ``,
    doc: "",
    unit: "",
    protected: true,
    authorizedRoles: [],
    imports: "",
    type: "default",
    group: "ecom",
  },
  {
    id: "_getCartItems",
    name: "Get Cart Items",
    method: "GET",
    description: "Get cart.",
    route: "/v2/api/lambda/ecom/cart",
    columns: [],
    inputs: [
      {
        id: "user_id",
        name: "user_id",
        type: "query",
        rules: "required",
        dataType: "Int?",
        value: "",
      },
    ],
    response: [
      {
        id: "error",
        key: "error",
        value: "false",
        valueType: "Boolean",
        parent: "",
      },
      {
        id: "message",
        key: "message",
        value: "",
        valueType: "String",
        parent: "",
      },
      {
        id: "model",
        key: "model",
        value: [],
        valueType: "Array",
        parent: "",
      },
    ],
    code: ``,
    doc: "",
    unit: "",
    protected: true,
    authorizedRoles: [],
    imports: "",
    type: "default",
    group: "ecom",
  },
  {
    id: "_ecomAddCart",
    name: "Ecom Add Cart",
    method: "POST",
    description: "Add Product to cart.",
    route: "/v2/api/lambda/ecom/cart/item",
    inputs: [
      {
        id: "user_id",
        name: "user_id",
        type: "body",
        rules: "required",
        dataType: "Int?",
        value: "",
      },
      {
        id: "productId",
        name: "productId",
        type: "body",
        rules: "required",
        dataType: "Int",
        value: "",
      },
      {
        id: "quantity",
        name: "quantity",
        type: "body",
        rules: "required",
        dataType: "Int",
        value: "",
      },
    ],
    response: [
      {
        id: "error",
        key: "error",
        value: "false",
        valueType: "Boolean",
        parent: "",
      },
      {
        id: "message",
        key: "message",
        value: "",
        valueType: "String",
        parent: "",
      },
    ],
    code: ``,
    doc: "",
    unit: "",
    protected: true,
    authorizedRoles: [],
    imports: "",
    type: "default",
    group: "ecom",
  },
  {
    id: "_ecomDeleteCartItem",
    name: "Ecom delete Cart item",
    method: "POST",
    description: "Delete Product from cart.",
    route: "/v2/api/lambda/ecom/cart/update",
    inputs: [
      {
        id: "user_id",
        name: "user_id",
        type: "query",
        rules: "required",
        dataType: "Int?",
        value: "",
      },
      {
        id: "data",
        name: "data",
        type: "query",
        rules: "required",
        dataType: [],
        value: "Array",
      },
    ],
    response: [
      {
        id: "error",
        key: "error",
        value: "false",
        valueType: "Boolean",
        parent: "",
      },
      {
        id: "message",
        key: "message",
        value: "",
        valueType: "String",
        parent: "",
      },
    ],
    code: ``,
    doc: "",
    unit: "",
    protected: true,
    authorizedRoles: [],
    imports: "",
    type: "default",
    group: "ecom",
  },
  {
    id: "_ecomGetReviews",
    name: "Ecom get product review",
    method: "POST",
    description: "Get Product Review.",
    route: "/v2/api/lambda/ecom/product/review",
    inputs: [
      {
        id: "productId",
        name: "productId",
        type: "query",
        rules: "required",
        dataType: "Int",
        value: "",
      },
    ],
    response: [
      {
        id: "error",
        key: "error",
        value: "false",
        valueType: "Boolean",
        parent: "",
      },
      {
        id: "message",
        key: "message",
        value: "",
        valueType: "String",
        parent: "",
      },
      {
        id: "list",
        key: "list",
        value: [],
        valueType: "String",
        parent: "",
      },
    ],
    code: ``,
    doc: "",
    unit: "",
    protected: true,
    authorizedRoles: [],
    imports: "",
    type: "default",
    group: "ecom",
  },
  {
    id: "_ecomAddReviews",
    name: "Ecom add product review",
    method: "POST",
    description: "Add Product Review.",
    route: "/v2/api/lambda/ecom/product/review/add",
    inputs: [
      {
        id: "review",
        name: "review",
        type: "query",
        rules: "required",
        dataType: "String",
        value: "",
      },
      {
        id: "productId",
        name: "productId",
        type: "query",
        rules: "required",
        dataType: "Int",
        value: "",
      },
    ],
    response: [
      {
        id: "error",
        key: "error",
        value: "false",
        valueType: "Boolean",
        parent: "",
      },
      {
        id: "message",
        key: "message",
        value: "",
        valueType: "String",
        parent: "",
      },
    ],
    code: ``,
    doc: "",
    unit: "",
    protected: true,
    authorizedRoles: [],
    imports: "",
    type: "default",
    group: "ecom",
  },
];
