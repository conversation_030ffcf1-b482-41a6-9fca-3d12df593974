export const reset = [
    {
        id: '_resetPassword',
        name: 'Reset Password',
        method: 'POST',
        description: '',
        route: '/v2/api/lambda/reset',
        inputs: [
            { id: 'token', name: 'token', type: 'body', rules: 'required', dataType: 'String', value: '' },
            { id: 'code', name: 'code', type: 'body', rules: 'required', dataType: 'String', value: '' },
            { id: 'password', name: 'password', type: 'body', rules: 'required', dataType: 'String', value: '' },
        ],
        response: [ 
            { id: "error", key: 'error', value: "false", valueType: 'Boolean', parent: '' },
            { id: "message", key: 'message', value: '', valueType: 'String', parent: '' },
            { id: "validation", key: 'validation', value: '', valueType: 'Array', parent: '' },
        ],
        code: '',
        doc: '',
        unit: '',
        protected: false,
        authorizedRoles: [],
        imports: '',
        type: 'default',
        group: 'reset'
      },
    {
        id: '_resetPasswordMobile',
        name: 'Reset Password Mobile',
        method: 'POST',
        description: '',
        route: '/v2/api/lambda/mobile/reset',
        inputs: [
            { id: 'code', name: 'code', type: 'body', rules: 'required', dataType: 'String', value: '' },
            { id: 'password', name: 'password', type: 'body', rules: 'required', dataType: 'String', value: '' },
        ],
        response: [ 
            { id: "error", key: 'error', value: "false", valueType: 'Boolean', parent: '' },
            { id: "message", key: 'message', value: '', valueType: 'String', parent: '' },
            { id: "validation", key: 'validation', value: '', valueType: 'Array', parent: '' },
        ],
        code: '',
        doc: '',
        unit: '',
        protected: false,
        authorizedRoles: [],
        imports: '',
        type: 'default',
        group: 'reset'
      },
      
      
      
      
]
