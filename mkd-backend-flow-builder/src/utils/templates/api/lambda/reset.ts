
export const reset = [
    {
        id: 'resetPassword',
        name: 'Reset Password',
        method: 'POST',
        description: '',
        route: '/v3/api/custom/lambda/reset',
        inputs: [
            { id: 'token', name: 'token', type: 'body', rules: 'required', dataType: 'String', value: '' },
            { id: 'code', name: 'code', type: 'body', rules: 'required', dataType: 'String', value: '' },
            { id: 'password', name: 'code', type: 'body', rules: 'required', dataType: 'String', value: '' },
        ],
        response: [ 
            { id: "error", key: 'error', value: "false", valueType: 'Boolean', parent: '' },
            { id: "message", key: 'message', value: '', valueType: 'String', parent: '' },
            { id: "validation", key: 'validation', value: '', valueType: 'Array', parent: '' },
        ],
        code: '',
        doc: '',
        unit: '',
        protected: false,
        authorizedRoles: [],
        imports: '',
        type: 'lambda',
        group: 'reset'
      },
    {
        id: 'resetPasswordMobile',
        name: 'Reset Password Mobile',
        method: 'POST',
        description: '',
        route: '/v3/api/custom/lambda/mobile/reset',
        inputs: [
            { id: 'code', name: 'code', type: 'body', rules: 'required', dataType: 'Integer', value: '' },
            { id: 'password', name: 'password', type: 'body', rules: 'required', dataType: 'String', value: '' },
        ],
        response: [ 
            { id: "error", key: 'error', value: "false", valueType: 'Boolean', parent: '' },
            { id: "message", key: 'message', value: '', valueType: 'String', parent: '' },
            { id: "validation", key: 'validation', value: '', valueType: 'Array', parent: '' },
        ],
        code: '',
        doc: '',
        unit: '',
        protected: false,
        authorizedRoles: [],
        imports: '',
        type: 'lambda',
        group: 'reset'
      },
      
      
      
      
]





export const resetPasswordImports = `
const AuthService = require("../../../services/AuthService");
const JwtService = require("../../../services/JwtService");
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const PermissionMiddleware = require("../../../middleware/PermissionMiddleware");
const permissionService = require("../../../services/PermissionService");
const DevLogService = require("../../../services/DevLogService");
const config = require("../../../config");
const NodeGoogleLogin = require("node-google-login");
const ManaKnightSDK = require("../../../core/ManaKnightSDK");
const BackendSDK = require("../../../core/BackendSDK");
const { ideahub_v1alpha } = require("googleapis");

const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  PermissionMiddleware
  // RateLimitMiddleware,
  // LogMiddleware,
  // UsageMiddleware
  // CheckProjectMiddleware,
  // AnalyticMiddleware,
  // RoleMiddleware
];

let logService = new DevLogService();
`