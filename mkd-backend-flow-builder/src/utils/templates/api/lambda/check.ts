export const check = [
  {
    id: 'checkLambda', 
    name: 'Lambda Check', 
    method: 'POST', 
    description: '',
    route: `/v3/api/custom/lambda/check`, 
    inputs: [{name: 'role', type: 'body', rules: 'required', dataType: "String"}], 
    response: [
      { id: "error", key: 'error', value: "false", valueType: 'Boolean', parent: '' },
      { id: "message", key: 'message', value: 'OK', valueType: 'String', parent: '' }
    ], 
    code: `app.post("/v3/api/custom/lambda/check", [TokenMiddleware()], function (req, res) {
      try {
        if (!req.body.role) {
          return res.status(401).json({
            error: true,
            message: "UNAUTHORIZED",
            code: "UNAUTHORIZED",
          });
        }
  
        if (req.body.role != req.role) {
          return res.status(401).json({
            error: true,
            message: "UNAUTHORIZED",
            code: "UNAUTHORIZED",
          });
        }
  
        return res.status(200).json({ error: false, message: "OK" });
      } catch (err) {
        console.error(err);
        res.status(404);
        res.json({
          error: true,
          message: err.message,
        });
      }
    });`, 
    doc: '', 
    unit: '',
    protected: true,
    authorizedRoles: [],
    imports: ``,
    type: 'lambda',
    group: 'check',
},
]

export const checkImports = `

  const TokenMiddleware = require("../../../middleware/TokenMiddleware");
  const config = require("../../../config");

`