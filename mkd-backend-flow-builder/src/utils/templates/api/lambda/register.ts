export const register = [
    {
        id: 'registerLambda',
        name: 'Register Lambda',
        method: 'POST',
        description: '',
        route: '/v3/api/custom/lambda/register',
        inputs: [
          { name: 'email', type: 'body', rules: 'required', dataType: 'String' },
          { name: 'role', type: 'body', rules: 'required', dataType: 'String' },
          { name: 'verify', type: 'body', rules: '', dataType: 'Boolean' },
          { name: 'is_refresh', type: 'body', rules: '', dataType: 'Boolean' },
          { name: 'password', type: 'body', rules: 'required', dataType: 'String' },
          { name: 'first_name', type: 'body', rules: '', dataType: 'String' },
          { name: 'last_name', type: 'body', rules: '', dataType: 'String' },
          { name: 'photo', type: 'body', rules: '', dataType: 'String' },
          { name: 'phone', type: 'body', rules: '', dataType: 'String' }
        ],
        response: [
          { id: 'error', key: 'error', value: 'false', valueType: 'Boolean', parent: '' },
          { id: 'role', key: 'role', value: 'req.body.role', valueType: 'String', parent: '' },
          { id: 'token', key: 'token', value: 'JwtService.createAccessToken({ user_id: result, role: req.body.role }, config.jwt_expire, config.jwt_key)', valueType: 'String', parent: '' },
          { id: 'refresh_token', key: 'refresh_token', value: 'refreshToken', valueType: 'String', parent: '' },
          { id: 'expire_at', key: 'expire_at', value: 'config.jwt_expire', valueType: 'Number', parent: '' },
          { id: 'user_id', key: 'user_id', value: 'result', valueType: 'String', parent: '' }
        ],
        code: `app.post("/v3/api/custom/lambda/register", middlewares, async function (req, res) {
          try {
            let verify = req.body.verify ? req.body.verify : false;
            const needRefreshToken = req.body.is_refresh ? true : false;
            let refreshToken = undefined;
            if (!req.body.email) {
              return res.status(403).json({
                error: true,
                message: "Email Missing",
                validation: [{ field: "email", message: "Email missing" }]
              });
            }
            if (!req.body.role) {
              return res.status(403).json({
                error: true,
                message: "Role Missing",
                validation: [{ field: "role", message: "Role missing" }]
              });
            }
            if (req.body.role === "admin") {
              verify = true;
              const userData = JwtService.verifyAccessToken(JwtService.getToken(req), config.jwt_key);
              if (!userData || userData.role != "admin") {
                return res.status(403).json({
                  error: true,
                  message: "Admin can't be registered using this API",
                  validation: [{ field: "role", message: "Role (admin) is not allowed" }]
                });
              }
            }
            if (!req.body.password) {
              return res.status(403).json({
                error: true,
                message: "Password Missing",
                validation: [{ field: "password", message: "Password missing" }]
              });
            }
      
            let service = new AuthService();
      
            logService.log(req.projectId, req.body.email, req.body.password, req.body.role);
      
            const result = await service.register(
              req.app.get("sdk"),
              req.projectId,
              req.body.email,
              req.body.password,
              req.body.role,
              verify,
              req.body.first_name,
              req.body.last_name,
              req.body.photo,
              req.body.phone
            );
      
            if (typeof result == "string") {
              return res.status(403).json({
                error: true,
                message: result
              });
            } else {
              if (needRefreshToken) {
                refreshToken = JwtService.createAccessToken(
                  {
                    user_id: result,
                    role: req.body.role
                  },
                  config.refresh_jwt_expire,
                  config.jwt_key
                );
                let expireDate = new Date();
                expireDate.setSeconds(expireDate.getSeconds() + config.refresh_jwt_expire);
                await service.saveRefreshToken(req.sdk, req.projectId, result, refreshToken, expireDate);
              }
      
              return res.status(200).json({
                error: false,
                role: req.body.role,
                token: JwtService.createAccessToken(
                  {
                    user_id: result,
                    role: req.body.role
                  },
                  config.jwt_expire,
                  config.jwt_key
                ),
                refresh_token: refreshToken,
                expire_at: config.jwt_expire,
                user_id: result
              });
            }
          } catch (err) {
            res.status(403);
            res.json({
              error: true,
              message: err.message
            });
          }
        });`,
        doc: '',
        unit: '',
        protected: true,
        authorizedRoles: [],
        imports: ``,
        type: 'lambda',
        group: 'register'
      }
      
]

export const registerImports = `

const AuthService = require("../../../services/AuthService");
const JwtService = require("../../../services/JwtService");
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const DevLogService = require("../../../services/DevLogService");
const config = require("../../../config");

const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware
  // RateLimitMiddleware,
  // LogMiddleware,
  // UsageMiddleware
  // CheckProjectMiddleware,
  // AnalyticMiddleware,
  // RoleMiddleware
];

let logService = new DevLogService();


`