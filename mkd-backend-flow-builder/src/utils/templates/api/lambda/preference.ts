export const preference = [
    {
        id: 'preferenceLambda',
        name: 'Preference Lambda',
        method: 'GET',
        description: '',
        route: '/v3/api/custom/lambda/preference',
        inputs: [],
        response: [
          { id: 'user_id', key: 'user_id', value: '', valueType: 'Number', parent: '' },
          { id: 'fcm_token', key: 'fcm_token', value: '', valueType: 'String', parent: '' },
          { id: 'first_name', key: 'first_name', value: '', valueType: 'String', parent: '' },
          { id: 'last_name', key: 'last_name', value: '', valueType: 'String', parent: '' },
          { id: 'email', key: 'email', value: '', valueType: 'String', parent: '' },
          { id: 'role', key: 'role', value: '', valueType: 'String', parent: '' },
          { id: 'phone', key: 'phone', value: '', valueType: 'String', parent: '' },
          { id: 'photo', key: 'photo', value: '', valueType: 'String', parent: '' },
          { id: 'mapping', key: 'mapping', value: '', valueType: 'String', parent: '' },
        ],
        code: `app.get("/v3/api/custom/lambda/preference", middlewares, async function (req, res) {
          try {
            let sdk = req.sdk;
            sdk.getDatabase();
            sdk.setProjectId(req.projectId);
            sdk.setTable("profile");
            const result = await sdk.get({
              user_id: req.user_id,
            });
      
            if (typeof result == "string") {
              return res.status(403).json({
                error: true,
                message: result,
              });
            }
      
            if (result.length != 1) {
              return res.status(401).json({
                error: true,
                message: "Invalid Credentials",
              });
            }
      
            sdk.setTable("user");
            const userResult = await sdk.get({
              id: req.user_id,
            });
      
            if (userResult.length != 1) {
              return res.status(401).json({
                error: true,
                message: "Invalid Credentials",
              });
            }
      
            let preferenceMapping = await getMapping(req);
      
            return res.status(200).json({
              ...result[0],
              first_name: userResult[0].first_name,
              last_name: userResult[0].last_name,
              email: userResult[0].email,
              role: userResult[0].role,
              phone: userResult[0].phone,
              photo: userResult[0].photo,
              mapping: preferenceMapping,
            });
          } catch (err) {
            res.status(403);
            res.json({
              error: true,
              message: err.message,
            });
          }
        });`,
        doc: '',
        unit: '',
        protected: true,
        authorizedRoles: ['user'],
        imports: `        `,
        type: 'lambda',
        group: 'preference'
      },
      {
        id: 'preferenceLambda',
        name: 'Preference Lambda',
        method: 'POST',
        description: '',
        route: '/v3/api/custom/lambda/preference',
        inputs: [
            { name: 'payload', type: 'body', rules: 'required', dataType: 'Object' }
        ],
        response: [],
        code: `app.post("/v3/api/custom/lambda/preference", middlewares, async function (req, res) {
          try {
            let sdk = req.sdk;
            sdk.getDatabase();
            sdk.setProjectId(req.projectId);
            sdk.setTable("profile");
            const result = await sdk.get({
              user_id: req.user_id,
            });
      
            if (typeof result == "string") {
              return res.status(403).json({
                error: true,
                message: result,
              });
            }
      
            if (result.length != 1) {
              return res.status(401).json({
                error: true,
                message: "Invalid Credentials",
              });
            }
      
            sdk.setTable("user");
            const userResult = await sdk.get({
              id: req.user_id,
            });
      
            if (userResult.length != 1) {
              return res.status(401).json({
                error: true,
                message: "Invalid Credentials",
              });
            }
      
            sdk.setTable("profile");
            const updateResult = await sdk.update(req.body.payload, result[0].id);
      
            if (updateResult == null) {
              return res.status(403).json({
                error: true,
                message: updateResult,
              });
            }
      
            return res.status(200).json({
              error: false,
              message: "Updated",
            });
          } catch (err) {
            res.status(403);
            res.json({
              error: true,
              message: err.message,
            });
          }
        });`,
        doc: '',
        unit: '',
        protected: true,
        authorizedRoles: ['user'],
        imports: '',
        type: 'lambda',
        group: 'preference'
      }
      
      
];

export const preferenceImports = `

const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const TokenMiddleware = require("../../../middleware/TokenMiddleware");
const ManaKnightSDK = require("../../../core/ManaKnightSDK");
const config = require("../../../config");

const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware(),
  // RateLimitMiddleware,
  // LogMiddleware,
  // UsageMiddleware
  // CheckProjectMiddleware,
  // AnalyticMiddleware,
  // RoleMiddleware
];

`