export const captcha = [
  {
      id: "captchaTest",
      name: "Captcha Test",
      description: "Endpoint for generating a captcha test",
      method: "GET",
      route: "/v3/api/custom/lambda/test/:width?/:height?/",
      inputs: [
        { name: "width", type: "path", rules: "", dataType: "Integer" },
        { name: "height", type: "path", rules: "", dataType: "Integer" }
      ],
      response: [
        {
          id: "error",
          key: "error",
          value: "false",
          valueType: "Boolean",
          parent: ""
        },
        {
          id: "model",
          key: "model",
          value: "<img class=\"generated-captcha\" src=\"{image}\">",
          valueType: "String",
          parent: ""
        }
      ],
      code: `
      app.get("/v3/api/custom/lambda/test/:width?/:height?/", middleWares, async(req, res) => {
        const width = parseInt(req.params.width) || 200;
        const height = parseInt(req.params.height) || 100;
        const { image } = captcha(width, height);
        return res.status(200).json({ 
            error: false, 
            model:\`<img class="generated-captcha" src="\${image}">\`
        });
      });
      `,
      doc: "",
      unit: "",
      protected: false,
      authorizedRoles: [],
      imports: "",
      type: "lambda",
      group: "captcha"
    },
    {
      id: "captchaGenerate",
      name: "Captcha Generate",
      description: "Endpoint for generating a captcha",
      method: "GET",
      route: "/v3/api/custom/lambda/captcha/:width?/:height?/",
      inputs: [
        { name: "width", type: "path", rules: "", dataType: "Integer" },
        { name: "height", type: "path", rules: "", dataType: "Integer" }
      ],
      response: [
        {
          id: "error",
          key: "error",
          value: "false",
          valueType: "Boolean",
          parent: ""
        },
        {
          id: "model",
          key: "model",
          value: "",
          valueType: "Object",
          parent: ""
        },
        {
          id: "image",
          key: "image",
          value: "",
          valueType: "String",
          parent: "model"
        },
        {
          id: "text",
          key: "text",
          value: "",
          valueType: "String",
          parent: "model"
        }
      ],
      code: `
      app.get("/v3/api/custom/lambda/captcha/:width?/:height?/", middleWares, async(req, res) => {
        const width = parseInt(req.params.width) || 200;
        const height = parseInt(req.params.height) || 100;
        const { image, text } = captcha(width, height);
        res.send({ image, text });
        return res.status(200).json({ 
            error: false, 
            model:{ image, text }
        });
      });
      `,
      doc: "",
      unit: "",
      protected: false,
      authorizedRoles: [],
      imports: "",
      type: "lambda",
      group: "captcha"
    },
    {
      id: "googleCaptchaVerify",
      name: "Google Captcha Verify",
      description: "Endpoint for verifying Google reCAPTCHA token",
      method: "POST",
      route: "/v3/api/custom/lambda/google-captcha/",
      inputs: [
        { name: "formData", type: "body", rules: "", dataType: "Object" },
        { name: "captchaToken", type: "body", rules: "", dataType: "String" }
      ],
      response: [
        {
          id: "error",
          key: "error",
          value: "false",
          valueType: "Boolean",
          parent: ""
        },
        {
          id: "message",
          key: "message",
          value: "",
          valueType: "String",
          parent: ""
        }
      ],
      code: `
      app.post("/v3/api/custom/lambda/google-captcha/", middleWares, async(req, res) => {
        try{
          // Extract token
          const { 
            formData,
            captchaToken
          } = req.body;
        
          // Call Google's API to get score
          const response = await axios.post(
              \`https://www.google.com/recaptcha/api/siteverify?secret=\${config.google.captcha_secret_key}&response=\${captchaToken}\`
          );
          
          // Extract result from the API response
          if(response.data.success) return res.status(200).json({
            error:false,
            message:"Verified"
          });
          else return res.status(200).json({
            error:true,
            message:"Not Verified"
          })
        }catch(e){
            return res.status(200).json({
              error:true,
              message:"Something went wrong"
          })
        }
      });
      
      `,
      doc: "",
      unit: "",
      protected: false,
      authorizedRoles: [],
      imports: "",
      type: "lambda",
      group: "captcha"
    }
    
    
    
];

export const captchaImports = `
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const PermissionMiddleware = require("../../../middleware/PermissionMiddleware");
const { default: axios } = require("axios");


const middleWares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  // PermissionMiddleware
];

const config = require("../../../config");

`