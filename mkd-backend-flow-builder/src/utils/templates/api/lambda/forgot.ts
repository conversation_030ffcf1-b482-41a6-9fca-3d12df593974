
export const forgot = [
    {
        id: 'forgotPassword',
        name: 'Forgot Password',
        method: 'POST',
        description: '',
        route: '/v3/api/custom/lambda/forgot',
        inputs: [
            { id: 'email', name: 'email', type: 'body', rules: 'required', dataType: 'String', value: '' },
            { id: 'role', name: 'role', type: 'body', rules: 'required', dataType: 'String', value: '' },
        ],
        response: [ 
            { id: "error", key: 'error', value: "false", valueType: 'Boolean', parent: '' },
            { id: "message", key: 'message', value: '', valueType: 'String', parent: '' },
        ],
        code: '',
        doc: '',
        unit: '',
        protected: false,
        authorizedRoles: [],
        imports: '',
        type: 'lambda',
        group: 'forgot'
      },
    {
        id: 'forgotPasswordMobile',
        name: 'Forgot Password Mobile',
        method: 'POSt',
        description: '',
        route: '/v3/api/custom/lambda/mobile/forgot',
        inputs: [
            { id: 'email', name: 'email', type: 'body', rules: 'required', dataType: 'String', value: '' },
        ],
        response: [ 
            { id: "error", key: 'error', value: "false", valueType: 'Boolean', parent: '' },
            { id: "message", key: 'message', value: '', valueType: 'String', parent: '' },
        ],
        code: '',
        doc: '',
        unit: '',
        protected: false,
        authorizedRoles: [],
        imports: '',
        type: 'lambda',
        group: 'forgot'
      },
      
      
      
      
]





export const forgotPasswordImports = `
const AuthService = require("../../../services/AuthService");
const JwtService = require("../../../services/JwtService");
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const PermissionMiddleware = require("../../../middleware/PermissionMiddleware");
const permissionService = require("../../../services/PermissionService");
const DevLogService = require("../../../services/DevLogService");
const config = require("../../../config");
const NodeGoogleLogin = require("node-google-login");
const ManaKnightSDK = require("../../../core/ManaKnightSDK");
const BackendSDK = require("../../../core/BackendSDK");
const { ideahub_v1alpha } = require("googleapis");

const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  PermissionMiddleware
  // RateLimitMiddleware,
  // LogMiddleware,
  // UsageMiddleware
  // CheckProjectMiddleware,
  // AnalyticMiddleware,
  // RoleMiddleware
];

let logService = new DevLogService();
`