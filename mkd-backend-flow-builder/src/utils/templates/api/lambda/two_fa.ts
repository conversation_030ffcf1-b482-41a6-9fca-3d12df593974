export const two_fa = [
    {
        id: 'twofaLogin',
        name: 'Two FA Login',
        description: 'Handles the 2FA login process',
        method: 'POST',
        route: '/v3/api/custom/lambda/2fa/login',
        inputs: [
          { name: 'email', type: 'body', rules: 'required', dataType: 'String' },
          { name: 'password', type: 'body', rules: 'required', dataType: 'String' },
          { name: 'role', type: 'body', rules: 'required', dataType: 'String' }
        ],
        response: [
          { id: 'error', key: 'error', value: 'false', valueType: 'Boolean', parent: '' },
          { id: 'qr_code', key: 'qr_code', value: '', valueType: 'String', parent: '' },
          { id: 'one_time_token', key: 'one_time_token', value: '', valueType: 'String', parent: '' },
          { id: 'expire_at', key: 'expire_at', value: 60, valueType: 'Number', parent: '' },
          { id: 'user_id', key: 'user_id', value: '', valueType: 'String', parent: '' }
        ],
        code: ``,
        doc: '',
        unit: '',
        protected: false,  // Updated to false
        authorizedRoles: [],
        imports: ``,
        type: 'default',
        group: 'twofa'
      },
      {
        id: 'twofaSignin',
        name: 'Two FA Signin',
        description: 'Handles the 2FA signin process',
        method: 'POST',
        route: '/v3/api/custom/lambda/2fa/signin',
        inputs: [
          { name: 'email', type: 'body', rules: 'required', dataType: 'String' },
          { name: 'password', type: 'body', rules: 'required', dataType: 'String' },
          { name: 'role', type: 'body', rules: 'required', dataType: 'String' }
        ],
        response: [
          { id: 'error', key: 'error', value: 'false', valueType: 'Boolean', parent: '' },
          { id: 'qr_code', key: 'qr_code', value: '', valueType: 'String', parent: '' },
          { id: 'access_token', key: 'access_token', value: '', valueType: 'String', parent: '' },
          { id: 'expire_at', key: 'expire_at', value: '', valueType: 'String', parent: '' },
          { id: 'user_id', key: 'user_id', value: '', valueType: 'String', parent: '' }
        ],
        code: ``,
        doc: '',
        unit: '',
        protected: false,  // Updated to false
        authorizedRoles: [],
        imports: ``,
        type: 'default',
        group: 'twofa'
      },
      {
        id: 'twofaAuthorize',
        name: 'Two FA Authorize',
        description: 'Authorizes the user with 2FA',
        method: 'POST',
        route: '/v3/api/custom/lambda/2fa/authorize',
        inputs: [
          { name: 'user_id', type: 'body', rules: 'required', dataType: 'String' }
        ],
        response: [
          { id: 'error', key: 'error', value: 'false', valueType: 'Boolean', parent: '' },
          { id: 'qr_code', key: 'qr_code', value: '', valueType: 'String', parent: '' },
          { id: 'type', key: 'type', value: 'qr', valueType: 'String', parent: '' },
          { id: 'access_token', key: 'access_token', value: '', valueType: 'String', parent: '' },
          { id: 'expire_at', key: 'expire_at', value: '', valueType: 'String', parent: '' },
          { id: 'user_id', key: 'user_id', value: '', valueType: 'String', parent: '' }
        ],
        code: ``,
        doc: '',
        unit: '',
        protected: true,
        authorizedRoles: [],
        imports: ``,
        type: 'default',
        group: 'twofa'
      },
      {
        id: 'twofaEnable',
        name: 'Two FA Enable',
        description: 'Enables 2FA for the user',
        method: 'POST',
        route: '/v3/api/custom/lambda/2fa/enable',
        inputs: [
          { name: 'user_id', type: 'body', rules: 'required', dataType: 'String' },
          { name: 'type', type: 'body', rules: '', dataType: 'String' },
          { name: 'phone', type: 'body', rules: '', dataType: 'String' },
          { name: 'token', type: 'body', rules: '', dataType: 'String' }
        ],
        response: [
          { id: 'error', key: 'error', value: 'false', valueType: 'Boolean', parent: '' },
          { id: 'qr_code', key: 'qr_code', value: '', valueType: 'String', parent: '' },
          { id: 'access_token', key: 'access_token', value: '', valueType: 'String', parent: '' },
          { id: 'expire_at', key: 'expire_at', value: '', valueType: 'String', parent: '' },
          { id: 'user_id', key: 'user_id', value: '', valueType: 'String', parent: '' }
        ],
        code: '',
        doc: '',
        unit: '',
        protected: true,
        authorizedRoles: [],
        imports: '',
        type: 'default',
        group: 'twofa'
      },
      {
        id: 'twofaDisable',
        name: 'Two FA Disable',
        description: 'Disables 2FA for the user',
        method: 'POST',
        route: '/v3/api/custom/lambda/2fa/disable',
        inputs: [
          { name: 'user_id', type: 'body', rules: 'required', dataType: 'String' }
        ],
        response: [
          { id: 'error', key: 'error', value: 'false', valueType: 'Boolean', parent: '' },
          { id: 'message', key: 'message', value: '', valueType: 'String', parent: '' }
        ],
        code: '',
        doc: '',
        unit: '',
        protected: true,
        authorizedRoles: [],
        imports: '',
        type: 'default',
        group: 'twofa'
      },
      {
        id: 'twofaVerify',
        name: 'Two FA Verify',
        description: 'Verifies the 2FA token',
        method: 'POST',
        route: '/v3/api/custom/lambda/2fa/verify',
        inputs: [
          { name: 'token', type: 'body', rules: 'required', dataType: 'String' },
          { name: 'access_token', type: 'body', rules: '', dataType: 'String' }
        ],
        response: [
          { id: 'error', key: 'error', value: 'false', valueType: 'Boolean', parent: '' },
          { id: 'valid', key: 'valid', value: 'true', valueType: 'Boolean', parent: '' },
          { id: 'message', key: 'message', value: 'Verified Successfully', valueType: 'String', parent: '' }
        ],
        code: '',
        doc: '',
        unit: '',
        protected: false,
        authorizedRoles: [],
        imports: '',
        type: 'default',
        group: 'twofa'
      },
      {
        id: 'twofaAuth',
        name: 'Two FA Auth',
        description: 'Performs 2FA authentication',
        method: 'POST',
        route: '/v3/api/custom/lambda/2fa/auth',
        inputs: [
          { name: 'code', type: 'body', rules: 'required', dataType: 'String' },
          { name: 'token', type: 'body', rules: '', dataType: 'String' }
        ],
        response: [
          { id: 'error', key: 'error', value: 'false', valueType: 'Boolean', parent: '' },
          { id: 'role', key: 'role', value: '', valueType: 'String', parent: '' },
          { id: 'token', key: 'token', value: '', valueType: 'String', parent: '' },
          { id: 'expire_at', key: 'expire_at', value: '', valueType: 'String', parent: '' },
          { id: 'user_id', key: 'user_id', value: '', valueType: 'String', parent: '' }
        ],
        code: '',
        doc: '',
        unit: '',
        protected: false,
        authorizedRoles: [],
        imports: '',
        type: 'default',
        group: 'twofa'
      }
      
      
      
      
]


export const two_faImports = `

    const AuthService = require("../../../services/AuthService");
    const JwtService = require("../../../services/JwtService");
    const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
    const UrlMiddleware = require("../../../middleware/UrlMiddleware");
    const HostMiddleware = require("../../../middleware/HostMiddleware");
    const DevLogService = require("../../../services/DevLogService");
    const TwoFactorService = require("../../../services/TwoFactorService");
    const LambdaPermissionMiddleware = require("../../../middleware/LambdaPermissionMiddleware");
    const TokenMiddleware = require("../../../middleware/TokenMiddleware");
    const TwilloSmsService = require("../../../services/TwilloSmsService");

    const middlewares = [
    ProjectMiddleware,
    UrlMiddleware,
    HostMiddleware
    // LambdaPermissionMiddleware
    // RateLimitMiddleware,
    // LogMiddleware,
    // UsageMiddleware
    // CheckProjectMiddleware,
    // AnalyticMiddleware,
    // RoleMiddleware
    ];

    const config = require("../../../config");

`
