export const upload = [
    {
        id: 'uploadLocalImageLambda',
        name: 'Upload Image Local Lambda',
        method: 'POST',
        description: '',
        route: '/v3/api/custom/lambda/upload',
        inputs: [
          { name: 'file', type: 'multipart', rules: '', dataType: 'String' }
        ],
        response: [
          { id: 'id', key: 'id', value: '', valueType: 'String', parent: '' },
          { id: 'url', key: 'url', value: '', valueType: 'String', parent: '' }
        ],
        code: `app.post("/v3/api/custom/lambda/upload", imageMiddlewares, async function (req, res) {
          try {
            const url = getLocalPath(req.file.path);
      
            let params = {
              url: url,
              user_id: req.user_id || null,
              caption: req.body.caption || null,
              type: 1,
              width: 0,
              height: 0,
            };
            const whitelist = ["image/png", "image/jpeg", "image/jpg"];
      
            const uploadedfile = fs.readFileSync(req.file.path);
      
            if (whitelist.includes(req.file.mimetype)) {
              const dimensions = sizeOf(uploadedfile);
              params.width = dimensions.width;
              params.height = dimensions.height;
              params.type = 0;
            }
      
            let sdk = req.sdk;
            sdk.getDatabase();
            sdk.setProjectId(req.projectId);
            sdk.setTable("photo");
      
            logService.log(req.body);
            logService.log(req.user_id);
      
            const result = await sdk.insert({
              url: params.url,
              caption: params.caption,
              user_id: req.user_id,
              width: params.width,
              height: params.height,
              type: params.type,
              create_at: sqlDateFormat(new Date()),
              update_at: sqlDateTimeFormat(new Date()),
            });
      
            return res.status(201).json({ id: result, url });
          } catch (error) {
            console.log(error);
            return res.status(500).json({ error: true, message: error.message });
          }
        });`,
        doc: '',
        unit: '',
        protected: true,
        authorizedRoles: [],
        imports: ``,
        type: 'lambda',
        group: 'upload'
      },
      {
        id: 'UploadImageS3Lambda',
        name: 'Upload Image S3 Lambda',
        method: 'POST',
        description: '',
        route: '/v3/api/custom/lambda/s3/upload',
        inputs: [
          { name: 'file', type: 'multipart', rules: '', dataType: 'String' },
        ],
        response: [
          { id: 'id', key: 'id', value: '', valueType: 'String', parent: '' },
          { id: 'url', key: 'url', value: '', valueType: 'String', parent: '' }
        ],
        code: `app.post("/v3/api/custom/lambda/s3/upload", imageMiddlewaresS3, async function (req, res) {
          try {
            const url = req.file.location;
      
            let params = {
              url: url,
              user_id: req.user_id || null,
              caption: req.body.caption || null,
              type: 1,
              width: 0,
              height: 0,
            };
            const whitelist = ["image/png", "image/jpeg", "image/jpg"];
      
            if (whitelist.includes(req.file.mimetype)) {
              const dimensions = await sizeOfRemote(url);
              params.width = dimensions.width;
              params.height = dimensions.height;
              params.type = 0;
            }
      
            let sdk = req.sdk;
            sdk.getDatabase();
            sdk.setProjectId(req.projectId);
            sdk.setTable("photo");
      
            logService.log(req.body);
            logService.log(req.user_id);
      
            const result = await sdk.insert({
              url: params.url,
              caption: params.caption,
              user_id: req.user_id,
              width: params.width,
              height: params.height,
              type: params.type,
              create_at: sqlDateFormat(new Date()),
              update_at: sqlDateTimeFormat(new Date()),
            });
      
            res.set('Content-Type', 'application/json');
            return res.status(201).json({ id: result, url });
          } catch (error) {
            console.log(error);
            return res.status(500).json({ error: true, message: error.message });
          }
        });`,
        doc: '',
        unit: '',
        protected: true,
        authorizedRoles: [],
        imports: `
          const imageMiddlewaresS3 = require("../../../middleware/imageMiddlewaresS3");
          const sizeOfRemote = require("image-size");
          const logService = require("../../../services/logService");
          const { sqlDateFormat, sqlDateTimeFormat } = require("../../../utils/dateUtils");
        `,
        type: 'lambda',
        group: 'upload'
      }
      
      
];

export const uploadImports = `
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const DevLogService = require("../../../services/DevLogService");
const UploadService = require("../../../services/UploadService");
const { getLocalPath, sqlDateFormat, sqlDateTimeFormat, sizeOfRemote } = require("../../../services/UtilService");
const sizeOf = require("image-size");
const TokenMiddleware = require("../../../middleware/TokenMiddleware");
const upload = UploadService.local_upload();
const uploadS3 = UploadService.s3_upload();
const uploadS3Public = UploadService.s3_upload_public();
const fs = require("fs");

const imageMiddlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware(),
  upload.single("file"),
  // RateLimitMiddleware,
  // LogMiddleware,
  // UsageMiddleware
  // CheckProjectMiddleware,
  // AnalyticMiddleware,
  // RoleMiddleware
];
const imageMiddlewaresS3 = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware(),
  uploadS3.single("file"),
  // RateLimitMiddleware,
  // LogMiddleware,
  // UsageMiddleware
  // CheckProjectMiddleware,
  // AnalyticMiddleware,
  // RoleMiddleware
];
const imagesMiddlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware(),
  upload.array("files"),
  // RateLimitMiddleware,
  // LogMiddleware,
  // UsageMiddleware
  // CheckProjectMiddleware,
  // AnalyticMiddleware,
  // RoleMiddleware
];
const imagesMiddlewaresS3 = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware(),
  uploadS3.array("files"),
  // RateLimitMiddleware,
  // LogMiddleware,
  // UsageMiddleware
  // CheckProjectMiddleware,
  // AnalyticMiddleware,
  // RoleMiddleware
];

const imagesPublicMiddlewaresS3 = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  uploadS3Public.array("files"),
  // RateLimitMiddleware,
  // LogMiddleware,
  // UsageMiddleware
  // CheckProjectMiddleware,
  // AnalyticMiddleware,
  // RoleMiddleware
];

let logService = new DevLogService();

`