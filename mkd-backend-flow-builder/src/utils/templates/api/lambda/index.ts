import { check, checkImports } from "./check";
import { captcha, captchaImports } from "./captcha";
import { analytics, analyticsImports } from "./analytics";
import { apple_login, appleLoginImports } from "./apple_login";
import { google_login, googleLoginImports } from "./google_login";
import { blog, blogImports } from "./blog";
import { cms, cmsImports } from "./cms";
import { login, loginImports } from "./login";
import { register, registerImports } from "./register";
import { admission_event, admissionEventImports } from "./admission_event";
import { upload, uploadImports } from "./upload";
import { ecommerce, ecommerceImports } from "./ecommerce";
import { forgot, forgotPasswordImports } from "./forgot";
import { reset, resetPasswordImports } from "./reset";


const lambda = {
  apple_login: { endpoints: apple_login, imports: appleLoginImports },
  google_login: { endpoints: google_login, imports: googleLoginImports },
  analytics: { endpoints: analytics, imports: analyticsImports },
  blog: { endpoints: blog, imports: blogImports },
  captcha: { endpoints: captcha, imports: captchaImports },
  check: { endpoints: check, imports: checkImports },
  cms: { endpoints: cms, imports: cmsImports },
  login: { endpoints: login, imports: loginImports },
  register: { endpoints: register, imports: registerImports },
  admission_event: {
    endpoints: admission_event,
    imports: admissionEventImports,
  },
  upload: { endpoints: upload, imports: uploadImports },
  ecommerce: { endpoints: ecommerce, imports: ecommerceImports },
  forgot: { endpoints: forgot, imports: forgotPasswordImports },
  reset: { endpoints: reset, imports: resetPasswordImports },
  
};

export const fetchLambdaEndpoint = (file) => {
  return lambda[file];
};

export const lambdas = [
  "analytics",
  "apple_login",
  "google_login",
  "blog",
  "captcha",
  "check",
  "cms",
  "login",
  "register",
  "admission_event",
  "upload",
  "ecommerce",
  "forgot",
  "reset",

];
