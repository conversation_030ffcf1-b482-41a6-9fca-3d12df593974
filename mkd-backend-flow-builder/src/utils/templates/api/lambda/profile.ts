export const profile = [

    {
        id: 'profileLambda',
        name: 'Profile Lambda',
        method: 'GET',
        description: '',
        route: '/v3/api/custom/lambda/profile',
        inputs: [],
        response: [
          { id: 'id', key: 'id', value: 'result[0].id', valueType: 'String', parent: '' },
          { id: 'first_name', key: 'first_name', value: 'result[0].first_name', valueType: 'String', parent: '' },
          { id: 'email', key: 'email', value: 'result[0].email', valueType: 'String', parent: '' },
          { id: 'role', key: 'role', value: 'result[0].role', valueType: 'String', parent: '' },
          { id: 'last_name', key: 'last_name', value: 'result[0].last_name', valueType: 'String', parent: '' },
          { id: 'phone', key: 'phone', value: 'result[0].phone ?? ""', valueType: 'String', parent: '' },
          { id: 'photo', key: 'photo', value: 'result[0].photo ?? ""', valueType: 'String', parent: '' }
        ],
        code: `app.get("/v3/api/custom/lambda/profile", middlewares, async function (req, res) {
          try {
            let sdk = req.sdk;
            sdk.getDatabase();
            sdk.setProjectId(req.projectId);
            sdk.setTable("user");
            console.log(req.user_id);
            const result = await sdk.get({
              id: req.user_id
            });
      
            if (typeof result == "string") {
              return res.status(403).json({
                error: true,
                message: result
              });
            }
      
            if (result.length != 1) {
              return res.status(401).json({
                error: true,
                message: "Invalid Credentials"
              });
            }
      
            return res.status(200).json({
              id: result[0].id,
              first_name: result[0].first_name,
              email: result[0].email,
              role: result[0].role,
              last_name: result[0].last_name,
              phone: result[0].phone ?? "",
              photo: result[0].photo ?? ""
            });
          } catch (err) {
            res.status(403);
            res.json({
              error: true,
              message: err.message
            });
          }
        });`,
        doc: '',
        unit: '',
        protected: true,
        authorizedRoles: [],
        imports: ``,
        type: 'lambda',
        group: 'profile'
      },
      {
        id: 'profileUpdateLambda',
        name: 'Profile Update Lambda',
        method: 'POST',
        description: '',
        route: '/v3/api/custom/lambda/profile',
        inputs: [
          { name: 'payload.first_name', type: 'body', rules: '', dataType: 'String' },
          { name: 'payload.last_name', type: 'body', rules: '', dataType: 'String' },
          { name: 'payload.photo', type: 'body', rules: '', dataType: 'String', defaultValue: 'null' }
        ],
        response: [
          { id: 'error', key: 'error', value: 'false', valueType: 'Boolean', parent: '' },
          { id: 'message', key: 'message', value: 'Updated', valueType: 'String', parent: '' }
        ],
        code: `app.post("/v3/api/custom/lambda/profile", middlewares, async function (req, res) {
          try {
            let sdk = req.sdk;
            sdk.getDatabase();
            sdk.setProjectId(req.projectId);
            sdk.setTable("user");
            const result = await sdk.get({
              id: req.user_id
            });
      
            if (typeof result == "string") {
              return res.status(403).json({
                error: true,
                message: result
              });
            }
      
            if (result.length != 1) {
              return res.status(401).json({
                error: true,
                message: "Invalid Credentials"
              });
            }
      
            const updateResult = await sdk.update(
              {
                first_name: req.body.payload.first_name,
                last_name: req.body.payload.last_name,
                photo: req.body.payload.photo ?? null
              },
              req.user_id
            );
      
            if (updateResult == null) {
              return res.status(403).json({
                error: true,
                message: updateResult
              });
            }
      
            return res.status(200).json({
              error: false,
              message: "Updated"
            });
          } catch (err) {
            res.status(403);
            res.json({
              error: true,
              message: err.message
            });
          }
        });`,
        doc: '',
        unit: '',
        protected: true,
        authorizedRoles: [],
        imports: ``,
        type: 'lambda',
        group: 'profile'
      }
      
      
]

export const profileImports = `

const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const TokenMiddleware = require("../../../middleware/TokenMiddleware");
const PermissionMiddleware = require("../../../middleware/PermissionMiddleware");

const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware(),
  PermissionMiddleware
  // RateLimitMiddleware,
  // LogMiddleware,
  // UsageMiddleware
  // CheckProjectMiddleware,
  // AnalyticMiddleware,
  // RoleMiddleware
];


`