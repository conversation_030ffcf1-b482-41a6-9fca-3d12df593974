export const cms = [
    {
        id: 'createCmsLambda',
        name: 'Create CMS Lambda',
        method: 'POST',
        description: '',
        route: '/v3/api/custom/lambda/cms',
        inputs: [
          { name: 'page', type: 'body', rules: 'required', dataType: 'String' },
          { name: 'key', type: 'body', rules: 'required', dataType: 'String' },
          { name: 'type', type: 'body', rules: 'required', dataType: 'String' },
          { name: 'value', type: 'body', rules: 'required', dataType: 'String' }
        ],
        response: [
          { id: 'error', key: 'error', value: 'false', valueType: 'Boolean', parent: '' },
          { id: 'message', key: 'message', value: 'insertResult', valueType: 'String', parent: '' }
        ],
        code: `app.post("/v3/api/custom/lambda/cms", middlewares, async function (req, res) {
          try {
            let client = req.app.get("subscriber");
            let sdk = req.sdk;
            sdk.setProjectId(req.projectId);
            sdk.setTable("cms");
      
            if (!req.body.page) {
              return res.status(403).json({
                error: true,
                message: "Page Missing",
                validation: { page: "page missing" },
              });
            }
      
            if (!req.body.key) {
              return res.status(403).json({
                error: true,
                message: "Key Missing",
                validation: { key: "key missing" },
              });
            }
      
            if (!req.body.type) {
              return res.status(403).json({
                error: true,
                message: "Type Missing",
                validation: { type: "type missing" },
              });
            }
      
            if (!req.body.value) {
              return res.status(403).json({
                error: true,
                message: "Value Missing",
                validation: { value: "value missing" },
              });
            }
      
            const insertResult = await sdk.insert({
              page: req.body.page,
              content_type: req.body.type,
              content_key: req.body.key,
              content_value: req.body.value,
              create_at: sqlDateFormat(new Date()),
              update_at: sqlDateTimeFormat(new Date()),
            });
      
            const getAllResult = await sdk.get({});
      
            client.set("cms-" + req.projectId, JSON.stringify(getAllResult), "EX", 60 * 60, function (data) {
              console.log("SET", data);
            });
      
            const getAllPageResult = await sdk.get({
              page: req.body.page,
            });
      
            client.set("cms-" + req.projectId + "-" + req.body.page, JSON.stringify(getAllPageResult), "EX", 60 * 60, function (data) {
              console.log("SET", data);
            });
      
            const getAllPageKeyResult = await sdk.get({
              page: req.body.page,
              content_key: req.body.key,
            });
      
            client.set("cms-" + req.projectId + "-" + req.body.page + "-" + req.body.key, JSON.stringify(getAllPageKeyResult), "EX", 60 * 60, function (data) {
              console.log("SET", data);
            });
      
            return res.status(200).json({
              error: false,
              message: insertResult,
            });
          } catch (error) {
            console.log(error);
            return res.status(403).json({
              error: true,
              message: "Something went wrong",
            });
          }
        });`,
        doc: '',
        unit: '',
        protected: true,
        authorizedRoles: [],
        imports: `const TokenMiddleware = require("../../../middleware/TokenMiddleware");`,
        type: 'lambda',
        group: 'cms'
      },
      {
        id: 'updateCmsLambda',
        name: 'Update CMS Lambda',
        method: 'PUT',
        description: 'Update a CMS record',
        route: '/v3/api/custom/lambda/cms/:id',
        inputs: [
          { name: 'id', type: 'path', rules: 'required', dataType: 'String' },
          { name: 'page', type: 'body', rules: 'required', dataType: 'String' },
          { name: 'key', type: 'body', rules: 'required', dataType: 'String' },
          { name: 'type', type: 'body', rules: 'required', dataType: 'String' },
          { name: 'value', type: 'body', rules: 'required', dataType: 'String' }
        ],
        response: [
          { id: 'error', key: 'error', value: 'false', valueType: 'Boolean', parent: '' },
          { id: 'message', key: 'message', value: 'updateResult', valueType: 'String', parent: '' }
        ],
        code: `app.put("/v3/api/custom/lambda/cms/:id", middlewares, async function (req, res) {
          try {
            let client = req.app.get("subscriber");
            let sdk = req.sdk;
            sdk.setProjectId(req.projectId);
            sdk.setTable("cms");
      
            if (!req.body.page) {
              return res.status(403).json({
                error: true,
                message: "Page Missing",
                validation: { page: "page missing" },
              });
            }
      
            if (!req.body.key) {
              return res.status(403).json({
                error: true,
                message: "Key Missing",
                validation: { key: "key missing" },
              });
            }
      
            if (!req.body.type) {
              return res.status(403).json({
                error: true,
                message: "Type Missing",
                validation: { type: "type missing" },
              });
            }
      
            if (!req.body.value) {
              return res.status(403).json({
                error: true,
                message: "Value Missing",
                validation: { value: "value missing" },
              });
            }
      
            const updateResult = await sdk.update(
              {
                page: req.body.page,
                content_type: req.body.type,
                content_key: req.body.key,
                content_value: req.body.value,
                update_at: sqlDateTimeFormat(new Date()),
              },
              req.params.id
            );
      
            const getAllResult = await sdk.get({});
      
            client.set("cms-" + req.projectId, JSON.stringify(getAllResult), "EX", 60 * 60, function (data) {
              console.log("SET", data);
            });
      
            const getAllPageResult = await sdk.get({
              page: req.body.page,
            });
      
            client.set("cms-" + req.projectId + "-" + req.body.page, JSON.stringify(getAllPageResult), "EX", 60 * 60, function (data) {
              console.log("SET", data);
            });
      
            const getAllPageKeyResult = await sdk.get({
              page: req.body.page,
              content_key: req.body.key,
            });
      
            client.set("cms-" + req.projectId + "-" + req.body.page + "-" + req.body.key, JSON.stringify(getAllPageKeyResult), "EX", 60 * 60, function (data) {
              console.log("SET", data);
            });
      
            return res.status(200).json({
              error: false,
              message: updateResult,
            });
          } catch (error) {
            return res.status(403).json({
              error: true,
              message: "Something went wrong",
            });
          }
        });`,
        doc: '',
        unit: '',
        protected: true,
        authorizedRoles: [],
        imports: ``,
        type: 'lambda',
        group: 'cms'
      },
      {
        id: 'deleteCmsLambda',
        name: 'Delete CMS Lambda',
        method: 'DELETE',
        description: 'Delete CMS record',
        route: '/v3/api/custom/lambda/cms/:id',
        inputs: [
          { name: 'id', type: 'path', rules: 'required', dataType: 'String' }
        ],
        response: [
          { id: 'error', key: 'error', value: 'false', valueType: 'Boolean', parent: '' },
          { id: 'message', key: 'message', value: 'deleted', valueType: 'String', parent: '' }
        ],
        code: `app.delete("/v3/api/custom/lambda/cms/:id", middlewares, async function (req, res) {
          try {
            let sdk = req.sdk;
            sdk.setProjectId(req.projectId);
            sdk.setTable("cms");
      
            if (!req.params.id) {
              return res.status(403).json({
                error: true,
                message: "ID Missing",
                validation: { id: "id missing" },
              });
            }
      
            await sdk.delete({}, req.params.id);
      
            return res.status(200).json({
              error: false,
              message: "deleted",
            });
          } catch (error) {
            return res.status(403).json({
              error: true,
              message: "Something went wrong",
            });
          }
        });`,
        doc: '',
        unit: '',
        protected: true,
        authorizedRoles: [],
        imports: ``,
        type: 'lambda',
        group: 'cms'
      },

      {
        id: 'getCmsByIdLambda',
        name: 'Get CMS by ID Lambda',
        method: 'GET',
        description: '',
        route: '/v3/api/custom/lambda/cms/id/:id',
        inputs: [
          { name: 'id', type: 'path', rules: 'required', dataType: 'String' }
        ],
        response: [
          { id: 'error', key: 'error', value: 'false', valueType: 'Boolean', parent: '' },
          { id: 'model', key: 'model', value: 'result[0]', valueType: 'Object', parent: '' }
        ],
        code: `app.get("/v3/api/custom/lambda/cms/id/:id", publicMiddlewares, async function (req, res) {
          try {
            let sdk = req.sdk;
            sdk.setProjectId(req.projectId);
            sdk.setTable("cms");
      
            if (!req.params.id) {
              return res.status(403).json({
                error: true,
                message: "ID Missing",
                validation: { id: "id missing" },
              });
            }
      
            const result = await sdk.get({ id: req.params.id });
      
            if (result.length > 0) {
              return res.status(200).json({
                error: false,
                model: result[0],
              });
            } else {
              return res.status(200).json({
                error: false,
                model: null,
              });
            }
          } catch (error) {
            return res.status(403).json({
              error: true,
              message: "Something went wrong",
            });
          }
        });`,
        doc: '',
        unit: '',
        protected: false,
        authorizedRoles: [],
        imports: ``,
        type: 'lambda',
        group: 'cms'
      },
      {
        id: 'getCmsByPageAndKeyLambda',
        name: 'Get CMS by Page and Key Lambda',
        method: 'GET',
        description: '',
        route: '/v3/api/custom/lambda/cms/page/:page/:key',
        inputs: [
          { name: 'page', type: 'path', rules: 'required', dataType: 'String' },
          { name: 'key', type: 'path', rules: 'required', dataType: 'String' }
        ],
        response: [
          { id: 'error', key: 'error', value: 'false', valueType: 'Boolean', parent: '' }
        ],
        code: `app.get("/v3/api/custom/lambda/cms/page/:page/:key", publicMiddlewares, async function (req, res) {
          try {
            let sdk = req.sdk;
            sdk.setProjectId(req.projectId);
            sdk.setTable("cms");
      
            let client = req.app.get("subscriber");
            const getall = await client.get("cms-" + req.projectId + "-" + req.params.page + "-" + req.params.key);
      
            if (getall) {
              return res.status(200).send(getall);
            }
      
            const getAllResult = await sdk.get({
              page: req.params.page,
              content_key: req.params.key,
            });
      
            client.set("cms-" + req.projectId + "-" + req.params.page + "-" + req.params.key, JSON.stringify(getAllResult), "EX", 60 * 60, function (data) {
              console.log("SET", data);
            });
      
            return res.status(200).send(getAllResult);
          } catch (error) {
            console.log("ERROR: ", error);
            return res.status(403).json({
              error: true,
              message: "Something went wrong",
            });
          }
        });`,
        doc: '',
        unit: '',
        protected: false,
        authorizedRoles: [],
        imports: ``,
        type: 'lambda',
        group: 'cms'
      },
      {
        id: 'getCmsByPageLambda',
        name: 'Get CMS by Page Lambda',
        method: 'GET',
        description: 'Get all CMS records for a particular page',
        route: '/v3/api/custom/lambda/cms/page/:page',
        inputs: [
          { name: 'page', type: 'path', rules: 'required', dataType: 'String' }
        ],
        response: [
          { id: 'error', key: 'error', value: 'false', valueType: 'Boolean', parent: '' }
        ],
        code: `app.get("/v3/api/custom/lambda/cms/page/:page", publicMiddlewares, async function (req, res) {
          try {
            let sdk = req.sdk;
            sdk.setProjectId(req.projectId);
            sdk.setTable("cms");
      
            let client = req.app.get("subscriber");
            const getall = await client.get("cms-" + req.projectId + "-" + req.params.page);
            if (getall) {
              return res.status(200).send(getall);
            }
      
            const getAllResult = await sdk.get({
              page: req.params.page,
            });
      
            client.set("cms-" + req.projectId + "-" + req.params.page, JSON.stringify(getAllResult), "EX", 60 * 60, function (data) {
              console.log("SET", data);
            });
      
            return res.status(200).send(getAllResult);
          } catch (error) {
            console.log("ERROR: ", error);
            return res.status(403).json({
              error: true,
              message: "Something went wrong",
            });
          }
        });`,
        doc: '',
        unit: '',
        protected: false,
        authorizedRoles: [],
        imports: ``,
        type: 'lambda',
        group: 'cms'
      },
      {
        id: 'getAllCmsLambda',
        name: 'Get All CMS Lambda',
        method: 'GET',
        description: 'Get all CMS records',
        route: '/v3/api/custom/lambda/cms/all',
        inputs: [],
        response: [
          { id: 'error', key: 'error', value: 'false', valueType: 'Boolean', parent: '' }
        ],
        code: `app.get("/v3/api/custom/lambda/cms/all", publicMiddlewares, async function (req, res) {
          try {
            let sdk = req.sdk;
            sdk.setProjectId(req.projectId);
            sdk.setTable("cms");
      
            let client = req.app.get("subscriber");
            const getall = await client.get("cms-" + req.projectId);
            if (getall) {
              return res.status(200).send(getall);
            }
      
            const getAllResult = await sdk.get({});
      
            client.set("cms-" + req.projectId, JSON.stringify(getAllResult), "EX", 60 * 60, function (data) {
              console.log("SET", data);
            });
      
            return res.status(200).send(getAllResult);
          } catch (error) {
            console.log("ERROR: ", error);
            return res.status(403).json({
              error: true,
              message: "Something went wrong",
            });
          }
        });`,
        doc: '',
        unit: '',
        protected: false,
        authorizedRoles: [],
        imports: ``,
        type: 'lambda',
        group: 'cms'
      }
      
      
      
      
      
      
      
]

export const cmsImports = `

const ProjectMiddleware = require("/../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const TokenMiddleware = require("../../../middleware/TokenMiddleware");
const { sqlDateFormat, sqlDateTimeFormat } = require("../../../services/UtilService");

const middlewares = [ProjectMiddleware, UrlMiddleware, HostMiddleware, TokenMiddleware()];
const publicMiddlewares = [ProjectMiddleware, UrlMiddleware, HostMiddleware];

const config = require("../../../config");

`