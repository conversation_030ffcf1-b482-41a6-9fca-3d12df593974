export const columnTypes = [
    "string",
    "textarea",
    "editor",
    "number",
    "decimal",
    "image",
    "video",
    "file",
    "date",
    "datetime"

]

export const modelBasicDataTypes = [
    "string",
    "number",
    "decimal",
    "image",
    "file",
    "date",
    "datetime"

]

export const DBTypeToColumnType = (type) => {
    switch (type) {
        case "varchar":
            return "string";
          case "text":
            return "textarea";
          case "mediumtext":
            return "textarea";
          case "longtext":
            return "textarea";
          case "tinyint":
            return "number";
          case "int":
            return "number";
          case "bigint":
            return "number";
          case "float":
            return "decimal";
          case "double":
            return "decimal";
          case "image":
            return "image";
          case "file":
            return "file";
          case "date":
            return "date";
          case "datetime":
            return "datetime";
          default:
            return type;
    }
}

export const convertToMYSQLType = ( value, defaultAsInput = false ) => {
  switch ( value ) {
    case "varchar":
    case "string":
      return "VARCHAR(255)";
    case "text":
    case "textarea":
      return "TEXT";
    case "mediumtext":
      return "MEDIUMTEXT";
    case "longtext":
      return "LONGTEXT";
    case "tinyint":
      return "TINYINT(1)";
    case "int":
    case "number":
      return "INT(11)";
    case "bigint":
      return "BIGINT(20)";
    case "float":
      return "FLOAT";
    case "double":
    case "decimal":
      return "DECIMAL(10,2)";
    case "image":
     return "IMAGE";
    case "video":
      return "VIDEO";
    case "editor":
      return "LONGTEXT";
    case "file":
      return "FILE";
    case "date":
      return "DATE";
    case "datetime":
      return "DATETIME";
    default:
      return defaultAsInput ? value : "VARCHAR(255)";
  }
};

export const reverseAttributeType = ( value ) => {
  switch ( value ) {
    case "VARCHAR(255)":
      return "string";
    case "TEXT":
      return "text";
    case "MEDIUMTEXT":
      return "mediumtext";
    case "LONGTEXT":
      return "longtext";
    case "TINYINT(1)":
      return "boolean";
    case "INT(11)":
      return "int";
    case "BIGINT(20)":
      return "bigint";
    case "FLOAT":
      return "float";
    case "DECIMAL(10,2)":
      return "double";
    case "IMAGE":
      return "image";
    case "FILE":
      return "file";
    case "DATE":
      return "date";
    case "DATETIME":
      return "datetime";
    default:
      return "";
  }
};

