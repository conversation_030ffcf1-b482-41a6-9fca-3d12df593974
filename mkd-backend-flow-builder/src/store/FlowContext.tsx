/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable no-unsafe-optional-chaining */
import React, {
  createContext,
  useContext,
  ReactNode,
  useReducer,
  act
} from "react";
import { Node, Edge } from "reactflow";

export interface Model {
  id: string;
  name: string;
  fields: {
    name: string;
    type: string;
    defaultValue: string;
    validation: string;
    mapping?: string;
  }[];
}

export interface Role {
  id: string;
  name: string;
  slug: string;
  permissions: {
    routes: string[];
    canCreateUsers?: boolean;
    canEditUsers?: boolean;
    canDeleteUsers?: boolean;
    canManageRoles?: boolean;
    canUpdateOtherUsers: boolean;
  };
}

export interface Route {
  id: string;
  name: string;
  method: string;
  url: string;
  flowData?: {
    nodes: any[];
    edges: any[];
  };
}

export interface Settings {
  id: string;
  globalKey: string;
  databaseType: string;
  authType: string;
  timezone: string;
  dbHost: string;
  dbPort: string;
  dbUser: string;
  dbPassword: string;
  dbName: string;
  isPWA: boolean;
  isMultiTenant: boolean;
  model_namespace: string;
  payment_option: string;
}

export interface FlowState {
  nodes: Node[];
  edges: Edge[];
  selectedNode: Node | null;
  models: Model[];
  roles: Role[];
  routes: Route[];
  activeRoute: Route;
  settings: Settings;
  defaultTablesShown: boolean;
}

export interface Action {
  type: string;
  payload?: any;
}

export interface FlowProviderInterface {
  state: FlowState;
  dispatch: React.DispatchWithoutAction;
  setNodes: (nodes: Node[] | ((prev: Node[]) => Node[])) => void;
  setEdges: (edges: Edge[] | ((prev: Edge[]) => Edge[])) => void;
  setSelectedNode: (node: Node | null) => void;
  updateNodeData: (nodeId: string, newData: any) => void;
  addModel: (model: Model) => void;
  updateModel: (model: Model) => void;
  updateModels: (models: Model[]) => void;
  addRole: (role: Role) => void;
  updateRole: (role: Role) => void;
  updateRoles: (roles: Role[]) => void;
  deleteRole: (roleId: string) => void;
  addRoute: (route: Route) => void;
  updateRoute: (route: Route) => void;
  updateRoutes: (routes: Route[]) => void;
  setActiveRoute: (route: Route) => void;
  deleteRoute: (routeId: string) => void;
  updateSettings: (settings: Settings) => void;
  setDefaultTablesShown: (shown: boolean) => void;
  updateNode: (nodeId: string, newData: any) => void;
}

const initialState = {
  nodes: [],
  edges: [],
  selectedNode: null,
  models: [],
  roles: [],
  routes: [],
  activeRoute: null,
  settings: {
    globalKey: `key_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    databaseType: "mysql",
    authType: "session",
    timezone: "UTC",
    dbHost: "localhost",
    dbPort: "3306", // normal MySQL port
    dbUser: "root",
    dbPassword: "root",
    dbName: `database_${new Date().toISOString().split("T")[0]}`, // today's date
    id: `project_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    isPWA: false,
    isMultiTenant: false,
    model_namespace: `namespace_${Date.now()}_${Math.random()
      .toString(36)
      .substr(2, 9)}`,
    payment_option: "none"
  },
  defaultTablesShown: false
};
const FlowContext = createContext<FlowProviderInterface | undefined>(
  initialState
);

const reducer = (state: FlowState, action: Action): FlowState => {
  switch (action.type) {
    case "SET_NODES": {
      // console.log("action", action);
      const activeRoute = {
        ...state?.activeRoute,
        flowData: {
          ...state?.activeRoute?.flowData,
          nodes: [...action.payload]
        }
      };
      const routes = state?.routes?.map((route: { id: any }) =>
        route.id == activeRoute?.id
          ? {
              ...route,
              ...activeRoute
            }
          : route
      );
      // console.log("activeRoute", activeRoute);
      // console.log("routes", routes);
      return { ...state, activeRoute, routes, nodes: action.payload };
    }
    case "SET_EDGES": {
      const activeRoute = {
        ...state?.activeRoute,
        flowData: {
          ...state?.activeRoute?.flowData,
          edges: [...action.payload]
        }
      };
      const routes = state?.routes?.map((route: { id: any }) =>
        route.id == activeRoute?.id
          ? {
              ...route,
              ...activeRoute
            }
          : route
      );
      return { ...state, activeRoute, routes, edges: action.payload };
    }
    case "SET_SELECTED_NODE":
      return { ...state, selectedNode: action.payload };
    case "ADD_MODEL":
      return { ...state, models: [...state.models, action.payload] };
    case "UPDATE_MODEL":
      return {
        ...state,
        models: state.models.map((m: { id: any }) =>
          m.id === action.payload.id ? action.payload : m
        )
      };
    case "DELETE_MODEL":
      return {
        ...state,
        models: state.models.filter((m: { id: any }) => m.id !== action.payload)
      };
    case "ADD_ROLE":
      return { ...state, roles: [...state.roles, action.payload] };
    case "UPDATE_ROLE":
      return {
        ...state,
        roles: state.roles.map((r: { id: any }) =>
          r.id === action.payload.id ? action.payload : r
        )
      };
    case "DELETE_ROLE":
      return {
        ...state,
        roles: state.roles.filter((r: { id: any }) => r.id !== action.payload)
      };
    case "ADD_ROUTE":
      return { ...state, routes: [...state.routes, action.payload] };
    case "SET_ACTIVE_ROUTE":
      return { ...state, activeRoute: { ...action.payload } };
    case "UPDATE_ROUTE":
      return {
        ...state,
        routes: state.routes.map((r: { id: any }) =>
          r.id === action.payload.id ? action.payload : r
        )
      };
    case "DELETE_ROUTE":
      return {
        ...state,
        routes: state.routes.filter((r: { id: any }) => r.id !== action.payload)
      };
    case "UPDATE_SETTINGS":
      return { ...state, settings: action.payload };
    case "SET_DEFAULT_TABLES_SHOWN":
      return { ...state, defaultTablesShown: action.payload };
    case "UPDATE_NODE": {
      // console.log("action", action);
      const selectedNode = {
        ...state?.nodes?.find(
          (node: { id: any }) => node.id === action.payload.id
        ),
        data: {
          ...state?.nodes?.find(
            (node: { id: unknown }) => node.id === action.payload.id
          )?.data,
          ...action.payload.data
        }
      };
      const activeRoute = {
        ...state?.activeRoute,
        flowData: {
          ...state?.activeRoute?.flowData,
          nodes: [...state?.activeRoute?.flowData?.nodes, selectedNode]
        }
      };
      const routes = state?.routes?.map((route: { id: any }) =>
        route.id === activeRoute?.id
          ? {
              ...route,
              ...activeRoute
            }
          : route
      );
      // console.log("activeRoute", activeRoute);
      // console.log("routes", routes);
      return {
        ...state,
        selectedNode,
        activeRoute,
        routes,
        nodes: state?.nodes?.map((node: { id: any; data: any }) =>
          node.id === action.payload.id
            ? {
                ...node,
                data: {
                  ...node.data,
                  ...action.payload.data
                }
              }
            : node
        )
      };
    }
    case "REMOVE_NODE":
      return {
        ...state,
        nodes: state.nodes.filter((node) => node.id !== action.payload.id),
        edges: state.edges.filter(
          (edge) =>
            edge.source !== action.payload.id &&
            edge.target !== action.payload.id
        )
      };
    case "UPDATE_MODELS":
      return { ...state, models: action.payload };
    case "UPDATE_ROLES":
      return { ...state, roles: action.payload };
    case "UPDATE_ROUTES":
      return { ...state, routes: action.payload };
    default:
      return state;
  }
};

export const FlowProvider = ({ children }: { children: ReactNode }) => {
  const [state, dispatch] = useReducer(reducer, initialState);

  const setNodes = (nodes: Node[] | ((prev: Node[]) => Node[])) => {
    const updatedNodes =
      typeof nodes === "function" ? nodes(state.nodes) : nodes;
    dispatch({ type: "SET_NODES", payload: updatedNodes });
  };

  const setEdges = (edges: Edge[] | ((prev: Edge[]) => Edge[])) => {
    const updatedEdges =
      typeof edges === "function" ? edges(state.edges) : edges;
    dispatch({ type: "SET_EDGES", payload: updatedEdges });
  };

  const setSelectedNode = (node: Node | null) => {
    console.log("node", node);
    dispatch({ type: "SET_SELECTED_NODE", payload: node });
  };

  const addModel = (model: Model) => {
    dispatch({ type: "ADD_MODEL", payload: model });
  };

  const updateModel = (model: Model) => {
    dispatch({ type: "UPDATE_MODEL", payload: model });
  };

  const deleteModel = (modelId: string) => {
    dispatch({ type: "DELETE_MODEL", payload: modelId });
  };

  const addRole = (role: Role) => {
    dispatch({ type: "ADD_ROLE", payload: role });
  };

  const updateRole = (role: Role) => {
    dispatch({ type: "UPDATE_ROLE", payload: role });
  };

  const deleteRole = (roleId: string) => {
    dispatch({ type: "DELETE_ROLE", payload: roleId });
  };

  const addRoute = (route: Route) => {
    dispatch({ type: "ADD_ROUTE", payload: route });
  };

  const updateRoute = (route: Route) => {
    dispatch({ type: "UPDATE_ROUTE", payload: route });
  };
  const setActiveRoute = (route: Route) => {
    dispatch({ type: "SET_ACTIVE_ROUTE", payload: route });
  };

  const deleteRoute = (routeId: string) => {
    dispatch({ type: "DELETE_ROUTE", payload: routeId });
  };

  const updateSettings = (settings: Settings) => {
    dispatch({ type: "UPDATE_SETTINGS", payload: settings });
  };

  const setDefaultTablesShown = (shown: boolean) => {
    dispatch({ type: "SET_DEFAULT_TABLES_SHOWN", payload: shown });
  };

  const updateNode = (nodeId: string, newData: any) => {
    dispatch({ type: "UPDATE_NODE", payload: { id: nodeId, data: newData } });
  };

  const updateModels = (models: Model[]) => {
    dispatch({ type: "UPDATE_MODELS", payload: models });
  };

  const updateRoles = (roles: Role[]) => {
    dispatch({ type: "UPDATE_ROLES", payload: roles });
  };

  const updateRoutes = (routes: Route[]) => {
    dispatch({ type: "UPDATE_ROUTES", payload: routes });
  };

  return (
    <FlowContext.Provider
      value={{
        state,
        dispatch,
        setNodes,
        setEdges,
        setSelectedNode,
        addModel,
        updateModel,
        updateModels,
        deleteModel,
        addRole,
        updateRole,
        updateRoles,
        deleteRole,
        addRoute,
        updateRoute,
        updateRoutes,
        setActiveRoute,
        deleteRoute,
        updateSettings,
        setDefaultTablesShown,
        updateNode
      }}
    >
      {children}
    </FlowContext.Provider>
  );
};

export const useFlowContext = () => {
  const context = useContext(FlowContext);
  if (!context) {
    throw new Error("useFlowContext must be used within a FlowProvider");
  }
  return context;
};
