import React, { useState } from "react";
import { Plus, Trash2, Code, CopyIcon } from "lucide-react";
import { RouteModal } from "@/components/RouteModal";
import { RouteFlowEditor } from "@/components/RouteFlowEditor";
import { useFlowContext, Route } from "@/store/FlowContext";

export default function RoutesPanel() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedRoute, setSelectedRoute] = useState<any>(null);
  const [editingRoute, setEditingRoute] = useState<any>(null);
  const {
    state: { routes },
    setNodes,
    setEdges,
    setActiveRoute,
    deleteRoute,
    addRoute,
  } = useFlowContext();

  const onEditingRoute = (route: any) => {
    setEditingRoute(route);
    if (route.flowData) {
      setNodes(route.flowData.nodes);
      setEdges(route.flowData.edges);
    } else {
      // Create default URL node for new routes
      const defaultNode = {
        id: `node_${Date.now()}`,
        type: route.type,
        position: { x: 100, y: 100 },
        data: {
          label: "URL",
          fields: [],
          path: route.url,
          queryFields: [],
          method: route.method,
        },
      };
      setNodes([defaultNode]);
      setActiveRoute(route);
      setEdges([]);
    }
  };

  const handleDeleteRoute = (routeId: string) => {
    deleteRoute(routeId);
  };

  const duplicateRoute = (route: Route) => {
    const duplicateRoute = {
      ...route,
      id: `route_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: `${route.name} (Copy)`,
    }
    addRoute(duplicateRoute)
    setSelectedRoute(duplicateRoute)
    setIsModalOpen(true)

    // console.log("route >>", route);
  };

  return (
    <div className="flex flex-col h-full">
      {!editingRoute ? (
        <>
          <div className="p-4">
            <button
              onClick={() => {
                setSelectedRoute(null);
                setIsModalOpen(true);
              }}
              className="flex gap-2 justify-center items-center px-4 py-2 w-full text-white bg-blue-500 rounded hover:bg-blue-600"
            >
              <Plus className="w-4 h-4" />
              Add Route
            </button>
          </div>

          <div className="overflow-y-auto flex-1 px-4">
            <div className="space-y-3">
              {routes.map((route) => (
                <div
                  key={route.id}
                  className="p-4 space-y-2 bg-white rounded-lg border transition-shadow hover:shadow-md"
                >
                  <div className="grid grid-cols-[1fr_auto] gap-2 justify-between items-start">
                    <div className="w-full min-w-full max-w-full truncate">
                      <h3 title={route.flowData.nodes[0].data.apiname ? route.flowData.nodes[0].data.apiname : route.name} className="font-medium truncate text-ellipsis">{route.flowData.nodes[0].data.apiname ? route.flowData.nodes[0].data.apiname : route.name}</h3>
                      
                    </div>
                    <div className="flex gap-2">
                      <button
                        onClick={() => duplicateRoute(route)}
                        className="p-1 text-blue-600 rounded hover:bg-red-100"
                        title="Delete Route"
                      >
                        <CopyIcon className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteRoute(route.id)}
                        className="p-1 text-red-600 rounded hover:bg-red-100"
                        title="Delete Route"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => {
                          onEditingRoute(route);
                        }}
                        className="p-1 rounded hover:bg-gray-100"
                        title="Edit Components"
                      >
                        <Code className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                    {route.flowData?.nodes[0]?.data && (
                        <p title={route.flowData.nodes[0].data.method + " " + route.flowData.nodes[0].data.path} className="text-sm text-gray-500 truncate">
                          {route.flowData.nodes[0].data.method}{" "}
                          {route.flowData.nodes[0].data.path}
                        </p>
                      )}
                </div>
              ))}
            </div>
          </div>

          <RouteModal
            isOpen={isModalOpen}
            onClose={() => {
              setIsModalOpen(false);
              setSelectedRoute(null);
            }}
            route={selectedRoute}
          />
        </>
      ) : (
        <div className="fixed inset-0 bg-white">
          <RouteFlowEditor
            route={editingRoute}
            onClose={() => setEditingRoute(null)}
          />
        </div>
      )}
    </div>
  );
}
