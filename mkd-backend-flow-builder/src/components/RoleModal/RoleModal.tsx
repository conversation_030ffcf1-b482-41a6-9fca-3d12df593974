import React, { useState, useEffect } from "react";
import { X, Trash2 } from "lucide-react";
import { useFlowContext } from "@/store/FlowContext";

interface Route {
  id: string;
  name: string;
  flowData: {
    nodes: {
      data: {
        method: string;
        path: string;
      };
    }[];
    edges: any[];
  };
}

interface Permissions {
  routes: string[];
  canCreateUsers: boolean;
  canEditUsers: boolean;
  canDeleteUsers: boolean;
  canManageRoles: boolean;
  canLogin: boolean;
  canRegister: boolean;
  canGoogleLogin: boolean;
  canAppleLogin: boolean;
  canForgot: boolean;
  canReset: boolean;
  canMicrosoftLogin: boolean;
  canMagicLinkLogin: boolean;
  needs2FA: boolean;
  canSetPermissions: boolean;
  canPreference: boolean;
  canVerifyEmail: boolean;
  canUpload: boolean;
  canStripe: boolean;
  canStripeWebhook: boolean;
  canRealTime: boolean;
  canAI: boolean;
  canUpdateEmail: boolean;
  canUpdatePassword: boolean;
  canUpdateOtherUsers: boolean;
  treeql: TreeQLPermissions;
}

interface TreeQLModelPermissions {
  allowed: boolean;
  blacklistedFields: string[];
  operations: {
    get: boolean;
    getOne: boolean;
    getAll: boolean;
    post: boolean;
    put: boolean;
    delete: boolean;
    paginate: boolean;
    join: boolean;
  };
}

interface TreeQLPermissions {
  enabled: boolean;
  models: {
    [modelName: string]: TreeQLModelPermissions;
  };
}

interface RoleModalProps {
  isOpen: boolean;
  onClose: () => void;
  role?: {
    id: string;
    name: string;
    slug: string;
    permissions: Permissions;
  };
}

const createInitialFormData = () => ({
  id: `role_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  name: "",
  slug: "",
  permissions: {
    routes: [] as any[],
    canCreateUsers: false,
    canEditUsers: false,
    canDeleteUsers: false,
    canManageRoles: false,
    canLogin: false,
    canRegister: false,
    canForgot: false,
    canReset: false,
    canGoogleLogin: false,
    canAppleLogin: false,
    canMicrosoftLogin: false,
    canMagicLinkLogin: false,
    canTwitterLogin: false,
    needs2FA: false,
    canSetPermissions: false,
    canPreference: false,
    canVerifyEmail: false,
    canUpload: false,
    canStripe: false,
    canStripeWebhook: false,
    canRealTime: false,
    canAI: false,
    canUpdateEmail: false,
    canUpdatePassword: false,
    canUpdateOtherUsers: false,
    treeql: {
      enabled: false,
      models: {}
    }
  }
});

export default function RoleModal({ isOpen, onClose, role }: RoleModalProps) {
  const { state, addRole, updateRole, deleteRole } = useFlowContext();
  const { routes } = state;

  console.log("Full context state:", state);
  console.log("Routes from state:", routes);

  const [formData, setFormData] = useState<
    typeof createInitialFormData extends () => infer R ? R : never
  >(createInitialFormData());

  useEffect(() => {
    console.log("Routes from context:", routes);
    if (isOpen) {
      if (role) {
        setFormData({
          ...createInitialFormData(),
          ...role,
          permissions: {
            ...createInitialFormData().permissions,
            ...role.permissions,
            routes: role.permissions.routes || []
          }
        });
      } else {
        setFormData(createInitialFormData());
      }
    }
  }, [role, isOpen, routes]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    if (type === "checkbox") {
      if (name.startsWith("permissions.")) {
        const permissionKey = name.split(".")[1];
        setFormData({
          ...formData,
          permissions: {
            ...formData.permissions,
            [permissionKey]: checked
          }
        });
      } else if (name === "treeql") {
        setFormData({
          ...formData,
          permissions: {
            ...formData.permissions,
            treeql: checked
          }
        });
      }
    } else {
      setFormData({
        ...formData,
        [name]: value,
        ...(name === "name" && !role
          ? {
              slug: value
                .toLowerCase()
                .replace(/[^a-z0-9]/g, "-")
                .replace(/-+/g, "-")
                .replace(/^-|-$/g, "")
            }
          : {})
      });
    }
  };

  const handleRouteChange = (routeId: string, checked: boolean) => {
    setFormData({
      ...formData,
      permissions: {
        ...formData.permissions,
        routes: checked
          ? [...formData.permissions.routes, routeId]
          : formData.permissions.routes.filter((id) => id !== routeId)
      }
    });
  };

  const handleSelectAllRoutes = (checked: boolean) => {
    setFormData({
      ...formData,
      permissions: {
        ...formData.permissions,
        routes: checked ? routes.map((route) => route.id) : []
      }
    });
  };

  const handleSave = () => {
    if (role) {
      updateRole(formData);
    } else {
      addRole(formData);
    }
    onClose();
  };

  const handleDelete = () => {
    if (role) {
      deleteRole(role.id);
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className="flex fixed inset-0 z-50 justify-center items-center bg-black bg-opacity-50"
      style={{ marginTop: "0px" }}
    >
      <div className="bg-white rounded-lg w-full max-w-2xl max-h-[80vh] overflow-y-auto">
        <div className="flex justify-between items-center p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold">
            {role ? "Edit Role" : "Add New Role"}
          </h2>
          <button onClick={onClose} className="p-1 rounded hover:bg-gray-100">
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="p-4">
          <div className="space-y-4">
            <div>
              <label className="block mb-1 text-sm font-medium">
                Role Name
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleChange}
                className="p-2 w-full rounded border"
                placeholder="Enter role name"
              />
            </div>

            <div>
              <label className="block mb-1 text-sm font-medium">
                Role Slug
              </label>
              <input
                type="text"
                name="slug"
                value={formData.slug}
                onChange={handleChange}
                className="p-2 w-full rounded border"
                placeholder="role-slug"
                readOnly={!!role}
              />
            </div>

            <div>
              <div className="space-y-3">
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <h4 className="text-sm font-medium">Permissions</h4>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={
                          formData.permissions.routes.length > 0 &&
                          formData.permissions.routes.length ===
                            routes.map((route) => route.id).length
                        }
                        onChange={(e) =>
                          handleSelectAllRoutes(e.target.checked)
                        }
                        className="text-blue-600 rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                      />
                      <span className="ml-2 text-sm">Select All</span>
                    </label>
                  </div>
                  <div className="overflow-y-auto p-2 space-y-2 max-h-40 rounded border">
                    {routes.map((route) => (
                      <label key={route.id} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={formData.permissions.routes.includes(
                            route.id
                          )}
                          onChange={(e) =>
                            handleRouteChange(route.id, e.target.checked)
                          }
                          className="text-blue-600 rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        />
                        <span className="ml-2 text-sm">
                          {route.name} ({route.flowData.nodes[0].data.method}{" "}
                          {route.flowData.nodes[0].data.path})
                        </span>
                      </label>
                    ))}
                  </div>
                </div>
                <div>
                  <ModelPermissionsSection
                    formData={formData}
                    setFormData={setFormData}
                  />
                </div>
                <div className="grid grid-cols-3 gap-0">
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">User Management</h4>
                    <div className="ml-4 space-y-2">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          name="permissions.canCreateUsers"
                          checked={formData.permissions.canCreateUsers}
                          onChange={handleChange}
                          className="text-blue-600 rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        />
                        <span className="ml-2 text-sm">Create Users</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          name="permissions.canEditUsers"
                          checked={formData.permissions.canEditUsers}
                          onChange={handleChange}
                          className="text-blue-600 rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        />
                        <span className="ml-2 text-sm">Edit Users</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          name="permissions.canDeleteUsers"
                          checked={formData.permissions.canDeleteUsers}
                          onChange={handleChange}
                          className="text-blue-600 rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        />
                        <span className="ml-2 text-sm">Delete Users</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          name="permissions.canManageRoles"
                          checked={formData.permissions.canManageRoles}
                          onChange={handleChange}
                          className="text-blue-600 rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        />
                        <span className="ml-2 text-sm">Manage Roles</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          name="permissions.canPreference"
                          checked={formData.permissions.canPreference}
                          onChange={handleChange}
                          className="text-blue-600 rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        />
                        <span className="ml-2 text-sm">Manage Preferences</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          name="permissions.canVerifyEmail"
                          checked={formData.permissions.canVerifyEmail}
                          onChange={handleChange}
                          className="text-blue-600 rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        />
                        <span className="ml-2 text-sm">Verify Email</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          name="permissions.canUpdateEmail"
                          checked={formData.permissions.canUpdateEmail}
                          onChange={handleChange}
                          className="text-blue-600 rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        />
                        <span className="ml-2 text-sm">Update Email</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          name="permissions.canUpdatePassword"
                          checked={formData.permissions.canUpdatePassword}
                          onChange={handleChange}
                          className="text-blue-600 rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        />
                        <span className="ml-2 text-sm">Update Password</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          name="permissions.canUpdateOtherUsers"
                          checked={formData.permissions.canUpdateOtherUsers}
                          onChange={handleChange}
                          className="text-blue-600 rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        />
                        <span className="ml-2 text-sm">Update Other Users</span>
                      </label>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">
                      Authentication Management
                    </h4>
                    <div className="ml-4 space-y-2">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          name="permissions.canLogin"
                          checked={formData.permissions.canLogin}
                          onChange={handleChange}
                          className="text-blue-600 rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        />
                        <span className="ml-2 text-sm">Can Login</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          name="permissions.canRegister"
                          checked={formData.permissions.canRegister}
                          onChange={handleChange}
                          className="text-blue-600 rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        />
                        <span className="ml-2 text-sm">Register</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          name="permissions.canGoogleLogin"
                          checked={formData.permissions.canGoogleLogin}
                          onChange={handleChange}
                          className="text-blue-600 rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        />
                        <span className="ml-2 text-sm">Use Google Login</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          name="permissions.canAppleLogin"
                          checked={formData.permissions.canAppleLogin}
                          onChange={handleChange}
                          className="text-blue-600 rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        />
                        <span className="ml-2 text-sm">Use Apple Login</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          name="permissions.canMicrosoftLogin"
                          checked={formData.permissions.canMicrosoftLogin}
                          onChange={handleChange}
                          className="text-blue-600 rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        />
                        <span className="ml-2 text-sm">
                          Use Microsoft Login
                        </span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          name="permissions.needs2FA"
                          checked={formData.permissions.needs2FA}
                          onChange={handleChange}
                          className="text-blue-600 rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        />
                        <span className="ml-2 text-sm">Requires 2FA</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          name="permissions.canSetPermissions"
                          checked={formData.permissions.canSetPermissions}
                          onChange={handleChange}
                          className="text-blue-600 rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        />
                        <span className="ml-2 text-sm">Set Permissions</span>
                      </label>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">
                      Additional Permissions
                    </h4>
                    <div className="ml-4 space-y-2">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          name="permissions.canUpload"
                          checked={formData.permissions.canUpload}
                          onChange={handleChange}
                          className="text-blue-600 rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        />
                        <span className="ml-2 text-sm">Upload Files</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          name="permissions.canStripe"
                          checked={formData.permissions.canStripe}
                          onChange={handleChange}
                          className="text-blue-600 rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        />
                        <span className="ml-2 text-sm">Use Stripe</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          name="permissions.canStripeWebhook"
                          checked={formData.permissions.canStripeWebhook}
                          onChange={handleChange}
                          className="text-blue-600 rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        />
                        <span className="ml-2 text-sm">
                          Use Stripe Webhooks
                        </span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          name="permissions.canRealTime"
                          checked={formData.permissions.canRealTime}
                          onChange={handleChange}
                          className="text-blue-600 rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        />
                        <span className="ml-2 text-sm">
                          Use Real-time Features
                        </span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          name="permissions.canAI"
                          checked={formData.permissions.canAI}
                          onChange={handleChange}
                          className="text-blue-600 rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        />
                        <span className="ml-2 text-sm">Use AI Features</span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-between p-4 border-t border-gray-200">
          {role && (
            <button
              onClick={handleDelete}
              className="flex gap-2 items-center px-4 py-2 text-red-600 rounded hover:bg-red-50"
            >
              <Trash2 className="w-4 h-4" />
              Delete Role
            </button>
          )}
          <div className="flex gap-2 ml-auto">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 rounded hover:bg-gray-100"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              className="px-4 py-2 text-white bg-blue-500 rounded hover:bg-blue-600"
            >
              {role ? "Update" : "Save"} Role
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

function ModelPermissionsSection({
  formData,
  setFormData
}: {
  formData: ReturnType<typeof createInitialFormData>;
  setFormData: React.Dispatch<
    React.SetStateAction<ReturnType<typeof createInitialFormData>>
  >;
}) {
  const { state } = useFlowContext();
  const models = state.models || [];

  const handleModelToggle = (modelName: string, checked: boolean) => {
    setFormData((prev) => ({
      ...prev,
      permissions: {
        ...prev.permissions,
        treeql: {
          ...prev.permissions.treeql,
          models: {
            ...prev.permissions.treeql.models,
            [modelName]: checked
              ? {
                  allowed: true,
                  blacklistedFields: [],
                  operations: {
                    get: true,
                    getOne: true,
                    getAll: true,
                    post: true,
                    put: true,
                    delete: true,
                    paginate: true,
                    join: true
                  }
                }
              : undefined
          }
        }
      }
    }));
  };

  const handleFieldToggle = (
    modelName: string,
    fieldName: string,
    checked: boolean
  ) => {
    setFormData((prev) => ({
      ...prev,
      permissions: {
        ...prev.permissions,
        treeql: {
          ...prev.permissions.treeql,
          models: {
            ...prev.permissions.treeql.models,
            [modelName]: {
              ...prev.permissions.treeql.models[modelName],
              blacklistedFields: checked
                ? prev.permissions.treeql.models[
                    modelName
                  ].blacklistedFields.filter((f) => f !== fieldName)
                : [
                    ...prev.permissions.treeql.models[modelName]
                      .blacklistedFields,
                    fieldName
                  ]
            }
          }
        }
      }
    }));
  };

  const handleOperationToggle = (
    modelName: string,
    operation: keyof TreeQLModelPermissions["operations"],
    checked: boolean
  ) => {
    setFormData((prev) => ({
      ...prev,
      permissions: {
        ...prev.permissions,
        treeql: {
          ...prev.permissions.treeql,
          models: {
            ...prev.permissions.treeql.models,
            [modelName]: {
              ...prev.permissions.treeql.models[modelName],
              operations: {
                ...prev.permissions.treeql.models[modelName].operations,
                [operation]: checked
              }
            }
          }
        }
      }
    }));
  };

  return (
    <div className="mt-4">
      <div className="flex justify-between items-center w-full">
        <h4 className="text-sm font-medium text-left">Tree QL</h4>
        <div className="ml-auto">
          <input
            type="checkbox"
            name="treeql.enabled"
            checked={formData.permissions.treeql.enabled}
            onChange={(e) => {
              setFormData((prev) => ({
                ...prev,
                permissions: {
                  ...prev.permissions,
                  treeql: {
                    ...prev.permissions.treeql,
                    enabled: e.target.checked
                  }
                }
              }));
            }}
            className="text-blue-600 rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
          />
        </div>
      </div>

      {formData.permissions.treeql.enabled && (
        <div className="space-y-2">
          {models.map((model) => (
            <div key={model.name} className="p-2 rounded-lg border">
              <div className="flex justify-between items-center">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={
                      !!formData.permissions.treeql.models[model.name]?.allowed
                    }
                    onChange={(e) =>
                      handleModelToggle(model.name, e.target.checked)
                    }
                    className="text-blue-600 rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                  />
                  <span className="ml-2 font-medium">{model.name}</span>
                </label>
              </div>

              {formData.permissions.treeql.models[model.name]?.allowed && (
                <>
                  <div>
                    <h5 className="text-sm font-medium">Operations</h5>
                    <div className="flex flex-wrap gap-3">
                      {Object.entries(
                        formData.permissions.treeql.models[model.name]
                          .operations
                      ).map(([op, enabled]) => (
                        <label key={op} className="flex items-center">
                          <input
                            type="checkbox"
                            checked={enabled}
                            onChange={(e) =>
                              handleOperationToggle(
                                model.name,
                                op as keyof TreeQLModelPermissions["operations"],
                                e.target.checked
                              )
                            }
                            className="text-blue-600 rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                          />
                          <span className="ml-2 text-sm capitalize">{op}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h5 className="text-sm font-medium">Fields</h5>
                    <div className="flex flex-wrap gap-3">
                      {model.fields.map((field) => (
                        <label key={field.name} className="flex items-center">
                          <input
                            type="checkbox"
                            checked={
                              !formData.permissions.treeql.models[
                                model.name
                              ].blacklistedFields.includes(field.name)
                            }
                            onChange={(e) =>
                              handleFieldToggle(
                                model.name,
                                field.name,
                                e.target.checked
                              )
                            }
                            className="text-blue-600 rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                          />
                          <span className="ml-2 text-sm">
                            {field.name}
                            <span className="ml-1 text-xs text-gray-500">
                              ({field.type})
                            </span>
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>
                </>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
