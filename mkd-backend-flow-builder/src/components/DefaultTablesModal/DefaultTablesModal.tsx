import { useState } from "react";
import { X, Plus } from "lucide-react";
import { useFlowContext } from "@/store/FlowContext";

interface DefaultTable {
  id: string;
  name: string;
  description: string;
  fields: {
    name: string;
    type: string;
    defaultValue: string;
    mapping?: string;
    validation: string;
  }[];
  dependencies?: string[];
}

const defaultTables: DefaultTable[] = [
  {
    id: "company",
    name: "company",
    description: "Organization table for multitenancy support",
    fields: [
      { name: "id", type: "primary key", defaultValue: "", validation: "" },
      { name: "name", type: "string", defaultValue: "", validation: "required" },
      { name: "website", type: "string", defaultValue: "", validation: "" },
      { name: "logo", type: "string", defaultValue: "", validation: "" },
      { name: "owner_id", type: "integer", defaultValue: "", validation: "" },
      { name: "address", type: "string", defaultValue: "", validation: "" },
      { name: "city", type: "string", defaultValue: "", validation: "" },
      { name: "state", type: "string", defaultValue: "", validation: "" },
      { name: "zip", type: "integer", defaultValue: "", validation: "" },
      { name: "country", type: "string", defaultValue: "", validation: "" },
      { name: "phone", type: "string", defaultValue: "", validation: "phone" },
      {
        name: "status",
        type: "mapping",
        mapping: "0:Active,1:Inactive,2:Suspend",
        defaultValue: "0",
        validation: ""
      },
      {
        name: "created_at",
        type: "timestamp",
        defaultValue: "CURRENT_TIMESTAMP",
        validation: "date"
      },
    ],
  },
  {
    id: "company_admin",
    name: "company_admin",
    description: "Company admin table for managing company admins",
    fields: [
      { name: "id", type: "primary key", defaultValue: "", validation: "" },
      { name: "company_id", type: "integer", defaultValue: "", validation: "required" },
      { name: "user_id", type: "integer", defaultValue: "", validation: "required" },
      {
        name: "created_at",
        type: "timestamp",
        defaultValue: "CURRENT_TIMESTAMP",
        validation: "date"
      },
    ],
  },
  {
    id: "company_employee_subscription",
    name: "company_employee_subscription",
    description: "Company employee subscription table for managing company employee subscriptions",
    fields: [
      { name: "id", type: "primary key", defaultValue: "", validation: "" },
      { name: "company_id", type: "integer", defaultValue: "", validation: "required" },
      { name: "user_id", type: "integer", defaultValue: "", validation: "required" },
      { name: "stripe_subscription_id", type: "string", defaultValue: "", validation: "required" },
      {
        name: "status",
        type: "mapping",
        mapping: "0:Active,1:Inactive,2:Suspend",
        defaultValue: "0",
        validation: "required,enum:0,1,2",
      },
      {
        name: "created_at",
        type: "timestamp",
        defaultValue: "CURRENT_TIMESTAMP",
        validation: "date"
      },
    ],
  },
  {
    id: "company_usage",
    name: "company_usage",
    description: "Company usage table for managing company usage",
    fields: [
      { name: "id", type: "primary key", defaultValue: "", validation: "" },
      { name: "company_id", type: "integer", defaultValue: "", validation: "required" },
      {
        name: "created_at",
        type: "timestamp",
        defaultValue: "CURRENT_TIMESTAMP",
        validation: "date"
      },
    ],
  },
  {
    id: "company_user",
    name: "company_user",
    description: "Company user table for managing company users",
    fields: [
      { name: "id", type: "primary key", defaultValue: "", validation: "" },
      { name: "company_id", type: "integer", defaultValue: "", validation: "required" },
      { name: "user_id", type: "integer", defaultValue: "", validation: "required" },
      { name: "role", type: "string", defaultValue: "", validation: "required" },
      { name: "email", type: "string", defaultValue: "", validation: "required" },
      {
        name: "status",
        type: "mapping",
        mapping: "0:Active,1:Inactive,2:Suspend",
        defaultValue: "0",
        validation: "required,enum:0,1,2",
      },
      {
        name: "created_at",
        type: "timestamp",
        defaultValue: "CURRENT_TIMESTAMP",
        validation: "date"
      },
    ],
  },
  {
    id: "user",
    name: "user",
    description: "User management table with authentication details",
    fields: [
      { name: "id", type: "primary key", defaultValue: "", validation: "" },
      { name: "email", type: "string", defaultValue: "", validation: "required,email" },
      { name: "password", type: "password", defaultValue: "", validation: "required" },
      {
        name: "login_type",
        type: "mapping",
        mapping: "0:Regular,1:Google,2:Microsoft,3:Apple,4:Twitter,5:Facebook",
        defaultValue: "0",
        validation: "required,enum:0,1,2,3,4,5",
      },
      { name: "role_id", type: "string", defaultValue: "", validation: "" },
      { name: "data", type: "json", defaultValue: "", validation: "" },
      {
        name: "status",
        type: "mapping",
        mapping: "0:Active,1:Inactive,2:Suspend",
        defaultValue: "0",
        validation: "required,enum:0,1,2",
      },
      {
        name: "verify",
        type: "boolean",
        defaultValue: "0",
        validation: "required"
      },
      {
        name: "two_factor_authentication",
        type: "boolean",
        defaultValue: "0",
        validation: ""
      },
      {
        name: "company_id",
        type: "integer",
        defaultValue: "0",
        validation: ""
      },
      {
        name: "stripe_uid",
        type: "string",
        defaultValue: "",
        validation: ""
      },
      {
        name: "created_at",
        type: "timestamp",
        defaultValue: "CURRENT_TIMESTAMP",
        validation: "date"
      }
    ],
  },
  {
    id: "preference",
    name: "preference",
    description: "User preferences table for storing user preferences",
    fields: [
      { name: "id", type: "primary key", defaultValue: "", validation: "" },
      { name: "first_name", type: "string", defaultValue: "", validation: "" },
      { name: "last_name", type: "string", defaultValue: "", validation: "" },
      { name: "phone", type: "string", defaultValue: "", validation: "" },
      { name: "photo", type: "string", defaultValue: "", validation: "" },
      { name: "user_id", type: "integer", defaultValue: "", validation: "required" },
    ],
    dependencies: ["user"]
  },
  {
    id: "tokens",
    name: "tokens",
    description: "Authentication tokens table for managing user sessions",
    fields: [
      { name: "id", type: "primary key", defaultValue: "", validation: "" },
      {
        name: "user_id",
        type: "foreign key",
        defaultValue: "",
        validation: "required"
      },
      { name: "token", type: "string", defaultValue: "", validation: "required" },
      { name: "code", type: "string", defaultValue: "", validation: "required" },
      {
        name: "type",
        type: "mapping",
        mapping: "0:Access,1:Refresh,2:Reset,3:Verify,4:Magic",
        defaultValue: "0",
        validation: "required,enum:0,1,2,3,4"
      },
      { name: "data", type: "json", defaultValue: "", validation: "" },
      {
        name: "status",
        type: "mapping",
        mapping: "0:Inactive,1:Active",
        defaultValue: "1",
        validation: "required,enum:0,1"
      },
      {
        name: "created_at",
        type: "timestamp",
        defaultValue: "CURRENT_TIMESTAMP",
        validation: "date"
      },
      {
        name: "updated_at",
        type: "timestamp",
        defaultValue: "CURRENT_TIMESTAMP",
        validation: "date"
      },
      {
        name: "expired_at",
        type: "timestamp",
        defaultValue: "",
        validation: "date"
      }
    ],
    dependencies: ["user"]
  },
  {
    id: "uploads",
    name: "uploads",
    description: "Table for managing user uploads",
    fields: [
      { name: "id", type: "primary key", defaultValue: "", validation: "" },
      { name: "url", type: "string", defaultValue: "", validation: "required" },
      { name: "caption", type: "string", defaultValue: "", validation: "" },
      { name: "user_id", type: "foreign key", defaultValue: "", validation: "" },
      { name: "width", type: "integer", defaultValue: "", validation: "" },
      { name: "height", type: "integer", defaultValue: "", validation: "" },
      {
        name: "type",
        type: "mapping",
        mapping: "0:Image,1:s3,2:Video,3:base64",
        defaultValue: "0",
        validation: "required,enum:0,1,2,3"
      },
      { name: "created_at", type: "timestamp", defaultValue: "CURRENT_TIMESTAMP", validation: "date" },
      { name: "updated_at", type: "timestamp", defaultValue: "CURRENT_TIMESTAMP", validation: "date" },
    ],
    dependencies: ["user"]
  },
  {
    id: "cms",
    name: "cms",
    description: "Content Management System table for storing dynamic content",
    fields: [
      { name: "id", type: "primary key", defaultValue: "", validation: "" },
      { name: "label", type: "string", defaultValue: "", validation: "required" },
      {
        name: "type",
        type: "mapping",
        mapping: "0:Text,1:Number,2:Image,3:Raw",
        defaultValue: "0",
        validation: "required,enum:0,1,2,3",
      },
      { name: "value", type: "long text", defaultValue: "", validation: "required" },
    ],
  },
  {
  id: "job",
  name: "job",
  description: "Job table for managing scheduled tasks",
  fields: [
    { name: "id", type: "primary key", defaultValue: "", validation: "" },
    { name: "task", type: "string", defaultValue: "", validation: "required" },
    { name: "arguments", type: "json", defaultValue: "", validation: "" },
    { name: "time_interval", type: "string", defaultValue: "once", validation: "" },
    { name: "retries", type: "integer", defaultValue: "1", validation: "" },
    { name: "status", type: "mapping", mapping: "0:Pending,1:Failed,2:Processing,3:Completed", defaultValue: "0", validation: "" },
    { name: "created_at", type: "timestamp", defaultValue: "CURRENT_TIMESTAMP", validation: "date" },
    { name: "updated_at", type: "timestamp", defaultValue: "CURRENT_TIMESTAMP", validation: "date" },
  ],
}
];

interface DefaultTablesModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function DefaultTablesModal({
  isOpen,
  onClose,
}: DefaultTablesModalProps) {
  const [selectedTables, setSelectedTables] = useState<string[]>([]);
  const [includeAdminRole, setIncludeAdminRole] = useState(true);
  const [includeMemberRole, setIncludeMemberRole] = useState(true);
  const { addModel, addRole } = useFlowContext();

  const handleToggleTable = (tableId: string) => {
    setSelectedTables((prev) => {
      if (tableId === "company") {
        if (!prev.includes(tableId)) {
          // When selecting company, also select user
          return [...prev, 
                tableId, 
                "user", 
                "company_user", 
                "company_admin", 
                "company_employee_subscription", 
                "company_usage"];
        } else {
          // When deselecting company, just remove company

          return prev.filter(id => !id.includes("company"));
        }
      }

      // Normal toggle for other tables
      return prev.includes(tableId)
        ? prev.filter((id) => id !== tableId)
        : [...prev, tableId];
    });
  };

  const handleAddTables = () => {
    selectedTables.forEach((tableId) => {
      const table = defaultTables.find((t) => t.id === tableId);
      if (table) {
        if (table.id === "user" && selectedTables.includes("company")) {
          // Add company_id field to user table when company is selected
          const userWithCompanyId = {
            ...table,
            fields: [
              ...table.fields.slice(0, 2),
              {
                name: "company_id",
                type: "foreign key",
                defaultValue: "",
                validation: "required"
              },
              ...table.fields.slice(2),
            ],
          };
          addModel({
            id: `model_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            name: userWithCompanyId.name,
            fields: userWithCompanyId.fields,
          });
        } else {
          addModel({
            id: `model_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            name: table.name,
            fields: table.fields,
          });
        }
      }
    });

    // Add roles based on checkbox selection
    if (selectedTables.includes("user")) {
      if (includeAdminRole) {
        // Add super admin role
        addRole({
          id: `role_admin_${Date.now()}`,
          name: "Super Admin",
          slug: "super_admin",
          permissions: {
            routes: [],
            canCreateUsers: true,
            canEditUsers: true,
            canDeleteUsers: true,
            canManageRoles: true,
            canUpdateOtherUsers: true,
          },
        });

        // Add company admin role if company is selected
        if (selectedTables.includes("company")) {
          addRole({
            id: `role_company_admin_${Date.now()}`,
            name: "Company Admin",
            slug: "company_admin",
            permissions: {
              routes: [],
              canCreateUsers: true,
              canEditUsers: true,
              canDeleteUsers: false,
              canManageRoles: false,
              canUpdateOtherUsers: true,
              companyScoped: true, // Only manage users within their company
            },
          });
        }
      }

      if (includeMemberRole) {
        addRole({
          id: `role_member_${Date.now()}`,
          name: "Member",
          slug: "member",
          permissions: {
            routes: [],
            canCreateUsers: false,
            canEditUsers: false,
            canDeleteUsers: false,
            canManageRoles: false,
            canUpdateOtherUsers: false,
            companyScoped: selectedTables.includes("company"), // Scope to company if company table exists
          },
        });
      }
    }

    onClose();
  };

  if (!isOpen) return null;

  return (
    <div
      className="flex fixed inset-0 z-50 justify-center items-center bg-black bg-opacity-50"
      style={{ marginTop: "0px" }}
    >
      <div className="bg-white rounded-lg w-full max-w-2xl max-h-[80vh] overflow-y-auto">
        <div className="flex justify-between items-center p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold">Add Default Tables</h2>
          <button onClick={onClose} className="p-1 rounded hover:bg-gray-100">
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="overflow-y-auto p-4">
          <p className="mb-4 text-sm text-gray-600">
            Select the default tables you would like to add to your project:
          </p>

          <div className="space-y-4">
            {defaultTables.map((table) => (
              <div
                key={table.id}
                className="p-4 rounded-lg border transition-colors hover:border-blue-500"
              >
                <label className="flex items-start space-x-3 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedTables.includes(table.id)}
                    onChange={() => handleToggleTable(table.id)}
                    className="mt-1 text-blue-600 rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                  />
                  <div>
                    <h3 className="font-medium">{table.name}</h3>
                    <p className="text-sm text-gray-600">{table.description}</p>
                    {table.dependencies && (
                      <p className="mt-1 text-xs text-blue-600">
                        Requires: {table.dependencies.map(dep =>
                          defaultTables.find(t => t.id === dep)?.name
                        ).join(", ")}
                      </p>
                    )}
                    <div className="mt-2 text-sm text-gray-500">
                      Fields: {table.fields.map((f) => f.name).join(", ")}
                    </div>

                    {/* Add role options when User table is selected */}
                    {table.id === "user" && selectedTables.includes("user") && (
                      <div className="pl-2 mt-3 border-l-2 border-gray-200">
                        <p className="mb-2 text-sm font-medium text-gray-700">
                          Include roles:
                        </p>
                        <div className="space-y-2">
                          <label className="flex items-center space-x-2 text-sm">
                            <input
                              type="checkbox"
                              checked={includeAdminRole}
                              onChange={(e) =>
                                setIncludeAdminRole(e.target.checked)
                              }
                              className="text-blue-600 rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                            />
                            <span>Admin Role</span>
                          </label>
                          <label className="flex items-center space-x-2 text-sm">
                            <input
                              type="checkbox"
                              checked={includeMemberRole}
                              onChange={(e) =>
                                setIncludeMemberRole(e.target.checked)
                              }
                              className="text-blue-600 rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                            />
                            <span>Member Role</span>
                          </label>
                        </div>
                      </div>
                    )}
                  </div>
                </label>
              </div>
            ))}
          </div>
        </div>

        <div className="flex gap-2 justify-end p-4 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 rounded hover:bg-gray-100"
          >
            Cancel
          </button>
          <button
            onClick={handleAddTables}
            disabled={selectedTables.length === 0}
            className="flex gap-2 items-center px-4 py-2 text-white bg-blue-500 rounded hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed"
          >
            <Plus className="w-4 h-4" />
            Add Selected Tables
          </button>
        </div>
      </div>
    </div>
  );
}
