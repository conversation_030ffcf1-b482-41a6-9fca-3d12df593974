import React, { useState, useEffect } from "react";
import { Plus, Edit2, CopyIcon } from "lucide-react";
import { ModelModal } from "@/components/ModelModal";
import { DefaultTablesModal } from "@/components/DefaultTablesModal";
import { useFlowContext } from "@/store/FlowContext";

export default function ModelPanel() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDefaultTablesModalOpen, setIsDefaultTablesModalOpen] =
    useState(false);
  const [selectedModel, setSelectedModel] = useState<any>(null);
  const {
    state: { models, defaultTablesShown },
    setDefaultTablesShown,
    addModel,
  } = useFlowContext();


  
  const handleEditModel = (model: any) => {
    setSelectedModel(model);
    setIsModalOpen(true);
  };

  const duplicateModel = (model: any) => {
    // Implement logic to duplicate the model
    const duplicateModel = {
      ...model,
      id: `model_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: `${model.name} (Copy)`,
    };
    addModel(duplicateModel)
    setSelectedModel(duplicateModel);
    setIsModalOpen(true);
    // console.log("Duplicate model:", model);
  };

  
  useEffect(() => {
    // Show default tables modal only if there are no models and it hasn't been shown before
    if (models?.length === 0 && !defaultTablesShown) {
      setIsDefaultTablesModalOpen(true);
      setDefaultTablesShown(true); // Mark as shown
    }
  }, []); // Empty dependency array means this runs once on mount


  return (
    <div className="space-y-4">
      <button
        onClick={() => {
          setSelectedModel(null);
          setIsModalOpen(true);
        }}
        className="flex gap-2 justify-center items-center px-4 py-2 w-full text-white bg-blue-500 rounded-lg transition-colors hover:bg-blue-600"
      >
        <Plus className="w-4 h-4" />
        Add Model
      </button>

      <div className="space-y-3">
        {models.map((model, modelKey) => (
          <div
            key={modelKey}
            className="p-4 bg-white rounded-lg border transition-shadow hover:shadow-md"
          >
            <div className="grid grid-cols-[1fr_auto] gap-2 justify-between items-start">
              <div className="truncate text-ellipsis">
                <h3 title={model.name} className="font-medium">{model.name}</h3>
                <p className="text-sm text-gray-500">
                  {model.fields.length} field
                  {model.fields.length !== 1 ? "s" : ""}
                </p>
              </div>
              <div className="flex gap-2 justify-center items-center">
              <button
                className="p-1 rounded hover:bg-gray-100"
                onClick={(e) => {
                  e.stopPropagation();
                  duplicateModel(model);
                }}
              >
                {/* duplicate model */}
                <CopyIcon className="w-4 h-4" />
              </button>
              <button
                className="p-1 rounded hover:bg-gray-100"
                onClick={(e) => {
                  e.stopPropagation();
                  handleEditModel(model);
                }}
              >
                <Edit2 className="w-4 h-4" />
              </button>

              </div>
            </div>
          </div>
        ))}
      </div>

      <ModelModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setSelectedModel(null);
        }}
        model={selectedModel}
      />

      <DefaultTablesModal
        isOpen={isDefaultTablesModalOpen}
        onClose={() => setIsDefaultTablesModalOpen(false)}
      />
    </div>
  );
}
