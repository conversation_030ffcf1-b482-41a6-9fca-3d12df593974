import React, { useState } from "react";
import { Plus, Edit2 } from "lucide-react";
import { RoleModal } from "@/components/RoleModal";
import { useFlowContext } from "@/store/FlowContext";

export default function RolesPanel() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState<any>(null);
  const {
    state: { roles },
  } = useFlowContext();

  return (
    <div className="space-y-4">
      <button
        onClick={() => {
          setSelectedRole(null);
          setIsModalOpen(true);
        }}
        className="flex gap-2 justify-center items-center px-4 py-2 w-full text-white bg-blue-500 rounded-lg transition-colors hover:bg-blue-600"
      >
        <Plus className="w-4 h-4" />
        Add Role
      </button>

      <div className="space-y-3">
        {roles.map((role) => (
          <div
            key={role.id}
            className="p-4 bg-white rounded-lg border transition-shadow hover:shadow-md"
          >
            <div className="grid grid-cols-[1fr_auto] gap-2 justify-between items-start">
              <div className={`w-full min-w-full max-w-full truncate`}>
                <h3 title={role.name} className="truncate text-ellipsisfont-medium">
                  {role.name}
                </h3>
                <p title={role.slug} className="text-sm text-gray-500">
                  {role.slug}
                </p>
              </div>
              <button
                onClick={() => {
                  setSelectedRole(role);
                  setIsModalOpen(true);
                }}
                className="p-1 rounded hover:bg-gray-100"
              >
                <Edit2 className="w-4 h-4" />
              </button>
            </div>
          </div>
        ))}
      </div>

      <RoleModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setSelectedRole(null);
        }}
        role={selectedRole}
      />
    </div>
  );
}
