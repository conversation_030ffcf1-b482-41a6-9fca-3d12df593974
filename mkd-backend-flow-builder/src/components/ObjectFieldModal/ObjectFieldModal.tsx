import React, { useState, useEffect } from "react";
import { Plus, Trash, X } from "lucide-react";

interface Field {
  name: string;
  type: string;
  validation?: string;
  fields?: Field[];
}

interface ObjectFieldModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (fields: Field[]) => void;
  initialFields?: Field[];
}

interface NestedFieldState {
  [key: string]: Field;
}

function FieldTypeSelect({ value, onChange }: { value: string, onChange: (value: string) => void }) {
  return (
    <select
      value={value}
      onChange={(e) => onChange(e.target.value)}
      className="p-2 w-24 text-sm rounded border"
    >
      <option value="string">String</option>
      <option value="number">Number</option>
      <option value="integer">Integer</option>
      <option value="bool">Bool</option>
      <option value="date">Date</option>
      <option value="datetime">Datetime</option>
      <option value="file">File</option>
      <option value="array">Array</option>
      <option value="object">Object</option>
    </select>
  );
}

export default function ObjectFieldModal({ isOpen, onClose, onSave, initialFields = [] }: ObjectFieldModalProps) {
  const [fields, setFields] = useState<Field[]>(initialFields);
  const [newField, setNewField] = useState<Field>({ name: '', type: 'string' });
  const [nestedNewFields, setNestedNewFields] = useState<NestedFieldState>({});
  const [expandedFields, setExpandedFields] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (isOpen) {
      setFields(initialFields);
    } else {
      setFields([]);
      setNewField({ name: '', type: 'string' });
    }
  }, [isOpen, initialFields]);

  const addField = () => {
    if (!newField.name.trim()) return;
    setFields([...fields, { ...newField }]);
    setNewField({ name: '', type: 'string' });
  };

  const removeField = (index: number) => {
    const newFields = [...fields];
    newFields.splice(index, 1);
    setFields(newFields);
  };

  const handleSave = () => {
    onSave(fields);
    onClose();
  };

  const renderField = (field: Field, index: number, level: number = 0, parentPath: number[] = []) => {
    const fieldKey = `${level}-${parentPath.join('-')}-${index}`;
    const isExpanded = expandedFields.has(fieldKey);
    
    // Get the nested field state for this specific object field
    const currentNestedField = nestedNewFields[fieldKey] || { name: '', type: 'string' };

    const handleDelete = () => {
      const newFields = [...fields];
      let target = newFields;
      let parentField = null;

      // Navigate to the correct nesting level
      for (const pathIndex of parentPath) {
        parentField = target[pathIndex];
        target = parentField.fields!;
      }

      // Delete the field at the current level
      if (parentField) {
        parentField.fields = target.filter((_, i) => i !== index);
      } else {
        newFields.splice(index, 1);
      }
      setFields(newFields);
    };

    const updateField = (fieldUpdate: Partial<Field>) => {
      const newFields = [...fields];
      let target = newFields;
      let targetField;

      // Navigate to the correct nesting level
      for (const pathIndex of parentPath) {
        targetField = target[pathIndex];
        target = targetField.fields!;
      }

      // Update the field
      if (parentPath.length > 0) {
        Object.assign(target[index], fieldUpdate);
      } else {
        Object.assign(newFields[index], fieldUpdate);
      }

      setFields(newFields);
    };

    return (
      <div key={fieldKey} className="mb-2">
        <div className="flex items-center">
          <div className="mr-2 text-gray-500">
            {level > 0 && '- '.repeat(level)}
          </div>
          <div className="flex flex-1 gap-1">
            <input
              type="text"
              value={field.name}
              onChange={(e) => updateField({ name: e.target.value })}
              className="flex-1 p-2 text-sm rounded border"
              placeholder="Field name"
            />
            <FieldTypeSelect
              value={field.type}
              onChange={(value) => {
                updateField({ 
                  type: value,
                  ...(value === 'object' ? { fields: [] } : {})
                });
              }}
            />
            <button
              onClick={handleDelete}
              className="p-2 text-red-500 rounded hover:bg-red-50"
            >
              <Trash className="w-4 h-4" />
            </button>
          </div>
        </div>

        {field.type === 'object' && (
          <div className="mt-2">
            <button
              onClick={() => {
                const newExpanded = new Set(expandedFields);
                const key = `${level}-${parentPath.join('-')}-${index}`;
                if (isExpanded) {
                  newExpanded.delete(key);
                } else {
                  newExpanded.add(key);
                }
                setExpandedFields(newExpanded);
              }}
              className="mb-2 ml-4 text-sm text-blue-500 hover:text-blue-600"
            >
              {isExpanded ? '- Collapse' : '+ Expand'} Object Fields
            </button>

            {isExpanded && (
              <>
                {field.fields?.map((nestedField, nestedIndex) => (
                  renderField(
                    nestedField, 
                    nestedIndex, 
                    level + 1, 
                    [...parentPath, index]
                  )
                ))}
                <div className="flex gap-1 mt-2 ml-4">
                  <div className="mr-2 text-gray-500">
                    {(level + 1 > 0) && '- '.repeat(level + 1)}
                  </div>
                  <input
                    type="text"
                    value={currentNestedField.name}
                    onChange={(e) => setNestedNewFields({
                      ...nestedNewFields,
                      [fieldKey]: { ...currentNestedField, name: e.target.value }
                    })}
                    className="flex-1 p-2 text-sm rounded border"
                    placeholder="New nested field name"
                  />
                  <FieldTypeSelect
                    value={currentNestedField.type}
                    onChange={(value) => setNestedNewFields({
                      ...nestedNewFields,
                      [fieldKey]: { ...currentNestedField, type: value }
                    })}
                  />
                  <button
                    onClick={() => {
                      if (!currentNestedField.name.trim()) return;
                      const newFields = [...fields];
                      let target = newFields;
                      let targetField;

                      // Navigate to the correct nesting level
                      for (const pathIndex of parentPath) {
                        targetField = target[pathIndex];
                        target = targetField.fields!;
                      }

                      // Add the new field at the current level
                      if (parentPath.length > 0) {
                        target[index].fields = target[index].fields || [];
                        target[index].fields.push({ ...currentNestedField });
                      } else {
                        newFields[index].fields = newFields[index].fields || [];
                        newFields[index].fields.push({ ...currentNestedField });
                      }

                      setFields(newFields);
                      // Reset only this specific nested field
                      setNestedNewFields({
                        ...nestedNewFields,
                        [fieldKey]: { name: '', type: 'string' }
                      });
                    }}
                    className="p-2 text-white bg-blue-500 rounded hover:bg-blue-600"
                  >
                    <Plus className="w-4 h-4" />
                  </button>
                </div>
              </>
            )}
          </div>
        )}
      </div>
    );
  };

  // Add this useEffect to clean up nested fields state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setNestedNewFields({});
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="flex fixed inset-0 z-50 justify-center items-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg p-6 w-1/2 max-h-[80vh] overflow-y-auto relative">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Object Fields</h3>
          <button 
            onClick={onClose}
            className="p-1 rounded hover:bg-gray-100"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
        
        {fields.map((field, index) => renderField(field, index, 0))}

        <div className="flex gap-1 mt-2">
          <input
            type="text"
            value={newField.name}
            onChange={(e) => setNewField({ ...newField, name: e.target.value })}
            className="flex-1 p-2 text-sm rounded border"
            placeholder="New field name"
          />
          <FieldTypeSelect
            value={newField.type}
            onChange={(value) => setNewField({ ...newField, type: value })}
          />
          <button
            onClick={addField}
            className="p-2 text-white bg-blue-500 rounded hover:bg-blue-600"
          >
            <Plus className="w-4 h-4" />
          </button>
        </div>

        <div className="flex justify-end mt-4">
          <button
            onClick={handleSave}
            className="px-4 py-2 text-white bg-blue-500 rounded hover:bg-blue-600"
          >
            Save
          </button>
        </div>
      </div>
    </div>
  );
} 