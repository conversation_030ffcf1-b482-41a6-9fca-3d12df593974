import React, { useState, useEffect } from "react";
import { X, Trash2 } from "lucide-react";
import { useFlowContext } from "@/store/FlowContext";

interface RouteModalProps {
  isOpen: boolean;
  onClose: () => void;
  route?: {
    id: string;
    name: string;
  };
}

const createInitialFormData = () => ({
  id: `route_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  name: "",
  method: "GET",
  url: "/v1/api/",
  isMockApi: false,
});

export default function RouteModal({ isOpen, onClose, route }: RouteModalProps) {
  const { addRoute, updateRoute, deleteRoute } = useFlowContext();
  const [formData, setFormData] = useState(createInitialFormData());

  useEffect(() => {
    if (route) {
      setFormData(route);
    } else {
      setFormData(createInitialFormData());
    }
  }, [route, isOpen]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleSave = () => {
    const urlNode = {
      id: `url_node_${Date.now()}`,
      type: formData.isMockApi ? "mock-api" : "url",
      position: { x: 100, y: 100 },
      data: formData.isMockApi
        ? {
            label: "Mock API",
            apiname: formData.name,
            path: formData.url,
            method: formData.method,
            description: "",
            fields: [],
            queryFields: [],
            responseFields: [],
            authType: "none",
            outputType: "json",
            statusCode: 200,
          }
        : {
            label: "URL",
            apiname: formData.name,
            path: formData.url,
            method: formData.method,
            fields: [],
            queryFields: [],
          },
    };

    const routeData = {
      id: formData.id,
      name: formData.name,
      flowData: {
        nodes: [urlNode],
        edges: [],
      },
    };

    if (route) {
      updateRoute(routeData);
    } else {
      addRoute(routeData);
    }
    onClose();
  };

  const handleDelete = () => {
    if (route) {
      deleteRoute(route.id);
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="flex fixed inset-0 z-50 justify-center items-center bg-black bg-opacity-50">
      <div className="w-full max-w-md bg-white rounded-lg">
        <div className="flex justify-between items-center p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold">
            {route ? "Edit Route" : "Add New Route"}
          </h2>
          <button onClick={onClose} className="p-1 rounded hover:bg-gray-100">
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="p-4">
          <div className="space-y-4">
            <div>
              <label className="block mb-1 text-sm font-medium">
                Route Name
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleChange}
                className="p-2 w-full rounded border"
                placeholder="Enter route name"
              />
            </div>
            <div>
              <label className="block mb-1 text-sm font-medium">Method</label>
              <select
                name="method"
                value={formData.method}
                onChange={handleChange}
                className="p-2 w-full rounded border"
                disabled={!!route}
                title={route ? "Method cannot be changed after creation" : ""}
              >
                <option value="GET">GET</option>
                <option value="POST">POST</option>
                <option value="PUT">PUT</option>
                <option value="DELETE">DELETE</option>
                <option value="PATCH">PATCH</option>
              </select>
            </div>

            <div>
              <label className="block mb-1 text-sm font-medium">URL</label>
              <input
                type="text"
                name="url"
                value={formData.url}
                onChange={handleChange}
                className="p-2 w-full rounded border"
                placeholder="/api/resource/:id"
                disabled={!!route}
                title={route ? "URL cannot be changed after creation" : ""}
              />
            </div>
            {!route && (
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isMockApi"
                  name="isMockApi"
                  checked={formData.isMockApi}
                  onChange={(e) =>
                    handleChange({
                      target: {
                        name: "isMockApi",
                        value: e.target.checked,
                      },
                    } as any)
                  }
                  className="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                />
                <label
                  htmlFor="isMockApi"
                  className="block ml-2 text-sm text-gray-900"
                >
                  Is Mock API
                </label>
              </div>
            )}
          </div>
        </div>

        <div className="flex justify-between p-4 border-t border-gray-200">
          {route && (
            <button
              onClick={handleDelete}
              className="flex gap-2 items-center px-4 py-2 text-red-600 rounded hover:bg-red-50"
            >
              <Trash2 className="w-4 h-4" />
              Delete Route
            </button>
          )}
          <div className="flex gap-2 ml-auto">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 rounded hover:bg-gray-100"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              className="px-4 py-2 text-white bg-blue-500 rounded hover:bg-blue-600"
            >
              {route ? "Update" : "Save"} Route
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
