import fs from "fs"
import path from "path"

// TODO: look into the current directory and create a folder for each file found apart from cf.js
// TODO: move the file into the corresponding folder and create an index file in each folder that exports the main file

const currentDirectory = process.cwd();
const files = fs.readdirSync(currentDirectory); 

files.forEach((file) => {
    const filePath = path.join(currentDirectory, file);
    const fileStats = fs.statSync(filePath);
    
    if (file !== "cf.js" && fileStats.isFile()) {
        const folderName = file.replace(".tsx", ""); 
        fs.mkdirSync(path.join(currentDirectory, folderName));
        const exportContentt = `
        import { lazy } from "react";

        export const ${folderName} = lazy(() => import("./${folderName}"));
        `
        fs.writeFileSync(path.join(currentDirectory, folderName, "index.ts"), exportContentt);
    }

})

// {
//     "id": 1.64,
//     "name": "demo",
//     "code": 28,
//     "error": false
//   }