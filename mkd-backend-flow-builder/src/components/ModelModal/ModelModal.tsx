import React, { useState, useEffect } from "react";
import { X, Plus, Trash } from "lucide-react";
import { useFlowContext } from "@/store/FlowContext";

interface Field {
  name: string;
  type: string;
  defaultValue: string;
  validation: string;
  validationOptions?: {
    pattern?: string;
    enum?: string[];
    min?: number;
    max?: number;
    minLength?: number;
    maxLength?: number;
  };
  mapping?: string;
  relation: string;
}

interface ModelModalProps {
  isOpen: boolean;
  onClose: () => void;
  model?: {
    id: string;
    name: string;
    fields: Field[];
  };
}

const fieldTypes = [
  "primary key",
  "string",
  "long text",
  "integer",
  "double",
  "big number",
  "boolean",
  "date",
  "datetime",
  "uuid",
  "json",
  "mapping",
];

const validationRules = [
  "required",
  "email",
  "url",
  "min",
  "max",
  "minLength",
  "maxLength",
  "length",
  "enum",
  "pattern",
  "positive",
  "negative",
  "integer",
  "decimal",
  "alphanumeric",
  "uuid",
  "json",
  "date",
  "phone",
];

const initialNewField: Field = {
  name: "",
  type: "string",
  defaultValue: "",
  validation: "required",
  validationOptions: {},
  relation: "",
};

const createInitialModelData = () => ({
  id: `model_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  name: "",
  fields: [
    {
      name: "id",
      type: "primary key",
      defaultValue: "",
      validation: "",
      validationOptions: {},
    },
    {
      name: "created_at",
      type: "datetime",
      defaultValue: "",
      validation: "",
      validationOptions: {},
    },
    {
      name: "updated_at",
      type: "datetime",
      defaultValue: "",
      validation: "",
      validationOptions: {},
    },
  ],
});

export default function ModelModal({ isOpen, onClose, model }: ModelModalProps) {
  const [modelData, setModelData] = useState<{
    id: string;
    name: string;
    fields: Field[];
  }>(createInitialModelData());
  const [newField, setNewField] = useState<Field>(initialNewField);
  const [createCrudApis, setCreateCrudApis] = useState(false); // New state for CRUD API checkbox
  const [modelRelations, setModelRelations] = useState<string[]>([]); // New state for managing model relations

  const { addModel, updateModel, addRoute, state } = useFlowContext();


  const handleAddField = () => {
    if (newField.name) {
      setModelData({
        ...modelData,
        fields: [
          ...modelData.fields,
          { ...newField, relation: modelRelations.join(', '), type: newField.type === "integer" && modelRelations.length > 0 ? "integer" : newField.type },
        ],
        relations: modelRelations,
      });
      setNewField(initialNewField);
      setModelRelations([]);
    }
  };

  const handleRemoveField = (index: number) => {
    const updatedFields = [...modelData.fields];
    updatedFields.splice(index, 1);
    setModelData({
      ...modelData,
      fields: updatedFields,
    });
  };

  const handleSave = () => {
    if (modelData.name) {
      if (model) {
        updateModel(modelData);
      } else {
        addModel(modelData);
        if (createCrudApis) {
          // Create GET route for fetching all records
          const uniqueId = Date.now(); // Generate a unique identifier based on the current timestamp
          const getRoute = {
            id: `route_${Date.now()}_${Math.random()
              .toString(36)
              .substr(2, 9)}`,
            name: `Get All ${modelData.name}`,
            method: "GET",
            url: `/api/${modelData.name.toLowerCase()}`,
            flowData: {
              nodes: [
                {
                  id: `url_node_${uniqueId}`,
                  type: "url",
                  position: { x: 100, y: 100 },
                  data: {
                    label: `Url`,
                    apiname: `Get All ${modelData.name}`,
                    path: `/api/${modelData.name.toLowerCase()}`,
                    method: "GET",
                  },
                },
                {
                  id: `auth_node_${uniqueId}`,
                  type: "auth",
                  position: { x: 100, y: 200 },
                  data: {
                    label: "Auth",
                    authType: "none",
                  },
                },
                {
                  id: `db_find_node_${uniqueId}`,
                  type: "db-find",
                  position: { x: 100, y: 300 },
                  data: {
                    label: "Database Find",
                    model: modelData.name,
                    operation: "findMany",
                    query: `SELECT * FROM ${modelData.name}`,
                    resultVar: `${modelData.name}Result`,
                  },
                },
                {
                  id: `logic_node_${uniqueId}`,
                  type: "logic",
                  position: { x: 100, y: 400 },
                  data: {
                    label: "Logic",
                    fields: [],
                    queryFields: [],
                    code: "//commented logic"
                  },
                },
                {
                  id: `output_node_${uniqueId}`,
                  type: "outputs",
                  position: { x: 100, y: 500 },
                  data: {
                    label: "Response",
                    outputType: "json",
                    fields: modelData.fields.map((field) => ({
                      name: field.name,
                      type:
                        field.type === "primary key"
                          ? "number"
                          : field.type === "long text"
                          ? "string"
                          : field.type === "big number"
                          ? "number"
                          : field.type,
                    })),
                    resultVar: `${modelData.name}Result`,
                    statusCode: 200,
                  },
                },
              ],
              edges: [
                {
                  id: `url-to-auth_${uniqueId}`,
                  source: `url_node_${uniqueId}`,
                  target: `auth_node_${uniqueId}`,
                },
                {
                  id: `auth-to-db_${uniqueId}`,
                  source: `auth_node_${uniqueId}`,
                  target: `db_find_node_${uniqueId}`,
                },
                {
                  id: `db-to-logic_${uniqueId}`,
                  source: `db_find_node_${uniqueId}`,
                  target: `logic_node_${uniqueId}`,
                },
                {
                  id: `logic-to-output_${uniqueId}`,
                  source: `logic_node_${uniqueId}`,
                  target: `output_node_${uniqueId}`,
                },
              ],
            },
          };

          const getOneRoute = {
            id: `route_${Date.now()}_${Math.random()
              .toString(36)
              .substr(2, 9)}`,
            name: `Get One ${modelData.name}`,
            method: "GET",
            url: `/api/${modelData.name.toLowerCase()}/:id`,
            flowData: {
              nodes: [
                {
                  id: `url_node_${uniqueId}_1`,
                  type: "url",
                  position: { x: 100, y: 100 },
                  data: {
                    label: `Url`,
                    apiname: `Get One ${modelData.name}`,
                    path: `/api/${modelData.name.toLowerCase()}/:id`,
                    method: "GET",
                    queryFields: [
                      {
                        name: "id",
                        type: "number",
                        validation: "required",
                      },
                    ],
                  },
                },
                {
                  id: `auth_node_${uniqueId}_1`,
                  type: "auth",
                  position: { x: 100, y: 200 },
                  data: {
                    label: "Auth",
                    authType: "none",
                  },
                },
                {
                  id: `db_query_node_${uniqueId}_1`,
                  type: "db-query",
                  position: { x: 100, y: 300 },
                  data: {
                    label: "Database Find",
                    model: modelData.name,
                    operation: "findOne",
                    query: `SELECT * FROM ${modelData.name} WHERE id=id`,
                    resultVar: `${modelData.name}OneResult`,
                  },
                },
                {
                  id: `logic_node_${uniqueId}_1`,
                  type: "logic",
                  position: { x: 100, y: 400 },
                  data: {
                    label: "Logic",
                    fields: [],
                    queryFields: [],
                    code: "//commented logic"
                  },
                },
                {
                  id: `output_node_${uniqueId}_1`,
                  type: "outputs",
                  position: { x: 100, y: 500 },
                  data: {
                    label: "Response",
                    outputType: "json",
                    fields: [
                      ...modelData.fields.map((field) => ({
                        name: field.name,
                        type:
                          field.type === "primary key"
                            ? "number"
                            : field.type === "long text"
                            ? "string"
                            : field.type === "big number"
                            ? "number"
                            : field.type,
                      })),
                      { name: "error", type: "boolean" },
                    ],
                    resultVar: `${modelData.name}OneResult`,
                    statusCode: 200,
                  },
                },
              ],
              edges: [
                {
                  id: `url-to-auth_${uniqueId}_1`,
                  source: `url_node_${uniqueId}_1`,
                  target: `auth_node_${uniqueId}_1`,
                },
                {
                  id: `auth-to-db_${uniqueId}_1`,
                  source: `auth_node_${uniqueId}_1`,
                  target: `db_query_node_${uniqueId}_1`,
                },
                {
                  id: `db-to-logic_${uniqueId}_1`,
                  source: `db_query_node_${uniqueId}_1`,
                  target: `logic_node_${uniqueId}_1`,
                },
                {
                  id: `logic-to-output_${uniqueId}_1`,
                  source: `logic_node_${uniqueId}_1`,
                  target: `output_node_${uniqueId}_1`,
                },
              ],
            },
          };

          const deleteRoute = {
            id: `route_${Date.now()}_${Math.random()
              .toString(36)
              .substr(2, 9)}`,
            name: `Delete One ${modelData.name}`,
            method: "DELETE",
            url: `/api/${modelData.name.toLowerCase()}/:id`,
            flowData: {
              nodes: [
                {
                  id: `url_node_${uniqueId}_2`,
                  type: "url",
                  position: { x: 100, y: 100 },
                  data: {
                    label: `Url`,
                    apiname: `Delete One ${modelData.name}`,
                    path: `/api/${modelData.name.toLowerCase()}/:id`,
                    method: "DELETE",
                    queryFields: [
                      {
                        name: "id",
                        type: "number",
                        validation: "required",
                      },
                    ],
                  },
                },
                {
                  id: `auth_node_${uniqueId}_2`,
                  type: "auth",
                  position: { x: 100, y: 200 },
                  data: {
                    label: "Auth",
                    authType: "none",
                  },
                },
                {
                  id: `db_delete_node_${uniqueId}_2`,
                  type: "db-delete",
                  position: { x: 100, y: 300 },
                  data: {
                    label: "Database Delete",
                    model: modelData.name,
                    operation: "delete",
                    query: `DELETE FROM ${modelData.name} WHERE id=id`,
                    resultVar: `${modelData.name}DeleteResult`,
                  },
                },
                {
                  id: `logic_node_${uniqueId}_2`,
                  type: "logic",
                  position: { x: 100, y: 400 },
                  data: {
                    label: "Logic",
                    fields: [],
                    queryFields: [],
                    code: "//commented logic"
                  },
                },
                {
                  id: `output_node_${uniqueId}_2`,
                  type: "outputs",
                  position: { x: 100, y: 500 },
                  data: {
                    label: "Response",
                    outputType: "json",
                    fields: [
                      { name: "error", type: "boolean" },
                      { name: "id", type: "integer" },
                    ],
                    statusCode: 200,
                    resultVar: `${modelData.name}DeleteResult`,
                  },
                },
              ],
              edges: [
                {
                  id: `url-to-auth_${uniqueId}_2`,
                  source: `url_node_${uniqueId}_2`,
                  target: `auth_node_${uniqueId}_2`,
                },
                {
                  id: `auth-to-db_${uniqueId}_2`,
                  source: `auth_node_${uniqueId}_2`,
                  target: `db_delete_node_${uniqueId}_2`,
                },
                {
                  id: `db-to-logic_${uniqueId}_2`,
                  source: `db_delete_node_${uniqueId}_2`,
                  target: `logic_node_${uniqueId}_2`,
                },
                {
                  id: `logic-to-output_${uniqueId}_2`,
                  source: `logic_node_${uniqueId}_2`,
                  target: `output_node_${uniqueId}_2`,
                },
              ],
            },
          };

          const createRoute = {
            id: `route_${Date.now()}_${Math.random()
              .toString(36)
              .substr(2, 9)}`,
            name: `Create ${modelData.name}`,
            method: "POST",
            url: `/api/${modelData.name.toLowerCase()}`,
            flowData: {
              nodes: [
                {
                  id: `url_node_${uniqueId}_3`,
                  type: "url",
                  position: { x: 100, y: 100 },
                  data: {
                    label: `Url`,
                    apiname: `Create ${modelData.name}`,
                    path: `/api/${modelData.name.toLowerCase()}`,
                    method: "POST",
                    fields: modelData.fields
                      .filter((field) => field.type !== "primary key")
                      .map((field) => ({
                        name: field.name,
                        type:
                          field.type === "long text"
                            ? "string"
                            : field.type === "big number"
                            ? "number"
                            : field.type,
                        validation: field.required ? "required" : "",
                      })),
                  },
                },
                {
                  id: `auth_node_${uniqueId}_3`,
                  type: "auth",
                  position: { x: 100, y: 200 },
                  data: {
                    label: "Auth",
                    authType: "none",
                  },
                },
                {
                  id: `db_insert_node_${uniqueId}_3`,
                  type: "db-insert",
                  position: { x: 100, y: 300 },
                  data: {
                    label: "Database Insert",
                    model: modelData.name,
                    operation: "create",
                    query: `INSERT INTO ${modelData.name} (${modelData.fields
                      .filter((field) => field.type !== "primary key")
                      .map((field) => field.name)
                      .join(", ")})
                      VALUES (${modelData.fields
                        .filter((field) => field.type !== "primary key")
                        .map((field) => `:${field.name}`)
                        .join(", ")})`,
                    resultVar: `${modelData.name}CreateResult`,
                  },
                },
                {
                  id: `logic_node_${uniqueId}_3`,
                  type: "logic",
                  position: { x: 100, y: 400 },
                  data: {
                    label: "Logic",
                    fields: [],
                    queryFields: [],
                    code: "//commented logic"
                  },
                },
                {
                  id: `output_node_${uniqueId}_3`,
                  type: "outputs",
                  position: { x: 100, y: 500 },
                  data: {
                    label: "Response",
                    outputType: "json",
                    fields: [
                      { name: "error", type: "boolean" },
                      { name: "id", type: "number" },
                    ],
                    resultVar: `${modelData.name}CreateResult`,
                    statusCode: 200,
                  },
                },
              ],
              edges: [
                {
                  id: `url-to-auth_${uniqueId}_3`,
                  source: `url_node_${uniqueId}_3`,
                  target: `auth_node_${uniqueId}_3`,
                },
                {
                  id: `auth-to-db_${uniqueId}_3`,
                  source: `auth_node_${uniqueId}_3`,
                  target: `db_insert_node_${uniqueId}_3`,
                },
                {
                  id: `db-to-logic_${uniqueId}_3`,
                  source: `db_insert_node_${uniqueId}_3`,
                  target: `logic_node_${uniqueId}_3`,
                },
                {
                  id: `logic-to-output_${uniqueId}_3`,
                  source: `logic_node_${uniqueId}_3`,
                  target: `output_node_${uniqueId}_3`,
                },
              ],
            },
          };

          const updateRoute = {
            id: `route_${Date.now()}_${Math.random()
              .toString(36)
              .substr(2, 9)}`,
            name: `Update ${modelData.name}`,
            method: "PUT",
            url: `/api/${modelData.name.toLowerCase()}/:id`,
            flowData: {
              nodes: [
                {
                  id: `url_node_${uniqueId}_4`,
                  type: "url",
                  position: { x: 100, y: 100 },
                  data: {
                    label: `Url`,
                    apiname: `Update ${modelData.name}`,
                    path: `/api/${modelData.name.toLowerCase()}/:id`,
                    method: "PUT",
                    queryFields: [
                      {
                        name: "id",
                        type: "number",
                        validation: "required",
                      },
                    ],
                    fields: modelData.fields
                      .filter((field) => field.type !== "primary key")
                      .map((field) => ({
                        name: field.name,
                        type:
                          field.type === "long text"
                            ? "string"
                            : field.type === "big number"
                            ? "number"
                            : field.type,
                        validation: field.validation || "",
                      })),
                  },
                },
                {
                  id: `auth_node_${uniqueId}_4`,
                  type: "auth",
                  position: { x: 100, y: 200 },
                  data: {
                    label: "Auth",
                    authType: "none",
                  },
                },
                {
                  id: `db_update_node_${uniqueId}_4`,
                  type: "db-update",
                  position: { x: 100, y: 300 },
                  data: {
                    label: "Database Update",
                    model: modelData.name,
                    operation: "update",
                    idField: "id",
                    query: `UPDATE ${modelData.name} SET ${modelData.fields
                      .filter((field) => field.type !== "primary key")
                      .map((field) => `${field.name}=:${field.name}`)
                      .join(", ")} WHERE id=:id`,
                    resultVar: `${modelData.name}UpdateResult`,
                  },
                },
                {
                  id: `logic_node_${uniqueId}_4`,
                  type: "logic",
                  position: { x: 100, y: 400 },
                  data: {
                    label: "Logic",
                    fields: [],
                    queryFields: [],
                    code: "//commented logic"
                  },
                },
                {
                  id: `output_node_${uniqueId}_4`,
                  type: "outputs",
                  position: { x: 100, y: 500 },
                  data: {
                    label: "Response",
                    outputType: "json",
                    fields: [
                      { name: "error", type: "boolean" },
                      { name: "id", type: "number" },
                    ],
                    resultVar: `${modelData.name}UpdateResult`,
                    statusCode: 200,
                  },
                },
              ],
              edges: [
                {
                  id: `url-to-auth_${uniqueId}_4`,
                  source: `url_node_${uniqueId}_4`,
                  target: `auth_node_${uniqueId}_4`,
                },
                {
                  id: `auth-to-db_${uniqueId}_4`,
                  source: `auth_node_${uniqueId}_4`,
                  target: `db_update_node_${uniqueId}_4`,
                },
                {
                  id: `db-to-logic_${uniqueId}_4`,
                  source: `db_update_node_${uniqueId}_4`,
                  target: `logic_node_${uniqueId}_4`,
                },
                {
                  id: `logic-to-output_${uniqueId}_4`,
                  source: `logic_node_${uniqueId}_4`,
                  target: `output_node_${uniqueId}_4`,
                },
              ],
            },
          };

          addRoute(getRoute as any);
          addRoute(getOneRoute as any);
          addRoute(createRoute as any);
          addRoute(updateRoute as any);
          addRoute(deleteRoute as any);
        }
        onClose();
      }
    }
  };

 
  useEffect(() => {
    if (model) {
      setModelData({
        id: model.id,
        name: model.name,
        fields: [...model.fields],
      });
    } else {
      setModelData(createInitialModelData());
    }
  }, [model, isOpen]);

  if (!isOpen) return null;

  return (
    <div
      className="flex fixed inset-0 z-50 justify-center items-center p-5 bg-black bg-opacity-50"
      style={{ marginTop: "0px" }}
    >
      <div className="overflow-auto relative w-full max-w-2xl h-full min-h-full max-h-full bg-white rounded-lg">
        <div className="flex sticky top-0 justify-between items-center p-4 m-auto w-full bg-white border-b border-gray-200">
          <h2 className="text-lg font-semibold">
            {model ? "Edit Model" : "Add New Model"}
          </h2>
          <button onClick={onClose} className="p-1 rounded hover:bg-gray-100">
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="overflow-y-auto p-4">
          <div className="mb-4">
            <label className="block mb-1 text-sm font-medium">Model Name</label>
            <input
              type="text"
              value={modelData.name}
              onChange={(e) =>
                setModelData({ ...modelData, name: e.target.value })
              }
              className="p-2 w-full rounded border"
              placeholder="Enter model name"
            />
          </div>

          <div className="mb-4">
            <label className="block mb-2 text-sm font-medium">Fields</label>
            <div className="space-y-2">
              {modelData.fields.map((field, index) => (
                <div key={index} className="flex gap-2 items-center">
                  <input
                    type="text"
                    value={field.name}
                    readOnly
                    className="flex-1 p-2 bg-gray-50 rounded border"
                  />
                  <span className="px-2 py-1 text-sm bg-gray-100 rounded">
                    {field.type === "integer" && field.relation ? field.relation : field.type}
                  </span>
                  {field.validation && (
                    <span className="px-2 py-1 text-sm bg-blue-100 rounded">
                      {field.validation}
                    </span>
                  )}
                  <button
                    onClick={() => handleRemoveField(index)}
                    className="p-1 text-red-500 rounded hover:bg-red-50"
                  >
                    <Trash className="w-4 h-4" />
                  </button>
                </div>
              ))}
            </div>

            <div className="mt-2 space-y-2">
              <div className="flex gap-2">
                <input
                  type="text"
                  value={newField.name}
                  onChange={(e) =>
                    setNewField({ ...newField, name: e.target.value })
                  }
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      handleAddField();
                    }
                  }}
                  className="flex-1 p-2 rounded border"
                  placeholder="Field name"
                />
                <select
                  value={newField.type}
                  onChange={(e) =>
                    setNewField({ ...newField, type: e.target.value })
                  }
                  className="p-2 w-32 rounded border"
                >
                  {fieldTypes.map((type) => (
                    <option key={type} value={type}>
                      {type}
                    </option>
                  ))}
                </select>
              </div>

              <div className="flex gap-2">
                <input
                  type="text"
                  value={newField.validation}
                  onChange={(e) => {
                    const newValidation = e.target.value;
                    setNewField({ ...newField, validation: newValidation });
                  }}
                  className="flex-1 p-2 rounded border"
                  placeholder="Validation Rules"
                />
                <select
                  onChange={(e) => {
                    const selectedValidation = e.target.value;
                    const currentValidations = newField.validation.split(',').map(v => v.trim());
                    if (selectedValidation && !currentValidations.includes(selectedValidation)) {
                        if (newField.validation) {
                            currentValidations.push(selectedValidation);
                        } else {
                            currentValidations[0] = selectedValidation; // If validation is empty, set the first value
                        }
                    }
                    setNewField({ ...newField, validation: currentValidations.join(',') });
                  }}
                  className="p-2 w-32 rounded border"
                >
                  <option value="">Validation</option>
                  {validationRules.map((rule) => (
                    <option key={rule} value={rule}>
                      {rule}
                    </option>
                  ))}
                </select>
              </div>
              <div className="flex gap-2">
                <input
                  type="text"
                  value={newField.defaultValue}
                  onChange={(e) =>
                    setNewField({ ...newField, defaultValue: e.target.value })
                  }
                  className="flex-1 p-2 rounded border"
                  placeholder="Default value"
                />
                {newField.type === "integer" && (
                  <select
                    placeholder="Join Model?"
                    onChange={(e) => {
                      const selectedModel = e.target.value;
                      if (selectedModel) {
                        setModelRelations([...modelRelations, selectedModel]);
                      }
                    }}
                    className="p-2 w-32 rounded border"
                  >
                    <option value="">Join Model?</option>
                    {state.models.map((model) => (
                      <option key={model.id} value={model.name}>
                        {model.name}
                      </option>
                    ))}
                  </select>
                )}
              </div>

              {newField.type === "mapping" && (
                <input
                  type="text"
                  value={newField.mapping || ""}
                  onChange={(e) =>
                    setNewField({ ...newField, mapping: e.target.value })
                  }
                  className="p-2 w-full rounded border"
                  placeholder="key:value,key2:value2"
                />
              )}

              {newField.validation &&
                [
                  "pattern",
                  "enum",
                  "min",
                  "max",
                  "minLength",
                  "maxLength",
                ].includes(newField.validation) && (
                  <div className="flex gap-2">
                    {newField.validation === "pattern" && (
                      <input
                        type="text"
                        value={newField.validationOptions?.pattern || ""}
                        onChange={(e) =>
                          setNewField({
                            ...newField,
                            validationOptions: {
                              ...newField.validationOptions,
                              pattern: e.target.value,
                            },
                          })
                        }
                        className="flex-1 p-2 rounded border"
                        placeholder="Regular expression pattern"
                      />
                    )}
                    {newField.validation === "enum" && (
                      <input
                        type="text"
                        value={
                          newField.validationOptions?.enum?.join(",") || ""
                        }
                        onChange={(e) =>
                          setNewField({
                            ...newField,
                            validationOptions: {
                              ...newField.validationOptions,
                              enum: e.target.value.split(","),
                            },
                          })
                        }
                        className="flex-1 p-2 rounded border"
                        placeholder="Comma-separated values"
                      />
                    )}
                    {["min", "max", "minLength", "maxLength"].includes(
                      newField.validation
                    ) && (
                      <input
                        type="number"
                        value={
                          newField.validationOptions?.[
                            newField.validation as keyof typeof newField.validationOptions
                          ] || ""
                        }
                        onChange={(e) =>
                          setNewField({
                            ...newField,
                            validationOptions: {
                              ...newField.validationOptions,
                              [newField.validation]: parseFloat(e.target.value),
                            },
                          })
                        }
                        className="flex-1 p-2 rounded border"
                        placeholder={`Enter ${newField.validation} value`}
                      />
                    )}
                  </div>
                )}

              <button
                onClick={handleAddField}
                className="flex gap-2 justify-center items-center px-4 py-2 w-full text-white bg-blue-500 rounded transition-colors hover:bg-blue-600"
              >
                <Plus className="w-4 h-4" />
                Add Field
              </button>

              {!model && (
                <div className="flex items-center mt-4">
                  <input
                    type="checkbox"
                    checked={createCrudApis}
                    onChange={(e) => setCreateCrudApis(e.target.checked)}
                    className="mr-2"
                  />
                  <label className="text-sm">Create CRUD APIs</label>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="flex gap-2 justify-end p-4 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 rounded hover:bg-gray-100"
          >
            Cancel
          </button>
          <button
            onClick={() => {
              handleSave();
              onClose();
            }}
            className="px-4 py-2 text-white bg-blue-500 rounded hover:bg-blue-600"
          >
            {model ? "Update" : "Save"} Model
          </button>
        </div>
      </div>
    </div>
  );
}
