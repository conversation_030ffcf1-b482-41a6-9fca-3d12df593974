import React, { useState, useEffect } from "react";
import { X, Plus, Trash } from "lucide-react";

interface Field {
  name: string;
  type: string;
  validation?: string;
  fields?: Field[];
}

interface ArrayFieldModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (type: string, objectFields?: Field[]) => void;
  initialType: string;
  initialObjectFields?: Field[];
}

function FieldTypeSelect({ value, onChange }: { value: string, onChange: (value: string) => void }) {
  return (
    <select
      value={value}
      onChange={(e) => onChange(e.target.value)}
      className="p-2 w-24 text-sm rounded border"
    >
      <option value="string">String</option>
      <option value="number">Number</option>
      <option value="integer">Integer</option>
      <option value="boolean">Boolean</option>
      <option value="date">Date</option>
      <option value="datetime">Datetime</option>
      <option value="object">Object</option>
    </select>
  );
}

export default function ArrayFieldModal({ isOpen, onClose, onSave, initialType = 'string', initialObjectFields = [] }: ArrayFieldModalProps) {
  const [selectedType, setSelectedType] = useState(initialType);
  const [showObjectFields, setShowObjectFields] = useState(selectedType === 'object');
  const [objectFields, setObjectFields] = useState<Field[]>(initialObjectFields);
  const [newField, setNewField] = useState<Field>({ name: '', type: 'string' });
  const [expandedFields, setExpandedFields] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (isOpen) {
      if (typeof initialType === 'object' && initialType.type === 'object') {
        setSelectedType('object');
        setObjectFields(initialType.fields || []);
        setShowObjectFields(true);
      } else {
        setSelectedType(initialType);
        setObjectFields([]);
        setShowObjectFields(false);
      }
    }
  }, [isOpen, initialType]);

  const addField = () => {
    if (!newField.name.trim()) return;
    setObjectFields([...objectFields, { ...newField }]);
    setNewField({ name: '', type: 'string' });
  };

  const removeField = (index: number) => {
    const newFields = [...objectFields];
    newFields.splice(index, 1);
    setObjectFields(newFields);
  };

  const renderField = (field: Field, index: number, path: string = '') => {
    const fieldPath = path ? `${path}.${field.name}` : field.name;
    const isExpanded = expandedFields.has(fieldPath);

    return (
      <div key={index} className="ml-4">
        <div className="flex mb-2">
          <input
            type="text"
            value={field.name}
            onChange={(e) => {
              const newFields = [...objectFields];
              newFields[index].name = e.target.value;
              setObjectFields(newFields);
            }}
            className="flex-1 p-2 text-sm rounded border"
            placeholder="Field name"
          />
          <FieldTypeSelect
            value={field.type}
            onChange={(value) => {
              const newFields = [...objectFields];
              newFields[index].type = value;
              if (value === 'object') {
                newFields[index].fields = newFields[index].fields || [];
              }
              setObjectFields(newFields);
            }}
          />
          <button
            onClick={() => removeField(index)}
            className="p-2 ml-1 text-red-500 rounded hover:bg-red-50"
          >
            <Trash className="w-4 h-4" />
          </button>
        </div>
        
        {field.type === 'object' && (
          <div className="ml-4">
            <button
              onClick={() => {
                const newExpanded = new Set(expandedFields);
                if (isExpanded) {
                  newExpanded.delete(fieldPath);
                } else {
                  newExpanded.add(fieldPath);
                }
                setExpandedFields(newExpanded);
              }}
              className="mb-2 text-sm text-blue-500 hover:text-blue-600"
            >
              {isExpanded ? '- Collapse' : '+ Expand'} Object Fields
            </button>
            
            {isExpanded && (
              <div className="pl-4 border-l-2 border-gray-200">
                {field.fields?.map((nestedField, nestedIndex) => (
                  renderField(nestedField, nestedIndex, fieldPath)
                ))}
                <div className="flex gap-1 mt-2">
                  <input
                    type="text"
                    value={newField.name}
                    onChange={(e) => setNewField({ ...newField, name: e.target.value })}
                    className="flex-1 p-2 text-sm rounded border"
                    placeholder="New nested field name"
                  />
                  <FieldTypeSelect
                    value={newField.type}
                    onChange={(value) => setNewField({ ...newField, type: value })}
                  />
                  <button
                    onClick={() => {
                      if (!newField.name.trim()) return;
                      const newFields = [...objectFields];
                      if (!newFields[index].fields) {
                        newFields[index].fields = [];
                      }
                      newFields[index].fields?.push({ ...newField });
                      setObjectFields(newFields);
                      setNewField({ name: '', type: 'string' });
                    }}
                    className="p-2 text-white bg-blue-500 rounded hover:bg-blue-600"
                  >
                    <Plus className="w-4 h-4" />
                  </button>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  if (!isOpen) return null;

  return (
    <div className="flex fixed inset-0 z-50 justify-center items-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg p-6 w-1/2 max-h-[80vh] overflow-y-auto relative">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Array Type</h3>
          <button 
            onClick={onClose}
            className="p-1 rounded hover:bg-gray-100"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
        
        <div className="mb-4">
          <label className="block mb-1 text-sm font-medium">Select Array Item Type</label>
          <select
            value={selectedType}
            onChange={(e) => {
              setSelectedType(e.target.value);
              setShowObjectFields(e.target.value === 'object');
            }}
            className="p-2 w-full text-sm rounded border"
          >
            <option value="string">String</option>
            <option value="number">Number</option>
            <option value="integer">Integer</option>
            <option value="boolean">Boolean</option>
            <option value="date">Date</option>
            <option value="datetime">Datetime</option>
            <option value="object">Object</option>
          </select>
        </div>

        {showObjectFields && (
          <div className="mt-4">
            <label className="block mb-2 text-sm font-medium">Object Fields</label>
            {objectFields.map((field, index) => renderField(field, index))}
            <div className="flex gap-1 mt-2">
              <input
                type="text"
                value={newField.name}
                onChange={(e) => setNewField({ ...newField, name: e.target.value })}
                className="flex-1 p-2 text-sm rounded border"
                placeholder="New field name"
              />
              <select
                value={newField.type}
                onChange={(e) => setNewField({ ...newField, type: e.target.value })}
                className="p-2 w-24 text-sm rounded border"
              >
                <option value="string">String</option>
                <option value="number">Number</option>
                <option value="integer">Integer</option>
                <option value="boolean">Boolean</option>
                <option value="date">Date</option>
                <option value="datetime">Datetime</option>
                <option value="object">Object</option>
              </select>
              <button
                onClick={addField}
                className="p-2 text-white bg-blue-500 rounded hover:bg-blue-600"
              >
                <Plus className="w-4 h-4" />
              </button>
            </div>
          </div>
        )}

        <div className="flex justify-end mt-4">
          <button
            onClick={() => {
              onSave(selectedType, selectedType === 'object' ? objectFields : undefined);
              onClose();
            }}
            className="px-4 py-2 text-white bg-blue-500 rounded hover:bg-blue-600"
          >
            Save
          </button>
        </div>
      </div>
    </div>
  );
} 