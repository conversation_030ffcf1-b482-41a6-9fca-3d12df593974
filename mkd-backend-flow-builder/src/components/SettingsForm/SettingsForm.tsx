import React from "react";
import { Save } from "lucide-react";
import { useFlowContext } from "@/store/FlowContext";
import toast from "react-hot-toast";
import { cleanConfiguration } from "@/utils/ConfigurationService";

interface Route {
  id: string;
  name: string;
  method: string;
  url: string;
  flowData?: {
    nodes: {
      id: string;
      type: string;
      position: {
        x: number;
        y: number;
      };
      data: {
        label: string;
        fields: Array<{ name: string; type: string }>;
        queryFields?: Array<{ name: string; type: string }>;
        [key: string]: any;
      };
    }[];
    edges: {
      source: string;
      target: string;
      id: string;
      sourceHandle: string | null;
      targetHandle: string | null;
    }[];
  };
}

interface Role {
  name: string;
  slug: string;
  permissions: {
    routes: string[];
    [key: string]: any;
  };
}

// based on the type of the payment option, we need to update updateModels

// check type mapping in migration.js



const stripeProductTable = [
  {
    name: "id",
    type: "primary key",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "created_at",
    type: "date",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "updated_at",
    type: "datetime",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "name",
    type: "string",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "product_id",
    type: "string",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "stripe_id",
    type: "string",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "object",
    type: "long text",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "status",
    type: "integer",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
];

const stripePriceTable = [
  {
    name: "id",
    type: "primary key",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "created_at",
    type: "date",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "updated_at",
    type: "datetime",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "name",
    type: "string",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "product_id",
    type: "string",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "stripe_id",
    type: "string",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "is_usage_metered",
    type: "integer",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "usage_limit",
    type: "integer",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "object",
    type: "medium text",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "amount",
    type: "float",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "trial_days",
    type: "integer",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "type",
    type: "string",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "status",
    type: "integer",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
];

const stripeSubscriptionTable = [
  {
    name: "id",
    type: "primary key",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "stripe_id",
    type: "string",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "price_id",
    type: "string",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "user_id",
    type: "integer",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "object",
    type: "json",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "status",
    type: "string",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "is_lifetime",
    type: "boolean",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "created_at",
    type: "date",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "updated_at",
    type: "datetime",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
];

const stripeCheckoutTable = [
  {
    name: "id",
    type: "primary key",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "user_id",
    type: "integer",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "stripe_id",
    type: "string",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "object",
    type: "json",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "created_at",
    type: "date",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "updated_at",
    type: "datetime",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
];

const stripeWebhookTable = [
  {
    name: "id",
    type: "primary key",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "stripe_id",
    type: "string",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "idempotency_key",
    type: "string",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "description",
    type: "string",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "event_type",
    type: "string",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "resource_type",
    type: "string",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "object",
    type: "json",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "is_handled",
    type: "boolean",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "created_at",
    type: "date",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "updated_at",
    type: "datetime",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
];

const stripeSettingTable = [
  {
    name: "id",
    type: "primary key",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "key",
    type: "string",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "value",
    type: "medium text",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "created_at",
    type: "date",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "updated_at",
    type: "datetime",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
];

const stripeOrderTable = [
  {
    name: "id",
    type: "primary key",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "user_id",
    type: "integer",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "price_id",
    type: "string",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "stripe_id",
    type: "string",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "object",
    type: "json",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "created_at",
    type: "date",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "updated_at",
    type: "datetime",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
];

const stripeInvoiceTable = [
  {
    name: "id",
    type: "primary key",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "user_id",
    type: "integer",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "stripe_id",
    type: "string",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "object",
    type: "json",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "created_at",
    type: "date",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "updated_at",
    type: "datetime",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
];

const stripeRefundTable = [
  {
    name: "id",
    type: "primary key",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "user_id",
    type: "integer",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "stripe_id",
    type: "string",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "object",
    type: "json",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "charge_id",
    type: "string",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "subscription_id",
    type: "string",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "amount",
    type: "string",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "currency",
    type: "string",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "reason",
    type: "string",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "status",
    type: "string",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "created_at",
    type: "date",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "updated_at",
    type: "datetime",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
];

const stripeDisputeTable = [
  {
    name: "id",
    type: "primary key",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "user_id",
    type: "integer",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "stripe_id",
    type: "string",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "subscription_id",
    type: "string",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "object",
    type: "json",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "amount",
    type: "string",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "reason",
    type: "string",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "reason_description",
    type: "string",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "status",
    type: "string",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "created_at",
    type: "date",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
  {
    name: "updated_at",
    type: "datetime",
    defaultValue: "",
    validation: "",
    validationOptions: {},
  },
];

// TODO: Add other stripe tables.


export default function SettingsForm() {
  const {
    state: { settings, models, roles, routes },
    updateSettings,
    updateModels,
    updateRoutes,
    updateRoles,
  } = useFlowContext();

  const handleChange = (
    e:
      | React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
      | { target: { name: string; value: any } }
  ) => {
    const newSettings = {
      ...settings,
      [e.target.name]: e.target.value,
      // id: settings?.id || "",
      // model_namespace: settings?.model_namespace || "",
      // payment_option: e.target.value || "",
    };
    updateSettings(newSettings);
    if (e.target.name === "payment_option" && e.target.value != "none") {
      updateModels([
        ...models,
        {
          id: `model_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          name: "stripe_product",
          fields: stripeProductTable,
        },
        {
          id: `model_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          name: "stripe_price",
          fields: stripePriceTable,
        },
        {
          id: `model_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          name: "stripe_subscription",
          fields: stripeSubscriptionTable,
        },
        {
          id: `model_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          name: "stripe_checkout",
          fields: stripeCheckoutTable,
        },
        {
          id: `model_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          name: "stripe_webhook",
          fields: stripeWebhookTable,
        },
        {
          id: `model_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          name: "stripe_setting",
          fields: stripeSettingTable,
        },
        {
          id: `model_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          name: "stripe_order",
          fields: stripeOrderTable,
        },
        {
          id: `model_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          name: "stripe_invoice",
          fields: stripeInvoiceTable,
        },
        {
          id: `model_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          name: "stripe_refund",
          fields: stripeRefundTable,
        },
        {
          id: `model_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          name: "stripe_dispute",
          fields: stripeDisputeTable,
        },
      ]);
    }
  };

  const handleSave = () => {
    try {
      if (settings) {
        updateSettings(settings);
        toast.success("Settings saved successfully!", {
          position: "top-right",
          duration: 3000,
          style: {
            background: "#4CAF50",
            color: "#fff",
          },
        });
      }
    } catch (error) {
      toast.error("Failed to save settings", {
        position: "top-right",
        duration: 3000,
      });
    }
  };

  const handleSaveConfiguration = () => {
    const configuration = {
      settings,
      models,
      routes,
      roles,
    };
console.log(configuration);
    const blob = new Blob(
      [JSON.stringify(cleanConfiguration(configuration), null, 2)],
      {
        type: "application/json",
      }
    );
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `configuration-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast.success("Configuration saved successfully", {
      position: "top-right",
      duration: 3000,
    });
  };

  const handleFileUpload = (content: string) => {
    try {
      const configuration = JSON.parse(content);

      // Update all state directly
      if (configuration.settings) {
        updateSettings(configuration.settings);
      }
      if (configuration.models) {
        updateModels(configuration.models);
      }
      if (configuration.routes) {
        updateRoutes(configuration.routes);
      }
      if (configuration.roles) {
        updateRoles(configuration.roles);
      }

      toast.success("Configuration imported successfully", {
        position: "top-right",
        duration: 3000,
      });
    } catch (error) {
      console.error("Error importing configuration:", error);
      toast.error("Failed to import configuration. Invalid file format.", {
        position: "top-right",
        duration: 3000,
      });
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <label className="block mb-1 text-sm font-medium">ID</label>
        <input
          type="text"
          name="id"
          value={settings?.id || ""}
          onChange={handleChange}
          className="p-2 w-full rounded border"
          placeholder="Enter ID"
        />
      </div>

      <div>
        <label className="block mb-1 text-sm font-medium">
          Model Namespace
        </label>
        <input
          type="text"
          name="model_namespace"
          value={settings?.model_namespace}
          onChange={handleChange}
          className="p-2 w-full rounded border"
          placeholder="Enter Model Namespace"
        />
      </div>
      <div>
        <label className="block mb-1 text-sm font-medium">Global Key</label>
        <input
          type="text"
          name="globalKey"
          value={settings?.globalKey || ""}
          onChange={handleChange}
          className="p-2 w-full rounded border"
          placeholder="Enter global key"
        />
      </div>

      <div>
        <label className="block mb-1 text-sm font-medium">Database Type</label>
        <select
          name="databaseType"
          value={settings?.databaseType || ""}
          onChange={handleChange}
          className="p-2 w-full rounded border"
        >
          <option value="">Select Database Type</option>
          <option value="mysql">MySQL</option>
          <option value="postgresql">PostgreSQL</option>
        </select>
      </div>

      <div>
        <label className="block mb-1 text-sm font-medium">
          Authentication Type
        </label>
        <select
          name="authType"
          value={settings?.authType || ""}
          onChange={handleChange}
          className="p-2 w-full rounded border"
        >
          <option value="session">Session</option>
          <option value="jwt">JWT</option>
        </select>
      </div>

      <div>
        <label className="block mb-1 text-sm font-medium">Timezone</label>
        <select
          name="timezone"
          value={settings?.timezone || ""}
          onChange={handleChange}
          className="p-2 w-full rounded border"
        >
          <option value="UTC">UTC</option>
          <option value="America/New_York">America/New_York</option>
          <option value="Europe/London">Europe/London</option>
          <option value="Asia/Tokyo">Asia/Tokyo</option>
        </select>
      </div>

      <div className="space-y-2">
        <label className="block text-sm font-medium">
          Database Credentials
        </label>

        <div>
          <input
            type="text"
            name="dbHost"
            value={settings?.dbHost || ""}
            onChange={handleChange}
            className="p-2 mb-2 w-full rounded border"
            placeholder="Host"
          />
        </div>

        <div>
          <input
            type="text"
            name="dbPort"
            value={settings?.dbPort || ""}
            onChange={handleChange}
            className="p-2 mb-2 w-full rounded border"
            placeholder="Port"
          />
        </div>

        <div>
          <input
            type="text"
            name="dbUser"
            value={settings?.dbUser || ""}
            onChange={handleChange}
            className="p-2 mb-2 w-full rounded border"
            placeholder="Username"
          />
        </div>

        <div>
          <input
            type="text"
            name="dbPassword"
            value={settings?.dbPassword || ""}
            onChange={handleChange}
            className="p-2 mb-2 w-full rounded border"
            placeholder="Password"
          />
        </div>

        <div>
          <input
            type="text"
            name="dbName"
            value={settings?.dbName || ""}
            onChange={handleChange}
            className="p-2 w-full rounded border"
            placeholder="Database Name"
          />
        </div>
      </div>

      <div className="flex items-center mb-4">
        <input
          type="checkbox"
          name="isPWA"
          checked={settings?.isPWA || false}
          onChange={(e) =>
            handleChange({
              target: {
                name: "isPWA",
                value: e.target.checked,
              },
            })
          }
          className="mr-2"
        />
        <label className="text-sm font-medium">Is PWA</label>
      </div>

      <div className="flex items-center mb-4">
        <input
          type="checkbox"
          name="isMultiTenant"
          checked={settings?.isMultiTenant || false}
          onChange={(e) =>
            handleChange({
              target: {
                name: "isMultiTenant",
                value: e.target.checked,
              },
            })
          }
          className="mr-2"
        />
        <label className="text-sm font-medium">Is Multi-Tenant</label>
      </div>

      <div>
        <label className="block mb-1 text-sm font-medium">Payment Option</label>
        <select
          name="payment_option"
          value={settings?.payment_option || ""}
          onChange={(e) =>
            handleChange({
              target: { name: "payment_option", value: e.target.value },
            })
          }
          className="p-2 w-full rounded border"
        >
          <option value="">Select Payment Option</option>
          <option value="none">None</option>
          <option value="free">Free</option>
          <option value="one_time_payment">One Time Payment</option>
          <option value="subscription">Subscription</option>
          <option value="seats">Seats</option>
          <option value="usage">Usage</option>
        </select>
      </div>

      <button
        onClick={handleSave}
        className="flex gap-2 justify-center items-center px-4 py-2 w-full text-white bg-blue-500 rounded transition-colors hover:bg-blue-600"
      >
        <Save className="w-4 h-4" />
        Save Settings
      </button>

      <div className="mt-6">
        <button
          onClick={handleSaveConfiguration}
          className="px-4 py-2 w-full text-white bg-green-500 rounded transition-colors hover:bg-green-600"
        >
          Save Configuration
        </button>
      </div>

      <div className="mt-6">
        <input
          type="file"
          accept=".json"
          onChange={(e) => {
            const file = e.target.files?.[0];
            if (file) {
              const reader = new FileReader();
              reader.onload = (event) => {
                const content = event.target?.result as string;
                handleFileUpload(content);
              };
              reader.readAsText(file);
            }
          }}
          className="hidden mt-4 w-full"
        />
        <button
          onClick={() => {
            const fileInput = document.querySelector(
              'input[type="file"]'
            ) as HTMLInputElement;
            if (fileInput) {
              fileInput.click();
            }
          }}
          className="px-4 py-2 w-full text-white bg-blue-500 rounded transition-colors hover:bg-blue-600"
        >
          Import Configuration
        </button>
      </div>
    </div>
  );
}
