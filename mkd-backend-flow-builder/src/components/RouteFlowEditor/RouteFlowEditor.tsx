import React, { use<PERSON><PERSON>back, useRef, useEffect, useState } from "react";
import React<PERSON>low, {
  Background,
  Controls,
  ReactFlowProvider,
  addEdge,
  Connection,
  useReactFlow,
  applyNodeChanges,
  applyEdgeChanges,
  Panel,
  NodeChange,
  EdgeChange,
} from "reactflow";
import { ArrowLeft, Save } from "lucide-react";
import { ConfigPanel } from "@/components/ConfigPanel";
import { ComponentsPanel } from "@/components/ComponentsPanel";
import {CustomNode} from "@/components/CustomNode";
import { useFlowContext } from "@/store/FlowContext";
import { ObjectFieldModal } from "@/components/ObjectFieldModal";
import { ArrayFieldModal } from "@/components/ArrayFieldModal";
import "reactflow/dist/style.css";

const nodeTypes = {
  auth: CustomNode,
  url: CustomNode,
  outputs: CustomNode,
  logic: CustomNode,
  variable: CustomNode,
  "mock-api": CustomNode,
  "db-find": CustomNode,
  "db-insert": CustomNode,
  "db-update": CustomNode,
  "db-delete": CustomNode,
  "db-query": CustomNode,
};

interface FlowEditorContentProps {
  route: any;
  onClose: () => void;
}

function FlowEditorContent({ route, onClose }: FlowEditorContentProps) {
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const { project } = useReactFlow();
  const {
    state: { nodes, edges, selectedNode },
    setNodes,
    setEdges,
    setSelectedNode,
    updateNode,
    updateRoute,
  } = useFlowContext();

  const [isObjectModalOpen, setIsObjectModalOpen] = useState(false);
  const [editingFieldIndex, setEditingFieldIndex] = useState<number | null>(null);
  const [editingNodeId, setEditingNodeId] = useState<string | null>(null);
  const [editingArrayName, setEditingArrayName] = useState<string | null>(null);
  const [isArrayModalOpen, setIsArrayModalOpen] = useState(false);

  const onNodesChange = useCallback(
    (changes: NodeChange[]) => {
      setNodes((nds) => applyNodeChanges(changes, nds));
    },
    [setNodes]
  );

  const onEdgesChange = useCallback(
    (changes: EdgeChange[]) =>
      setEdges((eds) => applyEdgeChanges(changes, eds)),
    [setEdges]
  );

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = "move";
  }, []);

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();

      const type = event.dataTransfer.getData("application/reactflow");
      if (!type || !reactFlowWrapper.current) return;

      const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();
      const position = project({
        x: event.clientX - reactFlowBounds.left - 75,
        y: event.clientY - reactFlowBounds.top - 25,
      });

      const newNode = {
        id: `node_${Date.now()}`,
        type,
        position,
        data: {
          label: type.charAt(0).toUpperCase() + type.slice(1).replace("-", " "),
          fields: [],
          queryFields: [],
        },
      };
      // console.log("nodes >>", nodes);
      // console.log("[...nodes, newNode] >>", [...nodes, newNode]);
      setNodes([...nodes, newNode]);
    },
    [project, setNodes]
  );

  const onNodeClick = useCallback(
    (_: React.MouseEvent, node: any) => {
      setSelectedNode(node);
    },
    [setSelectedNode]
  );

  const onPaneClick = useCallback(() => {
    setSelectedNode(null);
  }, [setSelectedNode]);

  const handleSave = () => {
    updateRoute({
      ...route,
      flowData: {
        nodes,
        edges,
      },
    });
    onClose();
  };

  const handleObjectFieldSave = (fields: any[]) => {
    if (editingNodeId && editingFieldIndex !== null && editingArrayName) {
      const node = nodes.find(n => n.id === editingNodeId);
      if (!node) return;

      const newData = { ...node.data };
      const array = [...newData[editingArrayName]];
      
      const existingName = array[editingFieldIndex]?.name || '';
      
      array[editingFieldIndex] = {
        name: existingName,
        type: 'object',
        value: fields
      };
      
      newData[editingArrayName] = array;
      updateNode(editingNodeId, newData);
    }
    setIsObjectModalOpen(false);
    setEditingFieldIndex(null);
    setEditingNodeId(null);
    setEditingArrayName(null);
  };

  const handleOpenObjectModal = (nodeId: string, index: number, arrayName: string, fieldName: string) => {
    setEditingNodeId(nodeId);
    setEditingFieldIndex(index);
    setEditingArrayName(arrayName);
    setIsObjectModalOpen(true);
    
    // Get the existing array
    const node = nodes.find(n => n.id === nodeId);
    if (!node) return;
    
    const array = [...node.data[arrayName]];
    
    // If it's a new field, add it to the array with the name
    if (index >= array.length) {
      array[index] = {
        name: fieldName,
        type: 'object',
        value: []
      };
      
      const newData = {
        ...node.data,
        [arrayName]: array
      };
      updateNode(nodeId, newData);
    }
  };

  const handleArrayFieldSave = (type: string, objectFields?: any[]) => {
    if (editingNodeId && editingFieldIndex !== null && editingArrayName) {
      const node = nodes.find(n => n.id === editingNodeId);
      if (!node) return;

      const newData = { ...node.data };
      const array = [...newData[editingArrayName]];
      
      const existingName = array[editingFieldIndex]?.name || '';
      
      array[editingFieldIndex] = {
        name: existingName,
        type: 'array',
        value: type === 'object' ? { type: 'object', fields: objectFields || [] } : type
      };
      
      newData[editingArrayName] = array;
      updateNode(editingNodeId, newData);
    }
    setIsArrayModalOpen(false);
    setEditingFieldIndex(null);
    setEditingNodeId(null);
    setEditingArrayName(null);
  };

  const handleOpenArrayModal = (nodeId: string, index: number, arrayName: string, fieldName: string) => {
    setEditingNodeId(nodeId);
    setEditingFieldIndex(index);
    setEditingArrayName(arrayName);
    setIsArrayModalOpen(true);
    
    const node = nodes.find(n => n.id === nodeId);
    if (!node) return;
    
    const array = [...node.data[arrayName]];
    
    if (index >= array.length) {
      array[index] = {
        name: fieldName,
        type: 'array',
        value: 'string'
      };
      
      const newData = {
        ...node.data,
        [arrayName]: array
      };
      updateNode(nodeId, newData);
    }
  };

  return (
    <div className="flex flex-col h-full">
      <div className="flex justify-between items-center p-4 bg-white border-b border-gray-200">
        <div className="flex items-center">
          <button
            onClick={onClose}
            className="flex gap-2 items-center text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="w-5 h-5" />
            Back to Routes
          </button>
          <h2 className="ml-4 text-lg font-semibold">
            {route.name}
            {route.flowData?.nodes[0]?.data && (
              <span>
                {" "}
                ({route.flowData.nodes[0].data.method}{" "}
                {route.flowData.nodes[0].data.path})
              </span>
            )}
          </h2>
        </div>
        <button
          onClick={handleSave}
          className="flex gap-2 items-center px-4 py-2 text-white bg-blue-500 rounded hover:bg-blue-600"
        >
          <Save className="w-4 h-4" />
          Save Changes
        </button>
      </div>
      <div className="flex overflow-hidden flex-1">
        <ComponentsPanel />
        <div className="flex-1 h-full" ref={reactFlowWrapper}>
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            onDrop={onDrop}
            onDragOver={onDragOver}
            onNodeClick={onNodeClick}
            onPaneClick={onPaneClick}
            nodeTypes={nodeTypes}
            deleteKeyCode="Delete"
            multiSelectionKeyCode="Control"
            selectionKeyCode="Shift"
            snapToGrid={true}
            snapGrid={[15, 15]}
            defaultViewport={{ x: 0, y: 0, zoom: 1 }}
            fitView
          >
            <Background />
            <Controls />
          </ReactFlow>
        </div>
        {selectedNode && (
          <ConfigPanel
            node={selectedNode}
            onClose={() => setSelectedNode(null)}
            onUpdateNode={updateNode}
            onOpenObjectModal={handleOpenObjectModal}
            onOpenArrayModal={handleOpenArrayModal}
          />
        )}
      </div>
      <ObjectFieldModal
        isOpen={isObjectModalOpen}
        onClose={() => {
          setIsObjectModalOpen(false);
          setEditingFieldIndex(null);
          setEditingNodeId(null);
          setEditingArrayName(null);
        }}
        onSave={handleObjectFieldSave}
        initialFields={
          editingNodeId && editingFieldIndex !== null && editingArrayName
            ? nodes.find(n => n.id === editingNodeId)?.data?.[editingArrayName]?.[editingFieldIndex]?.value || []
            : []
        }
      />
      <ArrayFieldModal
        isOpen={isArrayModalOpen}
        onClose={() => {
          setIsArrayModalOpen(false);
          setEditingFieldIndex(null);
          setEditingNodeId(null);
          setEditingArrayName(null);
        }}
        onSave={handleArrayFieldSave}
        initialType={
          editingNodeId && editingFieldIndex !== null && editingArrayName
            ? nodes.find(n => n.id === editingNodeId)?.data?.[editingArrayName]?.[editingFieldIndex]?.value || 'string'
            : 'string'
        }
        initialObjectFields={
          editingNodeId && editingFieldIndex !== null && editingArrayName
            ? nodes.find(n => n.id === editingNodeId)?.data?.[editingArrayName]?.[editingFieldIndex]?.value?.fields || []
            : []
        }
      />
    </div>
  );
}

export default function RouteFlowEditor({ route, onClose }: FlowEditorContentProps) {
  return (
    // <ReactFlowProvider>
    <FlowEditorContent route={route} onClose={onClose} />
    // </ReactFlowProvider>
  );
}
