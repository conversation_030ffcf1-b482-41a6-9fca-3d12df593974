import React, { memo } from "react";
import { <PERSON>le, Position, NodeProps } from "reactflow";
import {
  Lock,
  Globe,
  ArrowUpDown,
  Code,
  Database,
  Variable,
} from "lucide-react";

export const nodeTypes = {
  auth: {
    label: "Auth",
    color: "bg-blue-500",
    icon: <Lock className="w-5 h-5" />,
  },
  url: {
    label: "URL",
    color: "bg-green-500",
    icon: <Globe className="w-5 h-5" />,
  },
  outputs: {
    label: "Outputs",
    color: "bg-yellow-500",
    icon: <ArrowUpDown className="w-5 h-5" />,
  },
  logic: {
    label: "Logic",
    color: "bg-red-500",
    icon: <Code className="w-5 h-5" />,
  },
  variable: {
    label: "Variable",
    color: "bg-purple-500",
    icon: <Variable className="w-5 h-5" />,
  },
  "db-find": {
    label: "DB Find",
    color: "bg-teal-500",
    icon: <Database className="w-5 h-5" />,
  },
  "db-insert": {
    label: "DB Insert",
    color: "bg-lime-500",
    icon: <Database className="w-5 h-5" />,
  },
  "db-update": {
    label: "DB Update",
    color: "bg-indigo-500",
    icon: <Database className="w-5 h-5" />,
  },
  "db-delete": {
    label: "DB Delete",
    color: "bg-pink-500",
    icon: <Database className="w-5 h-5" />,
  },
  "db-query": {
    label: "DB Query",
    color: "bg-cyan-500",
    icon: <Database className="w-5 h-5" />,
  },
  "mock-api": {
    label: "Mock API",
    color: "bg-purple-500",
    icon: <Globe className="w-5 h-5" />,
  },
};

const CustomNode = ({ data, type }: NodeProps) => {
  const getIcon = () => {
    switch (type) {
      case "auth":
        return <Lock className="w-5 h-5" />;
      case "url":
        return <Globe className="w-5 h-5" />;
      case "outputs":
        return <ArrowUpDown className="w-5 h-5" />;
      case "logic":
        return <Code className="w-5 h-5" />;
      case "variable":
        return <Variable className="w-5 h-5" />;
      case "db-find":
      case "db-insert":
      case "db-update":
      case "db-delete":
      case "db-query":
        return <Database className="w-5 h-5" />;
      case "mock-api":
        return <Globe className="w-5 h-5" />;
      default:
        return null;
    }
  };

  return (
    <div className="relative px-4 py-2 shadow-md rounded-md bg-white border-2 border-gray-200 min-w-[180px] cursor-grab active:cursor-grabbing">
      <Handle
        type="target"
        position={Position.Left}
        className="!w-3 !h-3 !bg-blue-500 !border-2 !border-white"
      />
      <div className="flex items-center">
        <div className="rounded-full w-8 h-8 flex items-center justify-center bg-gray-50 mr-2">
          {getIcon()}
        </div>
        <div>
          <div className="text-sm font-bold">{data.label}</div>
          <div className="text-xs text-gray-500">
            {type
              .split("-")
              .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
              .join(" ")}
          </div>
        </div>
      </div>
      <Handle
        type="source"
        position={Position.Right}
        className="!w-3 !h-3 !bg-blue-500 !border-2 !border-white"
      />
    </div>
  );
};

export default memo(CustomNode);
