/* eslint-disable no-unsafe-optional-chaining */
import React, { useState, useEffect } from "react";
import { X, Plus, Trash, Trash2 } from "lucide-react";
import { Node } from "reactflow";
import { useFlowContext } from "@/store/FlowContext";

interface ConfigPanelProps {
  node: Node | null;
  onClose: () => void;
  onUpdateNode: (id: string, data: any) => void;
  onOpenObjectModal: (
    nodeId: string,
    index: number,
    arrayName: string,
    fieldName: string
  ) => void;
  onOpenArrayModal: (
    nodeId: string,
    index: number,
    arrayName: string,
    fieldName: string
  ) => void;
}

interface Field {
  name: string;
  type: string;
  validation?: string;
}

const getDefaultDataForType = (type: string) => {
  const baseData = {
    label: type.charAt(0).toUpperCase() + type.slice(1).replace("-", " "),
  };

  switch (type) {
    case "variable":
      return {
        ...baseData,
        name: "",
        type: "string",
        defaultValue: "null",
      };

    case "url":
      return {
        method: "GET",
        path: "",
        fields: [],
        queryFields: [],
        apiname: "New API",
        ...baseData,
      };
    case "mock-api":
      return {
        method: "GET",
        path: "",
        description: "",
        authType: "none",
        fields: [],
        queryFields: [],
        responseFields: [],
        outputType: "json",
        statusCode: 200,
        apiname: "Mock API",
        ...baseData,
      };

    case "auth":
      return {
        ...baseData,
        authType: "bearer",
      };

    case "outputs":
      return {
        ...baseData,
        outputType: "json",
        resultVar: "",
        statusCode: 200,
        fields: [],
        responseRaw: "",
      };

    case "logic":
      return {
        ...baseData,
        code: "",
      };

    case "db-find":
      return {
        ...baseData,
        model: "",
        operation: "findMany",
        query: "SELECT * FROM table",
        resultVar: "result",
        label: "Database Find",
      };
    case "db-query":
      return {
        ...baseData,
        model: "",
        operation: "findOne",
        query: "SELECT * FROM table",
        resultVar: "result",
        label: "Database Query",
      };

    case "db-insert":
      return {
        ...baseData,
        model: "",
        operation: "create",
        query: "",
        variables: "",
        resultVar: "result",
        label: "Database Insert",
      };

    case "db-update":
      return {
        ...baseData,
        model: "",
        operation: "update",
        idField: "id",
        query: "",
        variables: "",
        resultVar: "result",
        label: "Database Update",
      };
    case "db-delete":
      return {
        ...baseData,
        model: "",
        operation: "delete",
        idField: "id",
        query: "",
        variables: "",
        resultVar: "result",
        label: "Database Delete",
      };

    default:
      return baseData;
  }
};

const validateVariableInput = (input: string) => {
  // Regular expression to allow only alphanumeric characters, underscores, and hyphens
  const regex = /^[a-zA-Z0-9_-]*$/;
  return regex.test(input);
};

const getExampleValue = (type: string, fields?: any): any => {
  switch (type) {
    case "string":
      return ["example", "sample", "test", "demo", "value"][
        Math.floor(Math.random() * 5)
      ];
    case "number":
      return parseFloat((Math.random() * 100).toFixed(2));
    case "integer":
      return Math.floor(Math.random() * 100);
    case "boolean":
      return Math.random() > 0.5;
    case "date":
      return "2024-03-15";
    case "datetime":
      return "2024-03-15T14:30:00";
    case "file":
      return "file.pdf";
    case "array":
      const numItems = 2 + Math.floor(Math.random() * 2); // 2-3 items
      if (typeof fields === "object" && fields.type === "object") {
        // Handle array of objects
        return Array(numItems)
          .fill(null)
          .map(() => getExampleValue("object", fields.fields));
      }
      // Handle array of primitive types
      return Array(numItems)
        .fill(null)
        .map(() => getExampleValue(fields));
    case "object":
      if (!fields) return {};
      // Handle both array of fields and direct field objects
      const fieldArray = Array.isArray(fields) ? fields : fields.fields || [];
      return fieldArray.reduce((acc: any, field: any) => {
        acc[field.name] = getExampleValue(
          field.type,
          field.value || field.fields
        );
        return acc;
      }, {});
    default:
      return "example";
  }
};

const generateExampleJson = (fields: any[]) => {
  return fields.reduce((acc, field) => {
    // For array types, pass the value field which contains the array item type
    acc[field.name] = getExampleValue(
      field.type,
      field.type === "array" ? field.value : field.value
    );
    return acc;
  }, {});
};

const getDefaultValueForType = (type: string) => {
  switch (type) {
    case "number":
    case "integer":
      return "0";
    case "boolean":
      return "true";
    case "array":
      return "[]";
    case "object":
      return "{}";
    default:
      return '""';
  }
};

export default function ConfigPanel({
  node,
  onClose,
  onUpdateNode,
  onOpenObjectModal,
  onOpenArrayModal,
}: ConfigPanelProps) {
  const {
    updateNode,
    dispatch,
    state: { nodes, models },
  } = useFlowContext();

  // Reset field states when node changes
  useEffect(() => {
    setNewField({
      name: "",
      type: "string",
      validation: "",
    });
    setNewQueryField({
      name: "",
      type: "string",
      validation: "",
    });
    setNewResponseField({
      name: "",
      type: "string",
      validation: "",
    });
  }, [node?.id]); // Add node.id as dependency

  const [renderPreview, setRenderPreview] = useState(false);
  const [newField, setNewField] = useState<Field>({
    name: "",
    type: "string",
    validation: "required",
  });
  const [newQueryField, setNewQueryField] = useState<Field>({
    name: "",
    type: "string",
    validation: "required",
  });
  const [newResponseField, setNewResponseField] = useState<Field>({
    name: "",
    type: "string",
    validation: "",
  });

  console.log(
    "nodes >>",
    nodes,
    "node >>",
    nodes.find((n) => n.id === node?.id)
  );

  useEffect(() => {
    // Initialize node data with defaults if not already set
    if (node) {
      const defaultData = getDefaultDataForType(node.type as string);
      const newData = {
        ...defaultData,
        ...node.data, // This will override defaults with any existing data
      };

      // Only update if the data is different
      if (JSON.stringify(newData) !== JSON.stringify(node.data)) {
        // onUpdateNode(node.id, newData);
        updateNode(node.id, newData);
      }
    }
    // node?.id, node?.type
  }, []);

  useEffect(() => {
    console.log("ConfigPanel re-rendered with node:", node);
  }, [node]);

  if (!node) return null;

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    if (!node) return;

    console.log(
      "Handling change for:",
      e.target.name,
      "with value:",
      e.target.value
    );

    const newData = {
      ...node.data,
      [e.target.name]: e.target.value,
    };

    console.log("New data to update:", newData);

    // onUpdateNode(node.id, newData);
    dispatch({ type: "UPDATE_NODE", payload: { id: node.id, data: newData } });
    // updateNode(node.id, newData);
  };

  const handleArrayChange = (
    index: number,
    field: string,
    value: string,
    arrayName: string
  ) => {
    const array = [...node.data[arrayName]];

    if (value === "editobject") {
      onOpenObjectModal(node.id, index, arrayName, array[index].name);
      return;
    }

    if (value === "editarray") {
      onOpenArrayModal(node.id, index, arrayName, array[index].name);
      return;
    }

    array[index] = { ...array[index], [field]: value };

    if (field === "type") {
      if (value === "object") {
        onOpenObjectModal(node.id, index, arrayName, array[index].name);
        return;
      }
      if (value === "array") {
        onOpenArrayModal(node.id, index, arrayName, array[index].name);
        return;
      }
    }

    const newData = {
      ...node.data,
      [arrayName]: array,
    };

    updateNode(node.id, newData);
  };

  const addField = (arrayName: string) => {
    // Choose which field state to use based on arrayName
    const fieldToAdd =
      arrayName === "queryFields"
        ? newQueryField
        : arrayName === "responseFields"
        ? newResponseField
        : newField;

    if (!fieldToAdd.name.trim()) return;

    if (fieldToAdd.type === "object") {
      onOpenObjectModal(
        node.id,
        node?.data?.[arrayName]?.length || 0,
        arrayName,
        fieldToAdd.name
      );
      return;
    }

    if (fieldToAdd.type === "array") {
      onOpenArrayModal(
        node.id,
        node?.data?.[arrayName]?.length || 0,
        arrayName,
        fieldToAdd.name
      );
      return;
    }

    const array = [
      ...(node?.data?.[arrayName] ? node?.data?.[arrayName] : []),
      { ...fieldToAdd },
    ];
    const newData = {
      ...node.data,
      [arrayName]: array,
    };

    updateNode(node.id, newData);

    // Reset the appropriate field state
    if (arrayName === "queryFields") {
      setNewQueryField({ name: "", type: "string", validation: "required" });
    } else if (arrayName === "responseFields") {
      setNewResponseField({ name: "", type: "string", validation: "" });
    } else {
      setNewField({ name: "", type: "string", validation: "" });
    }

    setRenderPreview(true);
  };

  const removeField = (index: number, arrayName: string) => {
    const array = [...node.data[arrayName]];
    array.splice(index, 1);
    const newData = {
      ...node.data,
      [arrayName]: array,
    };

    // onUpdateNode(node.id, newData);
    updateNode(node.id, newData);

    setRenderPreview(true);
  };

  const copyQueryFields = () => {
    const currentFields = node.data.fields || [];
    navigator.clipboard.writeText(JSON.stringify(currentFields, null, 2));
  };

  const extractQueryParams = (path: string) => {
    const params = path.match(/:[a-zA-Z]+/g) || [];
    return params.map((param) => ({
      name: param.substring(1),
      type: "string",
      validation: "",
    }));
  };

  const renderDatabaseFields = () => (
    <>
      <div className="mb-4">
        <label className="block mb-1 text-sm font-medium">Model</label>
        <select
          name="model"
          value={node.data.model}
          onChange={handleChange}
          className="p-2 w-full rounded border"
        >
          <option value="">Select Model</option>
          {models.map((model) => (
            <option key={model.id} value={model.name}>
              {model.name}
            </option>
          ))}
        </select>
      </div>
      <div className="mb-4">
        <label className="block mb-1 text-sm font-medium">Operation</label>
        <select
          name="operation"
          value={node.data.operation}
          onChange={handleChange}
          className="p-2 w-full rounded border"
        >
          <option value="findMany">Find Many</option>
          <option value="findOne">Find One</option>
          <option value="create">Create</option>
          <option value="update">Update</option>
          <option value="delete">Delete</option>
        </select>
      </div>
      <div className="mb-4">
        <label className="block mb-1 text-sm font-medium">SQL Query</label>
        <textarea
          name="query"
          value={node.data.query}
          onChange={handleChange}
          className="p-2 w-full h-32 font-mono text-sm rounded border"
          placeholder="SELECT * FROM table WHERE id = :id"
        />
      </div>
      <div className="mb-4">
        <label className="block mb-1 text-sm font-medium">Save Result In</label>
        <input
          type="text"
          name="resultVar"
          value={node.data.resultVar}
          onChange={handleChange}
          className="p-2 w-full rounded border"
          placeholder="result"
        />
      </div>
    </>
  );

  const renderFields = () => {
    switch (node.type) {
      case "auth":
        return (
          <>
            <div className="mb-4">
              <label className="block mb-1 text-sm font-medium">
                Auth Type
              </label>
              <select
                name="authType"
                value={node.data.authType}
                onChange={handleChange}
                className="p-2 w-full rounded border"
              >
                <option value="none">None</option>
                <option value="basic">Basic</option>
                <option value="bearer">Bearer JWT Token</option>
                <option value="apiKey">API Key</option>
                <option value="oauth2">OAuth 2.0</option>
                <option value="digest">Digest</option>
                <option value="hmac">HMAC</option>
                <option value="session">Session-based</option>
                <option value="ldap">LDAP</option>
                <option value="saml">SAML</option>
              </select>
            </div>
          </>
        );

      case "url":
        return (
          <>
            <div className="mb-4">
              <label className="block mb-1 text-sm font-medium">
                Route Path
              </label>
              <input
                type="text"
                name="apiname"
                value={node.data.apiname}
                onChange={handleChange}
                className="p-2 w-full rounded border"
                placeholder="New API"
              />
            </div>
            <div className="mb-4">
              <label className="block mb-1 text-sm font-medium">Method</label>
              <select
                name="method"
                value={node.data.method}
                onChange={handleChange}
                className="p-2 w-full rounded border"
              >
                {["GET", "POST", "PUT", "DELETE", "PATCH"].map((method) => (
                  <option key={method} value={method}>
                    {method}
                  </option>
                ))}
              </select>
            </div>
            <div className="mb-4">
              <label className="block mb-1 text-sm font-medium">
                Route Path
              </label>
              <input
                type="text"
                name="path"
                value={node.data.path}
                onChange={handleChange}
                className="p-2 w-full rounded border"
                placeholder="/api/users/:id"
              />
            </div>
            <div className="mb-4">
              <label className="block mb-1 text-sm font-medium">
                Body Fields
              </label>
              {node.data.fields?.length > 0 && renderPreview && (
                <div className="p-2 mb-2 font-mono text-sm bg-gray-50 rounded">
                  <textarea
                    rows={12}
                    value={JSON.stringify(
                      generateExampleJson(node.data.fields),
                      null,
                      2
                    )}
                    onChange={(e) => {
                      try {
                        const parsedJson = JSON.parse(e.target.value);
                        const newFields = Object.entries(parsedJson).map(
                          ([name, value]) => ({
                            name,
                            type: Array.isArray(value)
                              ? "array"
                              : typeof value === "object" && value !== null
                              ? "object"
                              : (typeof value as string),
                          })
                        );

                        const newData = {
                          ...node.data,
                          fields: newFields,
                        };

                        updateNode(node.id, newData);
                      } catch (error) {
                        console.error("Invalid JSON", error);
                      }
                    }}
                    className="w-full whitespace-pre-wrap break-words"
                  />
                </div>
              )}
              {(node.data.fields || []).map((field: Field, index: number) => (
                <div key={index} className="flex mb-2">
                  <input
                    type="text"
                    value={field.name}
                    onChange={(e) =>
                      handleArrayChange(index, "name", e.target.value, "fields")
                    }
                    className="flex-1 p-2 text-sm rounded border"
                    placeholder="Field name"
                  />
                  <select
                    value={field.type}
                    onChange={(e) =>
                      handleArrayChange(index, "type", e.target.value, "fields")
                    }
                    className="p-2 w-24 text-sm rounded border"
                  >
                    <option value="string">String</option>
                    <option value="number">Number</option>
                    <option value="integer">Integer</option>
                    <option value="boolean">Bool</option>
                    <option value="date">Date</option>
                    <option value="datetime">Date Time</option>
                    <option value="file">File</option>
                    <option value="array">Array</option>
                    <option value="object">Object</option>
                    {field.type === "object" && (
                      <option value="editobject">Edit Object</option>
                    )}
                    {field.type === "object" && (
                      <option value="editarray">Edit Array</option>
                    )}
                    {field.type === "array" && (
                      <option value="editarray">Edit Array</option>
                    )}
                  </select>
                  <button
                    onClick={() => removeField(index, "fields")}
                    className="p-2 text-red-500 rounded hover:bg-red-50"
                  >
                    <Trash className="w-4 h-4" />
                  </button>
                </div>
              ))}
              <div className="flex mt-2">
                <input
                  type="text"
                  value={newField.name}
                  onChange={(e) =>
                    setNewField({ ...newField, name: e.target.value })
                  }
                  className="flex-1 p-2 text-sm rounded border"
                  placeholder="New field name"
                />
                <select
                  value={newField.type}
                  onChange={(e) =>
                    setNewField({ ...newField, type: e.target.value })
                  }
                  className="p-2 w-24 text-sm rounded border"
                >
                  <option value="string">String</option>
                  <option value="number">Number</option>
                  <option value="integer">Integer</option>
                  <option value="boolean">Bool</option>
                  <option value="date">Date</option>
                  <option value="datetime">Date Time</option>
                  <option value="file">File</option>
                  <option value="array">Array</option>
                  <option value="object">Object</option>
                </select>
                <button
                  onClick={() => {
                    if (newField.name.trim()) {
                      addField("fields");
                    }
                  }}
                  className="p-2 text-white bg-blue-500 rounded hover:bg-blue-600"
                >
                  <Plus className="w-4 h-4" />
                </button>
              </div>
            </div>
            <div className="mb-4">
              <label className="block mb-1 text-sm font-medium">
                Query Parameters
              </label>
              {node.data.queryFields?.length > 0 && (
                <div className="p-2 mb-2 text-sm bg-gray-50 rounded">
                  {node.data.queryFields.map((field: Field) => (
                    <div key={field.name} className="text-gray-600">
                      {field.name}: {field.type}
                      {field.validation ? ` (${field.validation})` : ""}
                    </div>
                  ))}
                </div>
              )}
              {(node.data.queryFields || []).map(
                (field: Field, index: number) => (
                  <div key={index} className="flex mb-2">
                    <input
                      type="text"
                      value={field.name}
                      onChange={(e) =>
                        handleArrayChange(
                          index,
                          "name",
                          e.target.value,
                          "queryFields"
                        )
                      }
                      className="flex-1 p-2 text-sm rounded border"
                      placeholder="Field name"
                    />
                    <select
                      value={field.type}
                      onChange={(e) =>
                        handleArrayChange(
                          index,
                          "type",
                          e.target.value,
                          "queryFields"
                        )
                      }
                      className="p-2 w-20 text-sm rounded border"
                    >
                      <option value="string">String</option>
                      <option value="number">Number</option>
                      <option value="integer">Integer</option>
                    </select>
                    <button
                      onClick={() => removeField(index, "queryFields")}
                      className="p-2 text-red-500 rounded hover:bg-red-50"
                    >
                      <Trash className="w-4 h-4" />
                    </button>
                  </div>
                )
              )}
              <div className="flex gap-1 mt-2">
                <input
                  type="text"
                  value={newQueryField.name}
                  onChange={(e) =>
                    setNewQueryField({ ...newQueryField, name: e.target.value })
                  }
                  className="flex-1 p-2 text-sm rounded border"
                  placeholder="New query param"
                />
                <select
                  value={newQueryField.type}
                  onChange={(e) =>
                    setNewQueryField({ ...newQueryField, type: e.target.value })
                  }
                  className="p-2 w-20 text-sm rounded border"
                >
                  <option value="string">String</option>
                  <option value="number">Number</option>
                  <option value="integer">Integer</option>
                </select>
                <button
                  onClick={() => {
                    if (newQueryField.name.trim()) {
                      addField("queryFields");
                    }
                  }}
                  className="p-2 text-white bg-blue-500 rounded hover:bg-blue-600"
                >
                  <Plus className="w-4 h-4" />
                </button>
              </div>
            </div>
          </>
        );

      case "outputs":
        return (
          <>
            <div className="mb-4">
              <label className="block mb-1 text-sm font-medium">
                Output Type
              </label>
              <select
                name="outputType"
                value={node.data.outputType}
                onChange={handleChange}
                className="p-2 w-full rounded border"
              >
                <option value="xml">XML</option>
                <option value="json">JSON</option>
                <option value="csv">CSV</option>
                <option value="plain">Plain Text</option>
                <option value="html">HTML</option>
              </select>
            </div>

            {node.data.outputType === "mockup" ? (
              <div className="mb-4">
                <label className="block mb-1 text-sm font-medium">
                  Response Raw
                </label>
                <textarea
                  name="responseRaw"
                  value={node.data.responseRaw}
                  onChange={handleChange}
                  className="p-2 w-full h-32 rounded border"
                  placeholder="Enter raw response here..."
                />
              </div>
            ) : (
              <div className="mb-4">
                <label className="block mb-1 text-sm font-medium">Fields</label>
                {node.data.fields?.length > 0 && (
                  <div className="p-2 mb-2 font-mono text-sm bg-gray-50 rounded">
                    <textarea
                      rows={12}
                      className="w-full whitespace-pre-wrap break-words"
                      value={JSON.stringify(
                        generateExampleJson(node.data.fields),
                        null,
                        2
                      )}
                      onChange={(e) => {
                        try {
                          const parsedJson = JSON.parse(e.target.value);
                          const newFields = Object.entries(parsedJson).map(
                            ([name, value]) => ({
                              name,
                              type: Array.isArray(value)
                                ? "array"
                                : typeof value === "object" && value !== null
                                ? "object"
                                : (typeof value as string),
                            })
                          );

                          const newData = {
                            ...node.data,
                            fields: newFields,
                          };

                          updateNode(node.id, newData);
                        } catch (error) {
                          console.error("Invalid JSON", error);
                        }
                      }}
                    />
                  </div>
                )}
                {(node.data.fields || []).map((field: Field, index: number) => (
                  <div key={index} className="flex mb-2">
                    <input
                      type="text"
                      value={field.name}
                      onChange={(e) =>
                        handleArrayChange(
                          index,
                          "name",
                          e.target.value,
                          "fields"
                        )
                      }
                      className="flex-1 p-2 text-sm rounded border"
                      placeholder="Field name"
                    />
                    <select
                      value={field.type}
                      onChange={(e) =>
                        handleArrayChange(
                          index,
                          "type",
                          e.target.value,
                          "fields"
                        )
                      }
                      className="p-2 w-24 text-sm rounded border"
                    >
                      <option value="string">String</option>
                      <option value="number">Number</option>
                      <option value="boolean">Boolean</option>
                      <option value="date">Date</option>
                      <option value="object">Object</option>
                      <option value="array">Array</option>
                    </select>
                    <button
                      onClick={() => removeField(index, "fields")}
                      className="p-2 text-red-500 rounded hover:bg-red-50"
                    >
                      <Trash className="w-4 h-4" />
                    </button>
                  </div>
                ))}
                <div className="flex gap-1 mt-2">
                  <input
                    type="text"
                    value={newField.name}
                    onChange={(e) =>
                      setNewField({ ...newField, name: e.target.value })
                    }
                    className="flex-1 p-2 text-sm rounded border"
                    placeholder="New field name"
                  />
                  <select
                    value={newField.type}
                    onChange={(e) =>
                      setNewField({ ...newField, type: e.target.value })
                    }
                    className="p-2 w-24 text-sm rounded border"
                  >
                    <option value="string">String</option>
                    <option value="number">Number</option>
                    <option value="boolean">Boolean</option>
                    <option value="date">Date</option>
                    <option value="object">Object</option>
                    <option value="array">Array</option>
                  </select>
                  <button
                    onClick={() => {
                      if (newField.name.trim()) {
                        addField("fields");
                      }
                    }}
                    className="p-2 text-white bg-blue-500 rounded hover:bg-blue-600"
                  >
                    <Plus className="w-4 h-4" />
                  </button>
                </div>
              </div>
            )}

            <div className="mb-4">
              <label className="block mb-1 text-sm font-medium">
                Result Variable (optional)
              </label>
              <input
                type="text"
                name="resultVar"
                value={node.data.resultVar}
                onChange={handleChange}
                className="p-2 w-full rounded border"
                placeholder=""
              />
            </div>
            <div className="mb-4">
              <label className="block mb-1 text-sm font-medium">
                Status Code
              </label>
              <input
                type="number"
                name="statusCode"
                value={node.data.statusCode}
                onChange={handleChange}
                className="p-2 w-full rounded border"
                placeholder="200"
              />
            </div>
          </>
        );

      case "variable":
        return (
          <>
            <div className="mb-4">
              <label className="block mb-1 text-sm font-medium">
                Variable Name
              </label>
              <input
                type="text"
                name="name"
                value={node.data.name}
                onChange={(e) => {
                  if (validateVariableInput(e.target.value)) {
                    handleChange(e);
                  }
                }}
                className="p-2 w-full rounded border"
                placeholder="variable name"
              />
            </div>
            <div className="mb-4">
              <label className="block mb-1 text-sm font-medium">Type</label>
              <select
                name="type"
                value={node.data.type}
                onChange={(e) => {
                  const newType = e.target.value;
                  const newDefaultValue = getDefaultValueForType(newType);
                  const newData = {
                    ...node.data,
                    type: newType,
                    defaultValue: newDefaultValue,
                  };
                  dispatch({
                    type: "UPDATE_NODE",
                    payload: { id: node.id, data: newData },
                  });
                }}
                className="p-2 w-full rounded border"
              >
                <option value="string">String</option>
                <option value="number">Number</option>
                <option value="boolean">Boolean</option>
                <option value="object">Object</option>
                <option value="array">Array</option>
              </select>
            </div>
            <div className="mb-4">
              <label className="block mb-1 text-sm font-medium">
                Default Value
              </label>
              {node.data.type === "boolean" ? (
                <select
                  name="defaultValue"
                  value={node.data.defaultValue}
                  onChange={handleChange}
                  className="p-2 w-full rounded border"
                >
                  <option value="">Select</option>
                  <option value="true">True</option>
                  <option value="false">False</option>
                </select>
              ) : (
                <input
                  type={node.data.type === "number" ? "text" : "text"}
                  name="defaultValue"
                  value={node.data.defaultValue}
                  onChange={(e) => {
                    if (node.data.type === "number") {
                      const regex = /^-?\d*\.?\d*$/; // Only digits, . and -
                      if (regex.test(e.target.value) || e.target.value === "") {
                        handleChange(e);
                      }
                    } else {
                      handleChange(e);
                    }
                  }}
                  className="p-2 w-full rounded border"
                  placeholder="Default value"
                />
              )}
            </div>
          </>
        );

      case "logic":
        return (
          <div className="mb-4">
            <label className="block mb-1 text-sm font-medium">
              Code / Comments
            </label>
            <textarea
              name="code"
              value={node.data.code}
              onChange={handleChange}
              className="p-2 w-full h-40 font-mono text-sm rounded border"
              placeholder="Write your code // comments here"
            />
          </div>
        );

      case "db-find":
        return renderDatabaseFields();

      case "db-query":
        return <>{renderDatabaseFields()}</>;

      case "db-insert":
        return (
          <>
            {renderDatabaseFields()}
            <div className="mb-4">
              <label className="block mb-1 text-sm font-medium">
                Variables
              </label>
              <textarea
                name="variables"
                value={node.data.variables}
                onChange={handleChange}
                className="p-2 w-full h-20 font-mono text-sm rounded border"
                placeholder="name: string&#10;age: number"
              />
            </div>
          </>
        );

      case "db-update":
      case "db-delete":
        return (
          <>
            {renderDatabaseFields()}
            <div className="mb-4">
              <label className="block mb-1 text-sm font-medium">ID Field</label>
              <input
                type="text"
                name="idField"
                value={node.data.idField}
                onChange={handleChange}
                className="p-2 w-full rounded border"
                placeholder="id"
              />
            </div>
            {node.type === "db-update" && (
              <div className="mb-4">
                <label className="block mb-1 text-sm font-medium">
                  Variables
                </label>
                <textarea
                  name="variables"
                  value={node.data.variables}
                  onChange={handleChange}
                  className="p-2 w-full h-20 font-mono text-sm rounded border"
                  placeholder="name: string&#10;age: number"
                />
              </div>
            )}
          </>
        );

      case "mock-api":
        return (
          <>
            <div className="mb-4">
              <label className="block mb-1 text-sm font-medium">API Name</label>
              <input
                type="text"
                name="apiname"
                value={node.data.apiname}
                onChange={handleChange}
                className="p-2 w-full rounded border"
                placeholder="New API"
              />
            </div>
            <div className="mb-4">
              <label className="block mb-1 text-sm font-medium">
                Route Path
              </label>
              <input
                type="text"
                name="path"
                value={node.data.path}
                onChange={handleChange}
                className="p-2 w-full rounded border"
                placeholder="New API"
              />
            </div>
            <div className="mb-4">
              <label className="block mb-1 text-sm font-medium">
                Description
              </label>
              <textarea
                name="description"
                value={node.data.description}
                onChange={handleChange}
                className="p-2 w-full rounded border"
                placeholder="Describe what this mock API does..."
                rows={8}
              />
            </div>

            <div className="mb-4">
              <label className="block mb-1 text-sm font-medium">Method</label>
              <select
                name="method"
                value={node.data.method}
                onChange={handleChange}
                className="p-2 w-full rounded border"
              >
                <option value="GET">GET</option>
                <option value="POST">POST</option>
                <option value="PUT">PUT</option>
                <option value="DELETE">DELETE</option>
                <option value="PATCH">PATCH</option>
              </select>
            </div>
            <div className="mb-4">
              <label className="block mb-1 text-sm font-medium">
                Auth Type
              </label>
              <select
                name="authType"
                value={node.data.authType}
                onChange={handleChange}
                className="p-2 w-full rounded border"
              >
                <option value="none">None</option>
                <option value="basic">Basic</option>
                <option value="bearer">Bearer JWT Token</option>
                <option value="apiKey">API Key</option>
                <option value="oauth2">OAuth 2.0</option>
                <option value="digest">Digest</option>
                <option value="hmac">HMAC</option>
                <option value="session">Session-based</option>
                <option value="ldap">LDAP</option>
                <option value="saml">SAML</option>
              </select>
            </div>
            <div className="mb-4">
              <label className="block mb-1 text-sm font-medium">
                Body Fields
              </label>
              {node.data.fields?.length > 0 && renderPreview && (
                <div className="p-2 mb-2 font-mono text-sm bg-gray-50 rounded">
                  <textarea
                    rows={12}
                    className="w-full whitespace-pre-wrap break-words"
                    value={JSON.stringify(
                      generateExampleJson(node.data.fields),
                      null,
                      2
                    )}
                    onChange={(e) => {
                      try {
                        const parsedJson = JSON.parse(e.target.value);
                        const newFields = Object.entries(parsedJson).map(
                          ([name, value]) => ({
                            name,
                            type: Array.isArray(value)
                              ? "array"
                              : typeof value === "object" && value !== null
                              ? "object"
                              : (typeof value as string),
                          })
                        );

                        const newData = {
                          ...node.data,
                          fields: newFields,
                        };

                        updateNode(node.id, newData);
                      } catch (error) {
                        console.error("Invalid JSON", error);
                      }
                    }}
                  />
                </div>
              )}
              {(node.data.fields || []).map((field: Field, index: number) => (
                <div key={index} className="flex mb-2">
                  <input
                    type="text"
                    value={field.name}
                    onChange={(e) =>
                      handleArrayChange(index, "name", e.target.value, "fields")
                    }
                    className="flex-1 p-2 mr-1 text-sm rounded border"
                    placeholder="Field name"
                  />
                  <select
                    value={field.type}
                    onChange={(e) =>
                      handleArrayChange(index, "type", e.target.value, "fields")
                    }
                    className="p-2 w-20 text-sm rounded border"
                  >
                    <option value="string">String</option>
                    <option value="number">Number</option>
                    <option value="integer">Integer</option>
                    <option value="boolean">Bool</option>
                    <option value="date">Date</option>
                    <option value="datetime">Date Time</option>
                    <option value="file">File</option>
                    <option value="array">Array</option>
                    <option value="object">Object</option>
                    <option value="editobject">Edit Object</option>
                    <option value="editarray">Edit Array</option>
                  </select>
                  <button
                    onClick={() => removeField(index, "fields")}
                    className="p-2 text-red-500 rounded hover:bg-red-50"
                  >
                    <Trash className="w-4 h-4" />
                  </button>
                </div>
              ))}
              <div className="flex gap-1 mt-2">
                <input
                  type="text"
                  value={newField.name}
                  onChange={(e) =>
                    setNewField({ ...newField, name: e.target.value })
                  }
                  className="flex-1 p-2 text-sm rounded border"
                  placeholder="New field name"
                />
                <select
                  value={newField.type}
                  onChange={(e) =>
                    setNewField({ ...newField, type: e.target.value })
                  }
                  className="p-2 w-20 text-sm rounded border"
                >
                  <option value="string">String</option>
                  <option value="number">Number</option>
                  <option value="integer">Integer</option>
                  <option value="boolean">Bool</option>
                  <option value="date">Date</option>
                  <option value="datetime">Date Time</option>
                  <option value="file">File</option>
                  <option value="array">Array</option>
                  <option value="object">Object</option>
                </select>
                <button
                  onClick={() => {
                    if (newField.name.trim()) {
                      addField("fields");
                    }
                  }}
                  className="p-2 text-white bg-blue-500 rounded hover:bg-blue-600"
                >
                  <Plus className="w-4 h-4" />
                </button>
              </div>
            </div>

            <div className="mb-4">
              <label className="block mb-1 text-sm font-medium">
                Query Parameters
              </label>
              {node.data.queryFields?.length > 0 && (
                <div className="p-2 mb-2 text-sm bg-gray-50 rounded">
                  {node.data.queryFields.map((field: Field) => (
                    <div key={field.name} className="text-gray-600">
                      {field.name}: {field.type}
                      {field.validation ? ` (${field.validation})` : ""}
                    </div>
                  ))}
                </div>
              )}
              {(node.data.queryFields || []).map(
                (field: Field, index: number) => (
                  <div key={index} className="flex mb-2">
                    <input
                      type="text"
                      value={field.name}
                      onChange={(e) =>
                        handleArrayChange(
                          index,
                          "name",
                          e.target.value,
                          "queryFields"
                        )
                      }
                      className="flex-1 p-2 text-sm rounded border"
                      placeholder="Field name"
                    />
                    <select
                      value={field.type}
                      onChange={(e) =>
                        handleArrayChange(
                          index,
                          "type",
                          e.target.value,
                          "queryFields"
                        )
                      }
                      className="p-2 w-20 text-sm rounded border"
                    >
                      <option value="string">String</option>
                      <option value="number">Number</option>
                      <option value="integer">Integer</option>
                    </select>
                    <button
                      onClick={() => removeField(index, "queryFields")}
                      className="p-2 text-red-500 rounded hover:bg-red-50"
                    >
                      <Trash className="w-4 h-4" />
                    </button>
                  </div>
                )
              )}
              <div className="flex gap-1 mt-2">
                <input
                  type="text"
                  value={newQueryField.name}
                  onChange={(e) =>
                    setNewQueryField({ ...newQueryField, name: e.target.value })
                  }
                  className="flex-1 p-2 text-sm rounded border"
                  placeholder="New query param"
                />
                <select
                  value={newQueryField.type}
                  onChange={(e) =>
                    setNewQueryField({ ...newQueryField, type: e.target.value })
                  }
                  className="p-2 w-20 text-sm rounded border"
                >
                  <option value="string">String</option>
                  <option value="number">Number</option>
                  <option value="integer">Integer</option>
                </select>
                <button
                  onClick={() => {
                    if (newQueryField.name.trim()) {
                      addField("queryFields");
                    }
                  }}
                  className="p-2 text-white bg-blue-500 rounded hover:bg-blue-600"
                >
                  <Plus className="w-4 h-4" />
                </button>
              </div>
            </div>
            <div className="mb-4">
              <label className="block mb-1 text-sm font-medium">
                Output Type
              </label>
              <select
                name="outputType"
                value={node.data.outputType}
                onChange={handleChange}
                className="p-2 w-full rounded border"
              >
                <option value="json">JSON</option>
                <option value="xml">XML</option>
                <option value="csv">CSV</option>
                <option value="plain">Plain Text</option>
                <option value="html">HTML</option>
              </select>
            </div>

            <div className="mb-4">
              <label className="block mb-1 text-sm font-medium">
                Response Fields
              </label>
              {node.data.responseFields?.length > 0 && (
                <div className="p-2 mb-2 font-mono text-sm bg-gray-50 rounded">
                  <textarea
                    rows={12}
                    value={JSON.stringify(
                      generateExampleJson(node.data.responseFields),
                      null,
                      2
                    )}
                    className="w-full whitespace-pre-wrap break-words"
                    onChange={(e) => {
                      try {
                        const parsedJson = JSON.parse(e.target.value);
                        const newFields = Object.entries(parsedJson).map(
                          ([name, value]) => ({
                            name,
                            type: Array.isArray(value)
                              ? "array"
                              : typeof value === "object" && value !== null
                              ? "object"
                              : (typeof value as string),
                          })
                        );

                        const newData = {
                          ...node.data,
                          responseFields: newFields,
                        };

                        updateNode(node.id, newData);
                      } catch (error) {
                        console.error("Invalid JSON", error);
                      }
                    }}
                  />
                </div>
              )}
              {(node.data.responseFields || []).map(
                (field: Field, index: number) => (
                  <div key={index} className="flex mb-2">
                    <input
                      type="text"
                      value={field.name}
                      onChange={(e) =>
                        handleArrayChange(
                          index,
                          "name",
                          e.target.value,
                          "responseFields"
                        )
                      }
                      className="flex-1 p-2 text-sm rounded border"
                      placeholder="Field name"
                    />
                    <select
                      value={field.type}
                      onChange={(e) =>
                        handleArrayChange(
                          index,
                          "type",
                          e.target.value,
                          "responseFields"
                        )
                      }
                      className="p-2 w-24 text-sm rounded border"
                    >
                      <option value="string">String</option>
                      <option value="number">Number</option>
                      <option value="integer">Integer</option>
                      <option value="boolean">Bool</option>
                      <option value="date">Date</option>
                      <option value="datetime">Date Time</option>
                      <option value="file">File</option>
                      <option value="array">Array</option>
                      <option value="object">Object</option>
                    </select>
                    <button
                      onClick={() => removeField(index, "responseFields")}
                      className="p-2 text-red-500 rounded hover:bg-red-50"
                    >
                      <Trash className="w-4 h-4" />
                    </button>
                  </div>
                )
              )}
              <div className="flex gap-1 mt-2">
                <input
                  type="text"
                  value={newResponseField.name}
                  onChange={(e) =>
                    setNewResponseField({
                      ...newResponseField,
                      name: e.target.value,
                    })
                  }
                  className="flex-1 p-2 text-sm rounded border"
                  placeholder="New field name"
                />
                <select
                  value={newResponseField.type}
                  onChange={(e) =>
                    setNewResponseField({
                      ...newResponseField,
                      type: e.target.value,
                    })
                  }
                  className="p-2 w-24 text-sm rounded border"
                >
                  <option value="string">String</option>
                  <option value="number">Number</option>
                  <option value="boolean">Boolean</option>
                  <option value="date">Date</option>
                  <option value="object">Object</option>
                  <option value="array">Array</option>
                </select>
                <button
                  onClick={() => {
                    if (newResponseField.name.trim()) {
                      addField("responseFields");
                    }
                  }}
                  className="p-2 text-white bg-blue-500 rounded hover:bg-blue-600"
                >
                  <Plus className="w-4 h-4" />
                </button>
              </div>
            </div>

            <div className="mb-4">
              <label className="block mb-1 text-sm font-medium">
                Status Code
              </label>
              <input
                type="number"
                name="statusCode"
                value={node.data.statusCode}
                onChange={handleChange}
                className="p-2 w-full rounded border"
                placeholder="200"
              />
            </div>
          </>
        );

      default:
        return null;
    }
  };

  const handleDeleteNode = () => {
    if (!node) return;

    // Dispatch the REMOVE_NODE action with the node's ID
    dispatch({ type: "REMOVE_NODE", payload: { id: node.id } });

    // Close the panel
    onClose();
  };

  return (
    <div className="overflow-y-auto fixed top-0 right-0 p-4 h-full bg-white border-l border-gray-200 shadow-lg transition-transform transform w-100">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold">Configure Node</h3>
        <button onClick={onClose} className="p-1 rounded hover:bg-gray-100">
          <X className="w-5 h-5" />
        </button>
      </div>
      {renderFields()}
      <div className="mt-8">
        <button
          onClick={handleDeleteNode}
          className="flex gap-2 justify-center items-center p-2 w-full text-white bg-red-500 rounded hover:bg-red-600"
        >
          <Trash2 className="w-4 h-4" />
          Delete Component
        </button>
      </div>
    </div>
  );
}
