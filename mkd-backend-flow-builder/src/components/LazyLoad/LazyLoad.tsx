import React, { memo, Suspense } from "react";
import { Skeleton } from "@/components/Skeleton";
import { MKDLOGO } from "@/assets/images";

interface LazyLoadProps {
  children?: React.ReactNode;
  counts?: number[];
  count?: number;
  className?: string;
  circle?: boolean;
  brand?: boolean;
}
const LazyLoad = ({
  children,
  counts = [1],
  count = 1,
  className,
  circle = false,
  brand = false,
}: LazyLoadProps) => {
  const childrenArray = React.Children.toArray(children).filter(Boolean);
  const compuntedClassName = childrenArray.filter(Boolean)[0]?.props?.className
    ? childrenArray[0]?.props?.className
    : "";
  // console.log("childrenArray >>", childrenArray);
  // console.log("className >>", className);

  return (
    <Suspense
      fallback={
        brand ? (
          <div className="flex flex-col justify-center items-center w-full min-w-full max-w-full bg-black h-svh max-h-svh min-h-svh">
            <img src={MKDLOGO} className="!h-[12.25rem]" />

            <span className="text-[2.8125rem] text-white">Wireframe v5</span>
          </div>
        ) : (
          <Skeleton
            counts={counts}
            count={count}
            className={`${compuntedClassName} ${className}`}
            circle={circle}
          />
        )
      }
    >
      {children}
    </Suspense>
  );
};

export default memo(LazyLoad);
