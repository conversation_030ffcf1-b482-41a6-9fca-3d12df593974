import React, { useState } from "react";
import { ModelPanel } from "@/components/ModelPanel";
import { SettingsForm } from "@/components/SettingsForm";
import { RolesPanel } from "@/components/RolesPanel";
import { RoutesPanel } from "@/components/RoutesPanel";
import { LazyLoad } from "../LazyLoad";

type Tab = "models" | "roles" | "routes" | "settings";

export default function Sidebar() {
  const [activeTab, setActiveTab] = useState<Tab>("models");
  const TABS = {
    models: (
      <LazyLoad>
        <ModelPanel />
      </LazyLoad>
    ),
    roles: (
      <LazyLoad>
        <RolesPanel />
      </LazyLoad>
    ),
    routes: (
      <LazyLoad>
        <RoutesPanel />
      </LazyLoad>
    ),
    settings: (
      <LazyLoad>
        <SettingsForm />
      </LazyLoad>
    ),
  };
  return (
    <div className="flex flex-col w-64 h-full bg-white border-r border-gray-200">
      {/* Tab Buttons */}
      <div className="flex flex-wrap border-b border-gray-200">
        <button
          className={`flex-1 py-3 text-sm font-medium ${
            activeTab === "models"
              ? "text-blue-600 border-b-2 border-blue-600"
              : "text-gray-500 hover:text-gray-700"
          }`}
          onClick={() => setActiveTab("models")}
        >
          Models
        </button>
        <button
          className={`flex-1 py-3 text-sm font-medium ${
            activeTab === "roles"
              ? "text-blue-600 border-b-2 border-blue-600"
              : "text-gray-500 hover:text-gray-700"
          }`}
          onClick={() => setActiveTab("roles")}
        >
          Roles
        </button>
        <button
          className={`flex-1 py-3 text-sm font-medium ${
            activeTab === "routes"
              ? "text-blue-600 border-b-2 border-blue-600"
              : "text-gray-500 hover:text-gray-700"
          }`}
          onClick={() => setActiveTab("routes")}
        >
          Routes
        </button>
        <button
          className={`flex-1 py-3 text-sm font-medium ${
            activeTab === "settings"
              ? "text-blue-600 border-b-2 border-blue-600"
              : "text-gray-500 hover:text-gray-700"
          }`}
          onClick={() => setActiveTab("settings")}
        >
          Settings
        </button>
      </div>

      {/* Tab Content */}
      <div className="overflow-y-auto flex-1 p-4">{TABS[activeTab]}</div>
    </div>
  );
}
