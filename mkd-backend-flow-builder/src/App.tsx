
import { ReactFlowProvider } from "reactflow";
import { Toaster } from "react-hot-toast";
import { Sidebar } from "@/components/Sidebar";
import { FlowProvider } from "@/store/FlowContext";
import { LazyLoad } from "@/components/LazyLoad";

function App() {
  return (
    <LazyLoad brand>
      <Toaster position="top-right" />
      <div className="w-full h-screen">
        <ReactFlowProvider>
          <FlowProvider>
            <div className="flex h-screen">
              <Sidebar />
              <div className="flex-1 bg-white" />
            </div>
          </FlowProvider>
        </ReactFlowProvider>
      </div>
    </LazyLoad>
  );
}

export default App;
