import path from "path";
import { fileURLToPath } from "url";
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
const dirname = path.dirname(fileURLToPath(import.meta.url));

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@/components": path.resolve(dirname, "./src/components"),
      "@/pages": path.resolve(dirname, "./src/pages"),
      "@/utils": path.resolve(dirname, "./src/utils"),
      "@/assets": path.resolve(dirname, "./src/assets"),
      "@/context": path.resolve(dirname, "./src/context"),
      "@/routes": path.resolve(dirname, "./src/routes"),
      "@/hooks": path.resolve(dirname, "./src/hooks"),
      "@/store": path.resolve(dirname, "./src/store"),
      "@": path.resolve(dirname, "./src"),
    },
  },
  optimizeDeps: {
    // exclude: ['lucide-react'],
  },
});
