{"compilerOptions": {"target": "ESNext", "lib": ["DOM", "DOM.Iterable", "ESNext"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "allowJs": true, "esModuleInterop": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "files": [], "include": ["src", "src/types", "src/utils"], "references": [{"path": "./tsconfig.app.json"}, {"path": "./tsconfig.node.json"}]}