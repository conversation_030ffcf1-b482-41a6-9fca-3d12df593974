const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');

async function runSQL() {
  try {
    // Database configuration
    const config = {
      host: '***************',
      user: 'baas',
      password: 'en8elqeiqxR)',
      database: 'baas_db'
    };

    // Create connection
    const connection = await mysql.createConnection(config);
    console.log('Connected to database');

    // Read SQL file
    const sqlFile = path.join(__dirname, 'custom/thinkpartnership_backend/create_tables.sql');
    const sql = fs.readFileSync(sqlFile, 'utf8');

    // Split SQL into individual statements
    const statements = sql.split(';').filter(stmt => stmt.trim().length > 0);

    console.log(`Found ${statements.length} SQL statements`);

    // Execute each statement separately
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i].trim();
      if (statement) {
        try {
          console.log(`Executing statement ${i + 1}/${statements.length}...`);
          await connection.execute(statement);
          console.log(`Statement ${i + 1} executed successfully`);
        } catch (error) {
          console.error(`Error executing statement ${i + 1}:`, error.message);
          // Continue with next statement
        }
      }
    }

    console.log('All SQL statements processed');

    // Check the actual user table structure
    console.log('Checking user table structure...');
    const [tableStructure] = await connection.execute('DESCRIBE thinkpartnership_user');
    console.log('User table structure:', tableStructure);

    // Add user_id column to service_request table for user-based authentication
    console.log('Adding user_id column to service_request table...');
    try {
      await connection.execute(`
        ALTER TABLE thinkpartnership_service_request
        ADD COLUMN user_id INT NULL AFTER customer_id,
        ADD INDEX idx_request_user (user_id)
      `);
      console.log('user_id column added successfully');
    } catch (error) {
      if (error.code !== 'ER_DUP_FIELDNAME') {
        console.error('Error adding user_id column:', error.message);
      } else {
        console.log('user_id column already exists');
      }
    }

    // Close connection
    await connection.end();
    console.log('Database connection closed');

  } catch (error) {
    console.error('Error:', error);
  }
}

runSQL();
