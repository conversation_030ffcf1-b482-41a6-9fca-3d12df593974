require("dotenv/config");
const express = require("express");
const cors = require("cors");
const path = require("path");
const config = require("./config");
const logger = require("morgan");
let DatabaseService = require("./baas/core/DatabaseService");
let BackendSDK = require("./baas/core/BackendSDK");
let customRouter = require("./custom/index");
let lambdaRouter = require("./baas/lambda/index");

const app = express();

// Remove x-powered-by header
app.disable("x-powered-by");

app.use(
  cors({
    origin: "*",
    credentials: true,
    optionSuccessStatus: 200
  })
);
// increase the limit of the request body
app.use(express.json({ limit: "50mb" }));
app.use(express.urlencoded({ extended: true, limit: "50mb" }));

// Serve static files from uploads directory
app.use('/uploads', express.static(path.join(__dirname, 'baas', 'public', 'uploads')));

let dbConnector;
let dbService;
let sdk;

async function connectWithRetry(config, retries = 5, delay = 3000) {
  while (retries > 0) {
    try {
      if (config.database.type === "MySQL") {
        const MySQLConnector = require("./baas/db_connector/mysql");
        dbConnector = new MySQLConnector(config.database);
        await dbConnector.initialize();
      } else if (config.database.type === "PostgreSQL") {
        const PostgreSQLConnector = require("./baas/db_connector/postgresql");
        dbConnector = new PostgreSQLConnector(config.database);
        await dbConnector.initialize();
      } else {
        const JSONConnector = require("./baas/db_connector/json");
        dbConnector = new JSONConnector();
        await dbConnector.initialize();
      }
      console.log("✅ DB connected");
      return dbConnector;
    } catch (err) {
      console.error(`❌ DB connection failed. Retries left: ${retries - 1}`);
      console.error(err.message);
      retries--;
      if (retries === 0) throw err;
      await new Promise((res) => setTimeout(res, delay));
    }
  }
}

(async () => {
  try {
    dbConnector = await connectWithRetry(config); // retry logic inside this
    dbService = new DatabaseService(dbConnector);
    sdk = new BackendSDK(dbService);
    sdk.setDatabase(config.database.database);
    app.set("sdk", sdk);

    console.log("✅ Backend SDK initialized");
  } catch (error) {
    console.error("🚨 Could not initialize full backend:", error.message);
    console.warn("⚠️  Running in degraded mode — database is unavailable.");
    app.set("sdk", null); // indicate degraded mode to routes if needed
  } finally {
    // Always start the server, even if DB fails
    const PORT = config.server.port || 5172;
    app.listen(PORT, () => {
      console.log(`🚀 Server is running on port ${PORT}`);
      console.log(`🗄️ Database type: ${config.database.type}`);
    });
  }
})();

app.enable("trust proxy");

app.get("/", (req, res) => {
  res.json({ message: "ok", error: false });
});

app.get("/api/v1/health", (req, res) => {
  res.json({ message: "ok", error: false });
});

lambdaRouter(app);

app.use(logger("dev"));
app.set("configuration", config);
customRouter(app);

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    error: true,
    message: "Something went wrong!"
  });
});

// List all registered routes
// app._router.stack.forEach(function(r){
//   if (r.route && r.route.path){
//     console.log(`${Object.keys(r.route.methods).join(',')} ${r.route.path}`);
//   } else if (r.name === 'router') {
//     r.handle.stack.forEach(function(layer) {
//       if (layer.route) {
//         console.log(`${Object.keys(layer.route.methods).join(',')} ${layer.route.path}`);
//       }
//     });
//   }
// });

// Export for testing purposes
module.exports = app;
