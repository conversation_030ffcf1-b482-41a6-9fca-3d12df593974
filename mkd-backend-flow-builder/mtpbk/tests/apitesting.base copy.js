/**
 * Create a framework for api testing without using any external libraries
 * To Start:
 * 1. define the structure of the api testing
 * 2. create before and after hooks for what info we need
 * 3. create a way to run the tests
 * 4. create a way to see results of test report without crashing other tests
 */

class APITestFramework {
    constructor() {
        // Store test cases and results
        this.testCases = [];
        this.testResults = {
            passed: [],
            failed: [],
            total: 0
        };
        
        // Hooks for setup and teardown
        this.beforeEachHook = null;
        this.afterEachHook = null;
    }

    // 1. Define structure of API testing
    addTestCase(name, testFunction) {
        this.testCases.push({ name, testFunction });
    }

    // 2. Create before and after hooks
    beforeEach(hookFunction) {
        this.beforeEachHook = hookFunction;
    }

    afterEach(hookFunction) {
        this.afterEachHook = hookFunction;
    }

    // 3. Create a way to run tests
    async runTests() {
        this.testResults.total = this.testCases.length;

        for (const testCase of this.testCases) {
            try {
                // Run before each hook if exists
                if (this.beforeEachHook) {
                    await this.beforeEachHook();
                }

                // Run the actual test
                await testCase.testFunction();
                
                // Mark test as passed
                this.testResults.passed.push(testCase.name);
            } catch (error) {
                // Mark test as failed
                this.testResults.failed.push({
                    name: testCase.name,
                    error: error.message
                });
            } finally {
                // Run after each hook if exists
                if (this.afterEachHook) {
                    await this.afterEachHook();
                }
            }
        }
    }

    // 4. Create a way to see test report
    generateTestReport() {
        console.log('=== API Test Report ===');
        console.log(`Total Tests: ${this.testResults.total}`);
        console.log(`Passed: ${this.testResults.passed.length}`);
        console.log(`Failed: ${this.testResults.failed.length}`);

        if (this.testResults.failed.length > 0) {
            console.log('\nFailed Tests:');
            this.testResults.failed.forEach(failedTest => {
                console.log(`- ${failedTest.name}: \n${failedTest.error}`);
            });
        }

        return {
            total: this.testResults.total,
            passed: this.testResults.passed.length,
            failed: this.testResults.failed.length,
            failedDetails: this.testResults.failed
        };
    }

    // Utility method for assertions
    assert(condition, message) {
        if (!condition) {
            throw new Error(message || 'Assertion failed');
        }
    }

    // Utility method for async HTTP requests
    async makeRequest(url, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        };

        const mergedOptions = { ...defaultOptions, ...options };

        // Start timing
        const startTime = performance.now();

        try {
            const response = await fetch(url, mergedOptions);
            
            // End timing
            const endTime = performance.now();
            const duration = endTime - startTime;

            // Try to parse response body
            let responseBody;
            try {
                responseBody = await response.json();
            } catch (parseError) {
                responseBody = null;
            }

            return {
                status: response.status,
                body: responseBody,
                headers: Object.fromEntries(response.headers.entries()),
                url: url,
                requestOptions: mergedOptions,
                timing: {
                    startTime,
                    endTime,
                    duration: Number(duration.toFixed(2)), // ms with 2 decimal places
                    durationUnit: 'ms'
                }
            };
        } catch (error) {
            // End timing for error case
            const endTime = performance.now();
            const duration = endTime - startTime;

            const errorDetails = {
                url: url,
                method: mergedOptions.method,
                headers: mergedOptions.headers,
                body: mergedOptions.body ? JSON.parse(mergedOptions.body) : null,
                errorMessage: error.message,
                timing: {
                    startTime,
                    endTime,
                    duration: Number(duration.toFixed(2)),
                    durationUnit: 'ms'
                }
            };

            throw new Error(`Request failed:
URL: ${errorDetails.url}
Method: ${errorDetails.method}
Headers: ${JSON.stringify(errorDetails.headers)}
Body: ${JSON.stringify(errorDetails.body)}
Duration: ${errorDetails.timing.duration}ms
Error: ${errorDetails.errorMessage}`);
        }
    }
}

// Export the framework for use in other test files
module.exports = APITestFramework;