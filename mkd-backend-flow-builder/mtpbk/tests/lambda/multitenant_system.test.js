const APITestFramework = require("../../../../tests/apitesting.base.js");

class MultitenantApiTests {
  constructor() {
    this.framework = new APITestFramework();
    this.baseUrl = "http://localhost:3000/v1/api/xyz/wxy/lambda";
    this.testCompanyId = "123";
    this.testUserId = "456";
    this.adminToken = "admin-token";
    this.userToken = "user-token";

    this.setupTests();
  }

  async runTests() {
    try {
      // Run the tests and return the results directly
      return await this.framework.runTests();
    } catch (error) {
      console.error("Test execution failed:", error);
      throw error;
    }
  }

  setupTests() {
    this.framework.describe("wxy Multitenant System API Tests", () => {
      // Setup hooks
      this.framework.beforeEach(async () => {
        // Reset any test state, clear mocks, etc.
      });

      this.framework.afterEach(async () => {
        // Clean up after each test
      });

      // Company Registration Tests
      this.framework.addTestCase(
        "wxy Should successfully register a new company",
        async () => {
          const response = await this.framework.makeRequest(
            `${this.baseUrl}/company`,
            {
              method: "POST",
              headers: {
                Authorization: `Bearer ${this.adminToken}`,
              },
              body: JSON.stringify({
                name: "Test Company",
                address: "123 Test St",
                phone: "1234567890",
                city: "Test City",
                state: "Test State",
                country: "Test Country",
                zip: "12345",
                owner_id: this.testUserId,
              }),
            }
          );

          this.framework.assert(
            response.status === 201,
            "Expected status code 201"
          );
          this.framework.assert(
            response.body.error === false,
            "Expected error to be false"
          );
          this.framework.assert(
            response.body.id,
            "Expected company ID in response"
          );
        }
      );

      this.framework.addTestCase(
        "wxy Should fail to register company without required fields",
        async () => {
          const response = await this.framework.makeRequest(
            `${this.baseUrl}/company`,
            {
              method: "POST",
              headers: {
                Authorization: `Bearer ${this.adminToken}`,
              },
              body: JSON.stringify({
                address: "123 Test St",
              }),
            }
          );

          this.framework.assert(
            response.status === 403,
            "Expected status code 403"
          );
          this.framework.assert(
            response.body.error === true,
            "Expected error to be true"
          );
          this.framework.assert(
            response.body.message === "Company name is required",
            "Expected name required message"
          );
        }
      );

      // Company Update Tests
      this.framework.addTestCase(
        "wxy Should successfully update company details",
        async () => {
          const response = await this.framework.makeRequest(
            `${this.baseUrl}/company/${this.testCompanyId}`,
            {
              method: "PATCH",
              headers: {
                Authorization: `Bearer ${this.adminToken}`,
              },
              body: JSON.stringify({
                name: "Updated Company Name",
                phone: "0987654321",
              }),
            }
          );

          this.framework.assert(
            response.status === 200,
            "Expected status code 200"
          );
          this.framework.assert(
            response.body.error === false,
            "Expected error to be false"
          );
        }
      );

      // User Management Tests
      this.framework.addTestCase(
        "wxy Should successfully add user to company",
        async () => {
          const response = await this.framework.makeRequest(
            `${this.baseUrl}/company/${this.testCompanyId}/user-add`,
            {
              method: "POST",
              headers: {
                Authorization: `Bearer ${this.adminToken}`,
              },
              body: JSON.stringify({
                email: "<EMAIL>",
                password: "testpass123",
                role: "user",
                first_name: "Test",
                last_name: "User",
                verify: 1,
              }),
            }
          );

          this.framework.assert(
            response.status === 200,
            "Expected status code 200"
          );
          this.framework.assert(
            response.body.error === false,
            "Expected error to be false"
          );
          this.framework.assert(
            response.body.message === "User added successfully",
            "Expected success message"
          );
        }
      );

      // Admin Management Tests
      this.framework.addTestCase(
        "wxy Should successfully add admin to company",
        async () => {
          const response = await this.framework.makeRequest(
            `${this.baseUrl}/company/${this.testCompanyId}/admin-add`,
            {
              method: "POST",
              headers: {
                Authorization: `Bearer ${this.adminToken}`,
              },
              body: JSON.stringify({
                user_id: this.testUserId,
              }),
            }
          );

          this.framework.assert(
            response.status === 200,
            "Expected status code 200"
          );
          this.framework.assert(
            response.body.error === false,
            "Expected error to be false"
          );
          this.framework.assert(
            response.body.message === "Admin added successfully",
            "Expected success message"
          );
        }
      );

      // User Authentication Tests
      this.framework.addTestCase(
        "wxy Should successfully authenticate company user",
        async () => {
          const response = await this.framework.makeRequest(
            `${this.baseUrl}/company/${this.testCompanyId}/user-login`,
            {
              method: "POST",
              body: JSON.stringify({
                email: "<EMAIL>",
                password: "testpass123",
              }),
            }
          );

          this.framework.assert(
            response.status === 200,
            "Expected status code 200"
          );
          this.framework.assert(
            response.body.error === false,
            "Expected error to be false"
          );
          this.framework.assert(
            response.body.token,
            "Expected JWT token in response"
          );
          this.framework.assert(
            response.body.role === "user",
            "Expected user role"
          );
        }
      );

      // Company Listing Tests
      this.framework.addTestCase(
        "wxy Should successfully list all companies",
        async () => {
          const response = await this.framework.makeRequest(
            `${this.baseUrl}/company/all`,
            {
              method: "GET",
              headers: {
                Authorization: `Bearer ${this.adminToken}`,
              },
            }
          );

          this.framework.assert(
            response.status === 200,
            "Expected status code 200"
          );
          this.framework.assert(
            response.body.error === false,
            "Expected error to be false"
          );
          this.framework.assert(
            Array.isArray(response.body.list),
            "Expected list of companies"
          );
        }
      );

      // Company Admin Listing Tests
      this.framework.addTestCase(
        "wxy Should successfully list company admins",
        async () => {
          const response = await this.framework.makeRequest(
            `${this.baseUrl}/company/${this.testCompanyId}/admins`,
            {
              method: "GET",
              headers: {
                Authorization: `Bearer ${this.adminToken}`,
              },
            }
          );

          this.framework.assert(
            response.status === 200,
            "Expected status code 200"
          );
          this.framework.assert(
            response.body.error === false,
            "Expected error to be false"
          );
          this.framework.assert(
            Array.isArray(response.body.list),
            "Expected list of admins"
          );
        }
      );

      // Company Usage Reporting Tests
      this.framework.addTestCase(
        "wxy Should successfully report company usage",
        async () => {
          const response = await this.framework.makeRequest(
            `${this.baseUrl}/company/report-usage`,
            {
              method: "POST",
              headers: {
                Authorization: `Bearer ${this.adminToken}`,
              },
              body: JSON.stringify({
                id: this.testCompanyId,
              }),
            }
          );

          this.framework.assert(
            response.status === 200,
            "Expected status code 200"
          );
          this.framework.assert(
            response.body.error === false,
            "Expected error to be false"
          );
          this.framework.assert(
            response.body.message === "Usage reported successfully",
            "Expected success message"
          );
        }
      );

      // Subscription Tests
      this.framework.addTestCase(
        "wxy Should successfully create subscription checkout session",
        async () => {
          const response = await this.framework.makeRequest(
            `${this.baseUrl}/company/subscribe`,
            {
              method: "POST",
              headers: {
                Authorization: `Bearer ${this.adminToken}`,
              },
              body: JSON.stringify({
                mode: "subscription",
                success_url: "https://example.com/success",
                cancel_url: "https://example.com/cancel",
                payment_method_types: ["card"],
                line_items: [
                  {
                    price: "price_123",
                    quantity: 1,
                  },
                ],
                owner_id: this.testUserId,
              }),
            }
          );

          this.framework.assert(
            response.status === 200,
            "Expected status code 200"
          );
          this.framework.assert(
            response.body.error === false,
            "Expected error to be false"
          );
          this.framework.assert(
            response.body.model.id,
            "Expected checkout session ID"
          );
        }
      );
    });
  }
}

// Create an instance of the test class and run the tests
const tests = new MultitenantApiTests();
module.exports = tests
  .runTests()
  .then((report) => {
    if (report.failed > 0) {
      process.exit(1);
    }
    return report; // Important: return the report for the test runner
  })
  .catch((error) => {
    console.error("Test framework error:", error);
    process.exit(1);
  });
