const APITestFramework = require("../../../../tests/apitesting.base.js");

const BASE_URL = "http://localhost:5172";

/**
 * Stripe API Tests
 * Class-based implementation of the Stripe API tests
 */
class StripeTests {
  constructor() {
    this.framework = new APITestFramework();
    
    // Define expected response schemas
    this.productsListSchema = {
      error: false,
      list: "object",
      total: "number",
      limit: "number",
      num_pages: "number",
      page: "number"
    };
    
    this.productSchema = {
      error: false,
      model: "object"
    };
    
    this.successResponseSchema = {
      error: false,
      message: "string"
    };
    
    this.setupTests();
  }

  setupTests() {
    this.framework.describe("wxy Stripe API Tests", () => {
      let authToken;

      // Setup before each test
      this.framework.beforeEach(async () => {
        // Setup mock data
        authToken = "Bearer mock_auth_token";
      });

      // Test case for Get Products
      this.framework.addTestCase("wxy Get Products - Success Path", async () => {
        // Create spy to track request
        const requestSpy = this.framework.createSpy(this.framework, "makeRequest");

        // Mock the API response
        this.framework.mockRequest(
          `${BASE_URL}/v1/api/lambda/stripe/products?limit=10&page=1`,
          {
            error: false,
            list: [
              {
                id: 1,
                stripe_id: "prod_123456",
                name: "Test Product",
                status: 1,
                object: JSON.stringify({
                  id: "prod_123456",
                  name: "Test Product",
                  active: true
                })
              }
            ],
            total: 1,
            limit: 10,
            num_pages: 1,
            page: 1
          },
          {
            status: 200,
            headers: {
              "Content-Type": "application/json"
            }
          }
        );

        const response = await this.framework.makeRequest(
          `${BASE_URL}/v1/api/lambda/stripe/products?limit=10&page=1`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: authToken
            }
          }
        );

        // Assertions
        this.framework.assert(
          response.status === 200,
          "Get Products should return 200 status"
        );
        this.framework.assert(
          response.body.error === false,
          "Get Products error flag should be false"
        );

        // Enhanced assertions
        this.framework.assertions.assertResponseValid(response, this.productsListSchema);
        this.framework.assertions.assertEquals(
          response.body.total,
          1,
          "Should return correct total count"
        );

        // Verify request was made correctly
        this.framework.assert(
          requestSpy.callCount() === 1,
          "API should be called exactly once"
        );
      });

      // Test case for Get Product by ID
      this.framework.addTestCase("wxy Get Product by ID - Success Path", async () => {
        // Mock the API response
        this.framework.mockRequest(
          `${BASE_URL}/v1/api/lambda/stripe/product/1`,
          {
            error: false,
            model: {
              id: 1,
              stripe_id: "prod_123456",
              name: "Test Product",
              status: 1,
              object: JSON.stringify({
                id: "prod_123456",
                name: "Test Product",
                active: true
              })
            }
          },
          {
            status: 200,
            headers: {
              "Content-Type": "application/json"
            }
          }
        );

        const response = await this.framework.makeRequest(
          `${BASE_URL}/v1/api/lambda/stripe/product/1`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: authToken
            }
          }
        );

        // Assertions
        this.framework.assert(
          response.status === 200,
          "Get Product by ID should return 200 status"
        );
        this.framework.assert(
          response.body.error === false,
          "Get Product by ID error flag should be false"
        );

        // Enhanced assertions
        this.framework.assertions.assertResponseValid(response, this.productSchema);
        this.framework.assertions.assertEquals(
          response.body.model.id,
          1,
          "Should return correct product ID"
        );
      });

      // Test case for Get Product by ID - Not Found
      this.framework.addTestCase("wxy Get Product by ID - Not Found", async () => {
        // Mock the API response for error case
        this.framework.mockRequest(
          `${BASE_URL}/v1/api/lambda/stripe/product/999`,
          {
            error: true,
            message: "Product not found"
          },
          {
            status: 404,
            headers: {
              "Content-Type": "application/json"
            }
          }
        );

        const response = await this.framework.makeRequest(
          `${BASE_URL}/v1/api/lambda/stripe/product/999`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: authToken
            }
          }
        );

        // Assertions for error case
        this.framework.assertions.assertEquals(
          response.status,
          404,
          "Should return 404 status for product not found"
        );
        this.framework.assertions.assertEquals(
          response.body.error,
          true,
          "Error flag should be true"
        );
        this.framework.assertions.assertEquals(
          response.body.message,
          "Product not found",
          "Should return correct error message"
        );
      });

      // Test case for Create Product
      this.framework.addTestCase("wxy Create Product - Success Path", async () => {
        // Mock the API response
        this.framework.mockRequest(
          `${BASE_URL}/v1/api/lambda/stripe/product`,
          {
            error: false,
            model: {
              id: 2,
              stripe_id: "prod_new123",
              name: "New Product",
              status: 1
            },
            message: "Product created successfully"
          },
          {
            status: 200,
            headers: {
              "Content-Type": "application/json"
            }
          }
        );

        const response = await this.framework.makeRequest(
          `${BASE_URL}/v1/api/lambda/stripe/product`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: authToken
            },
            body: JSON.stringify({
              name: "New Product",
              description: "A new test product"
            })
          }
        );

        // Assertions
        this.framework.assert(
          response.status === 200,
          "Create Product should return 200 status"
        );
        this.framework.assert(
          response.body.error === false,
          "Create Product error flag should be false"
        );

        // Enhanced assertions
        this.framework.assertions.assertEquals(
          response.body.message,
          "Product created successfully",
          "Should return success message"
        );
        this.framework.assertions.assertEquals(
          response.body.model.name,
          "New Product",
          "Should return created product name"
        );
      });

      // Test case for Update Product
      this.framework.addTestCase("wxy Update Product - Success Path", async () => {
        // Mock the API response
        this.framework.mockRequest(
          `${BASE_URL}/v1/api/lambda/stripe/product/1`,
          {
            error: false,
            message: "Product updated successfully"
          },
          {
            status: 200,
            headers: {
              "Content-Type": "application/json"
            }
          }
        );

        const response = await this.framework.makeRequest(
          `${BASE_URL}/v1/api/lambda/stripe/product/1`,
          {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
              Authorization: authToken
            },
            body: JSON.stringify({
              name: "Updated Product",
              description: "Updated description",
              status: 1
            })
          }
        );

        // Assertions
        this.framework.assert(
          response.status === 200,
          "Update Product should return 200 status"
        );
        this.framework.assert(
          response.body.error === false,
          "Update Product error flag should be false"
        );

        // Enhanced assertions
        this.framework.assertions.assertResponseValid(response, this.successResponseSchema);
        this.framework.assertions.assertEquals(
          response.body.message,
          "Product updated successfully",
          "Should return success message"
        );
      });

      // Test case for Delete Product
      this.framework.addTestCase("wxy Delete Product - Success Path", async () => {
        // Mock the API response
        this.framework.mockRequest(
          `${BASE_URL}/v1/api/lambda/stripe/product/1`,
          {
            error: false,
            message: "Product deleted from stripe and set to inactive at db."
          },
          {
            status: 200,
            headers: {
              "Content-Type": "application/json"
            }
          }
        );

        const response = await this.framework.makeRequest(
          `${BASE_URL}/v1/api/lambda/stripe/product/1`,
          {
            method: "DELETE",
            headers: {
              "Content-Type": "application/json",
              Authorization: authToken
            }
          }
        );

        // Assertions
        this.framework.assert(
          response.status === 200,
          "Delete Product should return 200 status"
        );
        this.framework.assert(
          response.body.error === false,
          "Delete Product error flag should be false"
        );

        // Enhanced assertions
        this.framework.assertions.assertResponseValid(response, this.successResponseSchema);
        this.framework.assertions.assertEquals(
          response.body.message,
          "Product deleted from stripe and set to inactive at db.",
          "Should return success message"
        );
      });

      // Test case for Get Prices
      this.framework.addTestCase("wxy Get Prices - Success Path", async () => {
        // Mock the API response
        this.framework.mockRequest(
          `${BASE_URL}/v1/api/lambda/stripe/prices?limit=10&page=1`,
          {
            error: false,
            list: [
              {
                id: 1,
                stripe_id: "price_123456",
                name: "Basic Plan",
                amount: 1000,
                type: "recurring",
                status: 1,
                currency: "usd",
                productId: 1,
                product_name: "Test Product"
              }
            ],
            total: 1,
            limit: 10,
            num_pages: 1,
            page: 1
          },
          {
            status: 200,
            headers: {
              "Content-Type": "application/json"
            }
          }
        );

        const response = await this.framework.makeRequest(
          `${BASE_URL}/v1/api/lambda/stripe/prices?limit=10&page=1`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: authToken
            }
          }
        );

        // Assertions
        this.framework.assert(
          response.status === 200,
          "Get Prices should return 200 status"
        );
        this.framework.assert(
          response.body.error === false,
          "Get Prices error flag should be false"
        );

        // Enhanced assertions
        this.framework.assertions.assertResponseValid(response, this.productsListSchema);
        this.framework.assertions.assertEquals(
          response.body.total,
          1,
          "Should return correct total count"
        );
      });
    });
  }

  async runTests() {
    try {
      // Run the tests and return the results directly
      return await this.framework.runTests();
    } catch (error) {
      console.error("Test execution failed:", error);
      throw error;
    }
  }
}

// Create an instance of the test class and run the tests
const tests = new StripeTests();
module.exports = tests
  .runTests()
  .then((report) => {
    if (report.failed > 0) {
      process.exit(1);
    }
    return report; // Important: return the report for the test runner
  })
  .catch((error) => {
    console.error("Test framework error:", error);
    process.exit(1);
  });
