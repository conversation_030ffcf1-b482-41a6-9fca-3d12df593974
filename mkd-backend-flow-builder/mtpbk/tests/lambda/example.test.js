const APITestFramework = require("../apitesting.base.js");

class ExampleTests {
  constructor() {
    this.framework = new APITestFramework();
    this.setupTests();
  }

  setupTests() {
    this.framework.describe("Example Test Suite", () => {
      // Setup before running tests
      this.framework.beforeEach(async () => {
        // Setup code here
      });

      // Add test cases
      this.framework.addTestCase("Test Case 1", async () => {
        const response = await this.framework.makeRequest("http://example.com");
        console.log("response.status >>", statuses);
        this.framework.assert(response.status === 200, "Should return 200");
      });

      this.framework.addTestCase("Test Case 2", async () => {
        // Test code here
        this.framework.assertions.assertEquals(1, 1, "Numbers should match");
      });
    });
  }

  async runTests() {
    try {
      const results = await this.framework.runTests();

      this.framework.generateTestReport();
      return results;
    } catch (error) {
      console.error("Test execution failed:", error);
      throw error;
    }
  }
}

// Create and run tests
const tests = new ExampleTests();
module.exports = tests
  .runTests()
  .then((report) => {
    if (report.failed > 0) {
      process.exit(1);
    }
    return report; // Important: return the report for the test runner
  })
  .catch((error) => {
    console.error("Test framework error:", error);
    process.exit(1);
  });
