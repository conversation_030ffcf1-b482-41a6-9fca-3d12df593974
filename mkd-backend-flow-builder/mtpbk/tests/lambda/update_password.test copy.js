
const APITestFramework = require('../../../../tests/apitesting.base.js');

const apiTest = new APITestFramework();

const BASE_URL = 'http://localhost:5172';



apiTest.addTestCase('Update Password wxy API', async () => {
    const authToken = 'Bearer YOUR_AUTH_TOKEN';
    const response = await apiTest.makeRequest(`${BASE_URL}/v1/api/xyz/wxy/lambda/update/password`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': authToken
        },
        body: JSON.stringify({
            password: 'a123456',
        })
    });

    // Assertions for the health check
    apiTest.assert(response.status === 200, 'Update Password wxy should return 200 status');
    
    apiTest.assert(
        response.body.error === false , 
        'Update Email wxy error flag should be false'
    );
});


// Run the tests and generate report
apiTest.runTests().then(() => {
    const report = apiTest.generateTestReport();
    
    // Optional: You could add additional reporting or logging here
    if (report.failed > 0) {
        process.exit(1);
    }
}).catch(error => {
    console.error('Test framework error:', error);
    process.exit(1);
});