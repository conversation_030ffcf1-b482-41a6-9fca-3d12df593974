const APITestFramework = require('../../../../tests/apitesting.base.js');

const apiTest = new APITestFramework();

const BASE_URL = 'http://localhost:5172';

class GoogleAuthTests {
  constructor() {
    this.framework = new APITestFramework();
    this.setupTests();
  }

  setupTests() {
    // Test Google Login URL Generation
    this.framework.addTestCase('Google Login URL Generation', async () => {
      const response = await this.framework.makeRequest(BASE_URL + '/v2/api/xyz/wxy/lambda/google/login', {
        method: 'GET'
      });

      this.framework.assert(response.status === 200, 'Should return 200 status code');
      this.framework.assert(response.body.includes('accounts.google.com'), 'Should return Google auth URL');
    });

    // Test Google Login with Company ID
    this.framework.addTestCase('Google Login with Company ID', async () => {
      const response = await this.framework.makeRequest(BASE_URL + '/v2/api/xyz/wxy/lambda/google/login', {
        method: 'GET',
        query: {
          company_id: '123',
          is_refresh: 'true'
        }
      });

      this.framework.assert(response.status === 200, 'Should return 200 status code');
      this.framework.assert(response.body.includes('company_id'), 'Should include company ID in auth URL');
    });

    // Test Google Code Callback
    this.framework.addTestCase('Google Code Callback - Success', async () => {
      const response = await this.framework.makeRequest(BASE_URL + '/v2/api/xyz/wxy/lambda/google/code', {
        method: 'GET',
        query: {
          code: 'valid-google-auth-code',
          state: Buffer.from('xyz:_').toString('base64') + '~wxy'
        }
      });

      this.framework.assert(response.status === 302, 'Should return 302 redirect status');
      this.framework.assert(response.headers.location.includes('/login/oauth'), 'Should redirect to oauth endpoint');
    });

    // Test Google Code Mobile
    this.framework.addTestCase('Google Code Mobile - Success', async () => {
      const response = await this.framework.makeRequest(BASE_URL + '/v2/api/xyz/wxy/lambda/google/code/mobile', {
        method: 'GET',
        query: {
          code: 'valid-mobile-auth-code',
          role: 'wxy',
          is_refresh: 'true'
        }
      });

      this.framework.assert(response.status === 200, 'Should return 200 status code');
      this.framework.assert(!response.body.error, 'Should not return error');
      this.framework.assert(response.body.token, 'Should return access token');
      this.framework.assert(response.body.refresh_token, 'Should return refresh token');
    });

    // Test Invalid Google Code
    this.framework.addTestCase('Google Code - Invalid Code', async () => {
      const response = await this.framework.makeRequest(BASE_URL + '/v2/api/xyz/wxy/lambda/google/code', {
        method: 'GET',
        query: {
          code: 'invalid-code',
          state: Buffer.from('xyz:_').toString('base64') + '~wxy'
        }
      });

      this.framework.assert(response.status === 302, 'Should return 302 redirect status');
      this.framework.assert(response.headers.location.includes('error=true'), 'Should redirect with error parameter');
    });
  }

  async runTests() {
    await this.framework.runTests();
    return this.framework.generateTestReport();
  }
}

const googleAuthTests = new GoogleAuthTests();
googleAuthTests.runTests().then(report => {
  console.log(report);
  process.exit(0);
}).catch(error => {
  console.error('Test framework error:', error);
  process.exit(1);
});