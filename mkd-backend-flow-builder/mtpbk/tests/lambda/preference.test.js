const APITestFramework = require("../apitesting.base.js");
const BASE_URL = "http://localhost:5172";

/**
 * Preference API Tests
 * Class-based implementation of the Preference API tests
 */
class PreferenceTests {
  constructor() {
    this.framework = new APITestFramework();
    this.setupTests();
  }

  setupTests() {
    this.framework.describe("wxy Preference API Tests", () => {
      // Preference Tests
      this.framework.addTestCase("wxy Get Preference - Success", async () => {
        const response = await this.framework.makeRequest(
          BASE_URL + "/v1/api/xyz/wxy/lambda/preference",
          {
            method: "GET",
            headers: {
              Authorization: "Bearer valid-token",
            },
          }
        );

        this.framework.assert(
          response.status === 200,
          "Should return 200 status code"
        );
        this.framework.assert(!response.body.error, "Should not return error");
        this.framework.assert(
          response.body.model,
          "Should return preference model"
        );
      });

      this.framework.addTestCase(
        "wxy Update Preference - Success",
        async () => {
          const response = await this.framework.makeRequest(
            BASE_URL + "/v1/api/xyz/wxy/lambda/preference",
            {
              method: "POST",
              headers: {
                Authorization: "Bearer valid-token",
              },
              body: JSON.stringify({
                payload: {
                  theme: "dark",
                  language: "en",
                },
              }),
            }
          );

          this.framework.assert(
            response.status === 200,
            "Should return 200 status code"
          );
          this.framework.assert(
            !response.body.error,
            "Should not return error"
          );
          this.framework.assert(
            response.body.message === "Updated",
            "Should return success message"
          );
        }
      );

      this.framework.addTestCase(
        "wxy Update Preference - Invalid Payload",
        async () => {
          const response = await this.framework.makeRequest(
            BASE_URL + "/v1/api/xyz/wxy/lambda/preference",
            {
              method: "POST",
              headers: {
                Authorization: "Bearer valid-token",
              },
              body: JSON.stringify({
                payload: {
                  invalid_field: "invalid_value",
                },
              }),
            }
          );

          this.framework.assert(
            response.status === 403,
            "Should return 403 status code"
          );
          this.framework.assert(
            response.body.error === true,
            "Should return error flag"
          );
          this.framework.assert(
            response.body.errors,
            "Should return validation errors"
          );
        }
      );
    });
  }

  async runTests() {
    try {
      // Run the tests and return the results directly
      // without generating a report (let the test runner handle that)
      return await this.framework.runTests();
    } catch (error) {
      console.error("Test execution failed:", error);
      throw error;
    }
  }
}

// Create and run tests
const tests = new PreferenceTests();
module.exports = tests
  .runTests()
  .then((report) => {
    if (report.failed > 0) {
      process.exit(1);
    }
    return report; // Important: return the report for the test runner
  })
  .catch((error) => {
    console.error("Test framework error:", error);
    process.exit(1);
  });
