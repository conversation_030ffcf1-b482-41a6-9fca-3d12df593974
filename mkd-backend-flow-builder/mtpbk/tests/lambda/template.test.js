const APITestFramework = require("../apitesting.base.js");
const BASE_URL = "http://localhost:5172";

/**
 * Template for Lambda API Tests
 * Replace ClassName, role, and endpoint as needed
 */
class TemplateApiTests {
  constructor() {
    this.framework = new APITestFramework();
    this.setupTests();
  }

  setupTests() {
    this.framework.describe("role Lambda API Tests", () => {
      // Setup before running tests
      this.framework.beforeEach(async () => {
        // Setup code here if needed
      });

      // Example test case
      this.framework.addTestCase("role Lambda - Success", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v1/api/xyz/role/lambda/endpoint`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              // Request body here
            }),
          }
        );

        // Assertions
        this.framework.assert(
          response.status === 200,
          "Should return 200 status code"
        );
        this.framework.assert(!response.body.error, "Should not return error");
      });

      // Add more test cases as needed
    });
  }

  async runTests() {
    try {
      // Run the tests and return the results directly
      // without generating a report (let the test runner handle that)
      return await this.framework.runTests();
    } catch (error) {
      console.error("Test execution failed:", error);
      throw error;
    }
  }
}

// Create and run tests
const tests = new TemplateApiTests();
module.exports = tests
  .runTests()
  .then((report) => {
    if (report.failed > 0) {
      process.exit(1);
    }
    return report; // Important: return the report for the test runner
  })
  .catch((error) => {
    console.error("Test framework error:", error);
    process.exit(1);
  });
