/**
 * TODO:
 * 1. Create testing class without using any testing framework
 * 2. Include a before and after function for test
 * 3. Run a list of tests and give report without affecting test of other tests
 */

class BaseTest {
    constructor() {
        this.errors = [];
    }

    assertEqual(actual, expected, message) {
        try {
            if (JSON.stringify(actual) !== JSON.stringify(expected)) {
                throw new Error(`${message}\nExpected: ${JSON.stringify(expected, null, 2)}\nActual: ${JSON.stringify(actual, null, 2)}`);
            }
        } catch (error) {
            this.errors.push(error);
            console.error(error.message);
        }
    }

    async runTests() {
        throw new Error('runTests method must be implemented by child class');
    }

    printSummary() {
        if (this.errors.length === 0) {
            console.log('All tests passed successfully!');
        } else {
            console.log(`Failed tests: ${this.errors.length}`);
            this.errors.forEach((error, index) => {
                console.log(`\n${index + 1}. ${error.message}`);
            });
        }
    }
}

module.exports = BaseTest;