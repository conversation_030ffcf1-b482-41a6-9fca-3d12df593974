const Migration = require('../migration');
const BaseTest = require('./base.test');

class MigrationTest extends BaseTest {
  constructor() {
    super();
  }

  async testGenerateCreateTableSQL() {
    // Test case 1: Basic table creation
    const model = {
      name: 'users',
      fields: [
        { name: 'id', type: 'primary key' },
        { name: 'username', type: 'string', validation: ['required'] },
        { name: 'email', type: 'string' },
        { name: 'created_at', type: 'timestamp', defaultValue: 'CURRENT_TIMESTAMP' }
      ]
    };

    const expectedSQL = `CREATE TABLE IF NOT EXISTS test_users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(255) NOT NULL,
  email VARCHAR(255),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

`;

    const result = Migration.generateCreateTableSQL(model, 'test');
    this.assertEqual(result, expectedSQL, 'Should generate correct CREATE TABLE SQL');

    // Test case 2: Table with JSON field
    const modelWithJson = {
      name: 'settings',
      fields: [
        { name: 'id', type: 'primary key' },
        { name: 'config', type: 'json' }
      ]
    };

    const expectedJsonSQL = `CREATE TABLE IF NOT EXISTS test_settings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  config TEXT
);

`;

    const jsonResult = Migration.generateCreateTableSQL(modelWithJson, 'test');
    this.assertEqual(jsonResult, expectedJsonSQL, 'Should handle JSON field type correctly');
  }

  async testGenerateDropTableSQL() {
    const model = {
      name: 'users'
    };

    const expectedSQL = 'DROP TABLE IF EXISTS test_users;\n';
    const result = Migration.generateDropTableSQL(model, 'test');
    this.assertEqual(result, expectedSQL, 'Should generate correct DROP TABLE SQL');
  }

  async testGenerateRelationMethod() {
    const relationModel = 'profile';
    const expected = `
    getProfile(fields) {
      const profileModel = require('./profile.js');
      return new profileModel(fields);
    }
  `;

    const result = Migration.generateRelationMethod(relationModel);
    this.assertEqual(result, expected, 'Should generate correct relation method');
  }

  async testGenerateRelationMethods() {
    const model = {
      name: 'users',
      fields: [
        { name: 'id', type: 'primary key' },
        { name: 'profile_id', type: 'foreign key', relation: 'profile' },
        { name: 'role_id', type: 'foreign key', relation: 'role' }
      ]
    };

    const expected = `
    getProfile(fields) {
      const profileModel = require('./profile.js');
      return new profileModel(fields);
    }
  

    getRole(fields) {
      const roleModel = require('./role.js');
      return new roleModel(fields);
    }
  `;

    const result = Migration.generateRelationMethods(model);
    this.assertEqual(result, expected, 'Should generate all relation methods correctly');
  }

  async testGenerateModelContent() {
    // Test case 1: Basic model
    const model = {
      name: 'users',
      fields: [
        { name: 'id', type: 'primary key' },
        { name: 'username', type: 'string', validation: ['required'] },
        { name: 'email', type: 'string' },
        { name: 'profile_id', type: 'foreign key', relation: 'profile' }
      ]
    };

    const expectedContent = `const BaseModel = require('./base_model.js');

class Users extends BaseModel {
  constructor(fields) {
    super(fields);
    this.tableName = 'users';
  }

    getProfile(fields) {
      const profileModel = require('./profile.js');
      return new profileModel(fields);
    }
  
}

module.exports = Users;`;

    const result = Migration.generateModelContent(model);
    this.assertEqual(result, expectedContent, 'Should generate correct model content');

    // Test case 2: Model without relations
    const simpleModel = {
      name: 'settings',
      fields: [
        { name: 'id', type: 'primary key' },
        { name: 'config', type: 'json' }
      ]
    };

    const expectedSimpleContent = `const BaseModel = require('./base_model.js');

class Settings extends BaseModel {
  constructor(fields) {
    super(fields);
    this.tableName = 'settings';
  }
}

module.exports = Settings;`;

    const simpleResult = Migration.generateModelContent(simpleModel);
    this.assertEqual(simpleResult, expectedSimpleContent, 'Should generate correct model content without relations');
  }

  async testGenerateRoleModel() {
    const expectedContent = `const BaseModel = require('./base_model.js');

class Role extends BaseModel {
  constructor(fields) {
    super(fields);
    this.tableName = 'role';
  }
}

module.exports = Role;`;

    const result = Migration.generateRoleModel();
    this.assertEqual(result, expectedContent, 'Should generate correct role model content');
  }

  async testGenerateUrlValidation() {
    // Test case 1: Basic URL validation
    const api = {
      url: '/api/users/:id',
      method: 'GET',
      params: {
        id: { type: 'number', required: true }
      },
      query: {
        filter: { type: 'string', required: false }
      }
    };

    const expectedCode = `
    // Validate URL parameters
    if (!req.params.id) {
      return res.status(400).json({ error: 'Missing required parameter: id' });
    }
    if (isNaN(req.params.id)) {
      return res.status(400).json({ error: 'Invalid parameter type: id should be a number' });
    }
`;

    const result = Migration.generateUrlValidation(api);
    this.assertEqual(result, expectedCode, 'Should generate correct URL validation code');

    // Test case 2: Multiple required parameters
    const complexApi = {
      url: '/api/users/:userId/posts/:postId',
      method: 'GET',
      params: {
        userId: { type: 'number', required: true },
        postId: { type: 'number', required: true }
      }
    };

    const expectedComplexCode = `
    // Validate URL parameters
    if (!req.params.userId) {
      return res.status(400).json({ error: 'Missing required parameter: userId' });
    }
    if (isNaN(req.params.userId)) {
      return res.status(400).json({ error: 'Invalid parameter type: userId should be a number' });
    }
    if (!req.params.postId) {
      return res.status(400).json({ error: 'Missing required parameter: postId' });
    }
    if (isNaN(req.params.postId)) {
      return res.status(400).json({ error: 'Invalid parameter type: postId should be a number' });
    }
`;

    const complexResult = Migration.generateUrlValidation(complexApi);
    this.assertEqual(complexResult, expectedComplexCode, 'Should generate correct URL validation code for multiple parameters');
  }

  async testGenerateDatabaseFind() {
    // Test case 1: Basic find
    const model = {
      name: 'users',
      fields: [
        { name: 'id', type: 'primary key' },
        { name: 'username', type: 'string' }
      ]
    };

    const expectedCode = `
    const result = await db.findOne('users', { id: params.id });
    if (!result) {
      return res.status(404).json({ error: 'User not found' });
    }
    return result;`;

    const result = Migration.generateDatabaseFind(model);
    this.assertEqual(result, expectedCode, 'Should generate correct database find code');
  }

  async testGenerateDatabaseQuery() {
    // Test case 1: Basic query
    const model = {
      name: 'users',
      fields: [
        { name: 'id', type: 'primary key' },
        { name: 'status', type: 'string' }
      ]
    };

    const expectedCode = `
    const query = {};
    if (params.status) {
      query.status = params.status;
    }
    const results = await db.find('users', query);
    return results;`;

    const result = Migration.generateDatabaseQuery(model);
    this.assertEqual(result, expectedCode, 'Should generate correct database query code');

    // Test case 2: Query with pagination
    const expectedPaginatedCode = `
    const query = {};
    if (params.status) {
      query.status = params.status;
    }
    const page = parseInt(params.page) || 1;
    const limit = parseInt(params.limit) || 10;
    const skip = (page - 1) * limit;
    const results = await db.find('users', query, { skip, limit });
    return results;`;

    const paginatedResult = Migration.generateDatabaseQuery(model, true);
    this.assertEqual(paginatedResult, expectedPaginatedCode, 'Should generate correct paginated query code');
  }

  async testGenerateDatabaseInsert() {
    const model = {
      name: 'users',
      fields: [
        { name: 'id', type: 'primary key' },
        { name: 'username', type: 'string', validation: ['required'] },
        { name: 'email', type: 'string', validation: ['required', 'email'] }
      ]
    };

    const expectedCode = `
    const insertData = {
      username: params.username,
      email: params.email
    };
    const result = await db.insert('users', insertData);
    return result;`;

    const result = Migration.generateDatabaseInsert(model);
    this.assertEqual(result, expectedCode, 'Should generate correct database insert code');
  }

  async testGenerateDatabaseUpdate() {
    const model = {
      name: 'users',
      fields: [
        { name: 'id', type: 'primary key' },
        { name: 'username', type: 'string' },
        { name: 'email', type: 'string' }
      ]
    };

    const expectedCode = `
    const updateData = {};
    if (params.username !== undefined) updateData.username = params.username;
    if (params.email !== undefined) updateData.email = params.email;
    
    const result = await db.update('users', { id: params.id }, updateData);
    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'User not found' });
    }
    return result;`;

    const result = Migration.generateDatabaseUpdate(model);
    this.assertEqual(result, expectedCode, 'Should generate correct database update code');
  }

  async testGenerateDatabaseDelete() {
    const model = {
      name: 'users',
      fields: [
        { name: 'id', type: 'primary key' }
      ]
    };

    const expectedCode = `
    const result = await db.delete('users', { id: params.id });
    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'User not found' });
    }
    return { success: true };`;

    const result = Migration.generateDatabaseDelete(model);
    this.assertEqual(result, expectedCode, 'Should generate correct database delete code');
  }

  async testGenerateFieldValidation() {
    // Test case 1: Required string field
    const stringField = {
      name: 'username',
      type: 'string',
      validation: ['required', 'min:3', 'max:50']
    };

    const expectedStringValidation = `
    if (typeof username !== 'string') {
      return res.status(400).json({ error: 'username must be a string' });
    }
    if (username.length < 3) {
      return res.status(400).json({ error: 'username must be at least 3 characters long' });
    }
    if (username.length > 50) {
      return res.status(400).json({ error: 'username cannot exceed 50 characters' });
    }`;

    const stringResult = Migration.generateFieldValidation(stringField);
    this.assertEqual(stringResult, expectedStringValidation, 'Should generate correct string field validation');

    // Test case 2: Number field with range
    const numberField = {
      name: 'age',
      type: 'integer',
      validation: ['min:18', 'max:100']
    };

    const expectedNumberValidation = `
    if (isNaN(age)) {
      return res.status(400).json({ error: 'age must be a number' });
    }
    if (age < 18) {
      return res.status(400).json({ error: 'age must be at least 18' });
    }
    if (age > 100) {
      return res.status(400).json({ error: 'age cannot exceed 100' });
    }`;

    const numberResult = Migration.generateFieldValidation(numberField);
    this.assertEqual(numberResult, expectedNumberValidation, 'Should generate correct number field validation');

    // Test case 3: Email validation
    const emailField = {
      name: 'email',
      type: 'string',
      validation: ['required', 'email']
    };

    const expectedEmailValidation = `
    if (typeof email !== 'string') {
      return res.status(400).json({ error: 'email must be a string' });
    }
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      return res.status(400).json({ error: 'Invalid email format' });
    }`;

    const emailResult = Migration.generateFieldValidation(emailField);
    this.assertEqual(emailResult, expectedEmailValidation, 'Should generate correct email validation');
  }

  async testGenerateObjectValidation() {
    // Test case 1: Basic object validation
    const objectSchema = {
      name: 'user',
      fields: [
        { name: 'username', type: 'string', validation: ['required'] },
        { name: 'email', type: 'string', validation: ['required', 'email'] },
        { name: 'age', type: 'integer', validation: ['min:18'] }
      ]
    };

    const expectedValidation = `
    // Validate user object
    if (!user || typeof user !== 'object') {
      return res.status(400).json({ error: 'Invalid user object' });
    }
    
    if (!user.username) {
      return res.status(400).json({ error: 'username is required' });
    }
    if (typeof user.username !== 'string') {
      return res.status(400).json({ error: 'username must be a string' });
    }
    
    if (!user.email) {
      return res.status(400).json({ error: 'email is required' });
    }
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(user.email)) {
      return res.status(400).json({ error: 'Invalid email format' });
    }
    
    if (user.age !== undefined) {
      if (isNaN(user.age)) {
        return res.status(400).json({ error: 'age must be a number' });
      }
      if (user.age < 18) {
        return res.status(400).json({ error: 'age must be at least 18' });
      }
    }`;

    const result = Migration.generateObjectValidation(objectSchema);
    this.assertEqual(result, expectedValidation, 'Should generate correct object validation');
  }

  async testGenerateMockValue() {
    // Test case 1: String type
    const stringResult = Migration.generateMockValue('string');
    this.assertEqual(typeof stringResult, 'string', 'Should generate mock string value');

    // Test case 2: Integer type
    const intResult = Migration.generateMockValue('integer');
    this.assertTrue(Number.isInteger(intResult), 'Should generate mock integer value');

    // Test case 3: Boolean type
    const boolResult = Migration.generateMockValue('boolean');
    this.assertEqual(typeof boolResult, 'boolean', 'Should generate mock boolean value');

    // Test case 4: Date type
    const dateResult = Migration.generateMockValue('date');
    this.assertTrue(dateResult instanceof Date, 'Should generate mock date value');
  }

  async testGenerateMockObjectValue() {
    const schema = {
      fields: [
        { name: 'id', type: 'integer' },
        { name: 'name', type: 'string' },
        { name: 'email', type: 'string', validation: ['email'] },
        { name: 'isActive', type: 'boolean' },
        { name: 'createdAt', type: 'date' }
      ]
    };

    const result = Migration.generateMockObjectValue(schema);
    
    this.assertTrue(Number.isInteger(result.id), 'Should generate integer ID');
    this.assertEqual(typeof result.name, 'string', 'Should generate string name');
    this.assertTrue(
      /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(result.email),
      'Should generate valid email'
    );
    this.assertEqual(typeof result.isActive, 'boolean', 'Should generate boolean value');
    this.assertTrue(result.createdAt instanceof Date, 'Should generate date value');
  }

  async testGenerateMockResponseObject() {
    // Test case 1: Single object response
    const singleSchema = {
      type: 'object',
      fields: [
        { name: 'id', type: 'integer' },
        { name: 'name', type: 'string' },
        { name: 'email', type: 'string', validation: ['email'] }
      ]
    };

    const singleResult = Migration.generateMockResponseObject(singleSchema);
    this.assertTrue(Number.isInteger(singleResult.id), 'Should generate integer ID');
    this.assertEqual(typeof singleResult.name, 'string', 'Should generate string name');
    this.assertTrue(
      /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(singleResult.email),
      'Should generate valid email'
    );

    // Test case 2: Array response
    const arraySchema = {
      type: 'array',
      items: {
        type: 'object',
        fields: [
          { name: 'id', type: 'integer' },
          { name: 'title', type: 'string' }
        ]
      }
    };

    const arrayResult = Migration.generateMockResponseObject(arraySchema);
    this.assertTrue(Array.isArray(arrayResult), 'Should generate array response');
    this.assertTrue(arrayResult.length > 0, 'Should generate non-empty array');
    this.assertTrue(Number.isInteger(arrayResult[0].id), 'Should generate integer ID in array item');
    this.assertEqual(typeof arrayResult[0].title, 'string', 'Should generate string title in array item');
  }

  async testProcessAPI() {
    // Mock fs.writeFileSync
    const originalWriteFileSync = require('fs').writeFileSync;
    const mockFiles = {};
    require('fs').writeFileSync = (path, content) => {
      mockFiles[path] = content;
    };

    try {
      const config = require('./configuration-test.json');
      await Migration.processAPI(config, 'test');

      // Verify generated files
      this.assertTrue(
        mockFiles['test/routes/users.js'].includes('router.get(\'/users\''),
        'Should generate users routes'
      );
      this.assertTrue(
        mockFiles['test/models/user.js'].includes('class User extends BaseModel'),
        'Should generate user model'
      );
      this.assertTrue(
        mockFiles['test/handlers/users.js'].includes('async function handleGetUsers'),
        'Should generate user handlers'
      );
    } finally {
      // Restore original fs.writeFileSync
      require('fs').writeFileSync = originalWriteFileSync;
    }
  }

  async testGenerateRoutes() {
    const api = {
      name: 'users',
      routes: [
        {
          method: 'GET',
          path: '/users',
          handler: 'handleGetUsers',
          auth: true
        },
        {
          method: 'POST',
          path: '/users',
          handler: 'handleCreateUser',
          auth: false
        }
      ]
    };

    const expectedCode = `const express = require('express');
const router = express.Router();
const handlers = require('../handlers/users.js');
const auth = require('../middleware/auth.js');

router.get('/users', auth, handlers.handleGetUsers);
router.post('/users', handlers.handleCreateUser);

module.exports = router;`;

    const result = Migration.generateRoutes(api);
    this.assertEqual(result, expectedCode, 'Should generate correct routes');
  }

  async testGenerateRouteHandler() {
    const api = {
      method: 'GET',
      path: '/users/:id',
      response: {
        type: 'object',
        fields: [
          { name: 'id', type: 'integer' },
          { name: 'username', type: 'string' }
        ]
      }
    };

    const expectedCode = `async function handleGetUser(req, res) {
  try {
    const params = { ...req.params, ...req.query, ...req.body };
    
    // Validate URL parameters
    if (!req.params.id) {
      return res.status(400).json({ error: 'Missing required parameter: id' });
    }
    if (isNaN(req.params.id)) {
      return res.status(400).json({ error: 'Invalid parameter type: id should be a number' });
    }

    const result = await db.findOne('users', { id: params.id });
    if (!result) {
      return res.status(404).json({ error: 'User not found' });
    }
    return res.json(result);
  } catch (error) {
    console.error('Error in handleGetUser:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}`;

    const result = Migration.generateRouteHandler(api);
    this.assertEqual(result, expectedCode, 'Should generate correct route handler');
  }

  async testGenerateVariableList() {
    const variables = [
      { name: 'userId', type: 'integer', required: true },
      { name: 'status', type: 'string', defaultValue: 'active' },
      { name: 'tags', type: 'array' }
    ];

    const expectedCode = `
    const userId = params.userId;
    const status = params.status || 'active';
    const tags = params.tags || [];`;

    const result = Migration.generateVariableList(variables);
    this.assertEqual(result, expectedCode, 'Should generate correct variable list');
  }

  async testGenerateCode() {
    const codeBlocks = [
      'const userId = params.userId;',
      'if (!userId) return res.status(400).json({ error: "Missing userId" });',
      'const user = await db.findOne("users", { id: userId });'
    ];

    const expectedCode = `
    const userId = params.userId;
    if (!userId) return res.status(400).json({ error: "Missing userId" });
    const user = await db.findOne("users", { id: userId });`;

    const result = Migration.generateCode(codeBlocks);
    this.assertEqual(result, expectedCode, 'Should generate correct code block');
  }

  async testGenerateAuth() {
    const expectedCode = `const jwt = require('jsonwebtoken');
const config = require('../config');

function auth(req, res, next) {
  const token = req.header('x-auth-token');
  if (!token) {
    return res.status(401).json({ error: 'Access denied. No token provided.' });
  }

  try {
    const decoded = jwt.verify(token, config.jwtSecret);
    req.user = decoded;
    next();
  } catch (error) {
    res.status(400).json({ error: 'Invalid token.' });
  }
}

module.exports = auth;`;

    const result = Migration.generateAuth();
    this.assertEqual(result, expectedCode, 'Should generate correct auth middleware');
  }

  async testGenerateOutput() {
    // Test case 1: Success response
    const successResponse = {
      type: 'success',
      data: { id: 1, name: 'Test' }
    };

    const expectedSuccessCode = `return res.json({ id: 1, name: 'Test' });`;
    const successResult = Migration.generateOutput(successResponse);
    this.assertEqual(successResult, expectedSuccessCode, 'Should generate correct success response');

    // Test case 2: Error response
    const errorResponse = {
      type: 'error',
      status: 404,
      message: 'Not found'
    };

    const expectedErrorCode = `return res.status(404).json({ error: 'Not found' });`;
    const errorResult = Migration.generateOutput(errorResponse);
    this.assertEqual(errorResult, expectedErrorCode, 'Should generate correct error response');
  }

  async runTests() {
    try {
      await this.testGenerateCreateTableSQL();
      await this.testGenerateDropTableSQL();
      await this.testGenerateRelationMethod();
      await this.testGenerateRelationMethods();
      //   await this.testGenerateModelContent();
      //   await this.testGenerateRoleModel();
      await this.testGenerateUrlValidation();
      await this.testGenerateDatabaseFind();
      await this.testGenerateDatabaseQuery();
      await this.testGenerateDatabaseInsert();
      await this.testGenerateDatabaseUpdate();
      await this.testGenerateDatabaseDelete();
      await this.testGenerateFieldValidation();
      await this.testGenerateObjectValidation();
      await this.testGenerateMockValue();
      await this.testGenerateMockObjectValue();
      await this.testGenerateMockResponseObject();
      await this.testProcessAPI();
      await this.testGenerateRoutes();
      await this.testGenerateRouteHandler();
      await this.testGenerateVariableList();
      await this.testGenerateCode();
      await this.testGenerateOutput();
      await this.testGenerateAuth();
    } catch (error) {
      console.error('Test execution error:', error);
    } finally {
      this.printSummary();
    }
  }
}

// Run the tests if this file is being run directly
if (require.main === module) {
  const test = new MigrationTest();
  test.runTests().catch(console.error);
}

// Export the test class for use in other files
module.exports = MigrationTest;