module.exports = function (router, dbService) {
  // GET single record
  router.get("/api/v1/records/:table/:id", [], async function (req, res) {
    // Mock response structure
    //role -> find hidden fields
    //remove those fields
    // treeql for rest
    //joins is biggest benefit user, cms -> /user?join=cms&join=cms2&page=1&limit=10
    return res.json({
      status: 200,
      response: {
        model: {
          id: "123",
          name: "Sample Record",
          // ... other record fields
        },
        mapping: {
          name: "Full Name",
          // ... field mappings for UI
        }
      }
    });
  });

  // LIST/PAGINATE records
  router.get("/api/v1/records/:table", [], async function (req, res) {
    // Mock response structure
    return res.json({
      status: 200,
      response: {
        list: [
          { id: "1", name: "Record 1" },
          { id: "2", name: "Record 2" }
        ],
        mapping: {
          name: "Full Name"
        },
        pagination: {
          page: 1,
          limit: 10,
          total: 50
        }
      }
    });
  });

  // CREATE record
  router.post("/api/v1/records/:table", [], async function (req, res) {
    // Mock response structure
    return res.json({
      status: 200,
      response: {
        model: {
          id: "new_id",
          // ... created record data
        }
      }
    });
  });

  // UPDATE record
  router.put("/api/v1/records/:table/:id?", [], async function (req, res) {
    // Mock response structure
    return res.json({
      status: 200,
      response: {
        model: {
          id: req.params.id,
          // ... updated record data
        }
      }
    });
  });

  // DELETE record
  router.delete("/api/v1/records/:table/:id?", [], async function (req, res) {
    // Mock response structure
    return res.json({
      status: 200,
      response: {
        success: true,
        message: "Record deleted successfully"
      }
    });
  });

  return router;
};
