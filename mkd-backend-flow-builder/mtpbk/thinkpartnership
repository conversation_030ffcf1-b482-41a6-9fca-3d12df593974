{"settings": {"globalKey": "key_thinkpartnership_marketplace", "databaseType": "mysql", "authType": "jwt", "timezone": "America/New_York", "dbHost": "localhost", "dbPort": "3306", "dbUser": "root", "dbPassword": "root", "dbName": "thinkpartnership_marketplace", "id": "project_thinkpartnership", "model_namespace": "thinkpartnership", "payment_option": "none", "isPWA": true}, "models": [], "routes": []}