# Global Application Key
GLOBAL_KEY=key_1731956956484_fs2brczmy

# JWT Configuration
JWT_KEY=key_1731956956484_fs2brczmy
REFRESH_JWT_EXPIRE=604800  # 7 days in seconds
ACCESS_JWT_EXPIRE=3600     # 1 hour in seconds
VERIFICATION_TOKEN_EXPIRE=86400  # 24 hours in seconds

# Application URL
APP_URL=http://localhost:5173

# Database Configuration
DB_TYPE=MySQL
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=root
DB_DATABASE=baas_db

# Mail Configuration
MAIL_HOST=smtp.zoho.com
MAIL_PORT=465
MAIL_USER=<EMAIL>
MAIL_PASS=dJwg7uw2uxQX
FROM_MAIL=<EMAIL>

# Authentication Configuration
SESSION=your-secret-key

# Server Configuration
PORT=5172
CORS_ORIGIN=*

# API Configuration
API_PREFIX=/api/v1
API_RATE_LIMIT_WINDOW_MS=900000  # 15 minutes in milliseconds
API_RATE_LIMIT_MAX=100  # limit each IP to 100 requests per windowMs

# Logging Configuration
LOG_LEVEL=info

# Stripe Configuration
STRIPE_SECRET_KEY=your_stripe_secret_key_here

# Twilio Configuration
TWILIO_ACCOUNT_SID=your_account_sid_here
TWILIO_AUTH_TOKEN=your_auth_token_here
TWILIO_PHONE_NUMBER=your_twilio_phone_number_here

# AWS Configuration
AWS_ACCESS_KEY_ID=your-access-key-id
AWS_SECRET_ACCESS_KEY=your-secret-access-key
AWS_BUCKET_NAME=your-bucket-name
AWS_REGION=your-region

# Basic Auth
BASIC_AUTH=your-basic-auth-key
BEARER_AUTH=your-bearer-auth-key
API_KEY=your-api-key
OAUTH2=your-oauth2-key
DIGEST=your-digest-key
HMAC=your-hmac-key
LDAP=your-ldap-key
SAML=your-saml-key
