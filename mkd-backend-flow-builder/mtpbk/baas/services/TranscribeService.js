const fs = require('fs');
const config = require('../../config');
const fetch = require('node-fetch');

const assembly = {
    upload: async () => {
        const url = 'https://api.assemblyai.com/v2/transcript';
        const audioUrl = config.argv[2];

        const data = {
            "audio_url": audioUrl
        };

        const params = {
            headers: {
                "authorization": config.ASSEMBLYAI_API_KEY,
                "content-type": "application/json",
            },
            body: JSON.stringify(data),
            method: "POST"
        };

        try {
            const response = await fetch(url, params);
            const result = await response.json();
            if (!response.ok) {
                throw new Error(`Error: ${result.error || 'Failed to upload audio'}`);
            }
            console.log('Success:', result);
            console.log('ID:', result.id);
        } catch (error) {
            console.error('Error:', error);
        }
    },

    download: async (id) => {
        const url = `https://api.assemblyai.com/v2/transcript/${id}`;
        const params = {
            headers: {
                "authorization": config.ASSEMBLYAI_API_KEY,
                "content-type": "application/json",
            },
            method: 'GET'
        };

        try {
            const response = await fetch(url, params);
            const data = await response.json();
            if (!response.ok) {
                throw new Error(`Error: ${data.error || 'Failed to fetch transcript'}`);
            }
            printTranscriptStatus(data);
        } catch (error) {
            console.error(`Error: ${error}`);
        }
    }
};

const printTranscriptStatus = (data) => {
    switch (data.status) {
        case 'queued':
        case 'processing':
            console.log('AssemblyAI is still transcribing your audio, please try again in a few minutes!');
            break;
        case 'completed':
            console.log(`Success: ${data}`);
            console.log(`Text: ${data.text}`);
            break;
        default:
            console.log(`Something went wrong :-( : ${data.status}`);
            break;
    }
};

module.exports = assembly;