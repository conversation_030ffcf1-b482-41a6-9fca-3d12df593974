const { sqlDateFormat, sqlDateTimeFormat, filterEmptyFields } = require("./UtilService");
const cronParser = require("cron-parser");
const fs = require("fs");


module.exports = class JobService {
  constructor(sdk) {
    this._sdk = sdk
    this.STATUS_PENDING = "pending"
    this.STATUS_FAILED = "failed"
    this.STATUS_PROCESSING = "processing"
    this.STATUS_COMPLETED = "completed"
  }

  async dispatch(task, task_arguments = {}, schedule = "once", retries = 1) {
    try {
      // Database driven
      this._sdk.setTable("job")
      let result = await this._sdk.insert(filterEmptyFields({
        task: task,
        arguments: JSON.stringify(task_arguments),
        time_interval: schedule,
        retries: retries,
        status: this.STATUS_PENDING,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date()),
      }))
      return {job_id: result.insertId };
    } catch (error) {
      throw error
    }
  }

  async runQueuedJobs() {
    // get pending scheduled jobs
    try {
      const dueJobs = await this.getDueJobs()

      for(const job of dueJobs) {
        await this.processJob(job)
      }

    } catch (error) {
      throw error
    }
  }



  async getDueJobs() {
    this._sdk.setTable("job")
    const queuedJobs = await this._sdk.get({status: this.STATUS_PENDING}, "*", "id", "ASC", )

    const dueJobs = [];
    
    for (const job of queuedJobs) {
      // Add one time jobs
      if (job.time_interval == "once" || job.last_run == null || job.last_run == "" ) {
        dueJobs.push(job);
        continue
      }

      //use node cron 
      if (this.isTaskDue(job.time_interval, job.last_run)) {
        dueJobs.push(job)
      }
    }

    return dueJobs;
  }

  async processJob(job) {
    // update status

    try {
      //run job
      let jobAction = `../custom/${this._sdk.getProjectId()}_backend/jobs/${job.task}`;
      let found = false
      try {
        // Check if the file exists
        // const exist = fs.existsSync(jobAction);
        // console.log("Job exist", exist);
        // File exists
        jobAction = require(jobAction);
        found = true
      } catch (error) {
        // File doesn't exist
        found = false
      }
      if(found){
        let jobArgs = JSON.parse(job.arguments);
        jobArgs["projectSdk"] = this._sdk;
  
        await jobAction(this._sdk, jobArgs)
  
        //clean
        await this.releaseJob(job);
      }

    } catch (error) {
      // mark failed
      await this.markJobAsFailed(job.id, error.message);
    }
  }

  async markJobAsFailed(jobId, error) {
    try {
      this._sdk.setTable("job")
      await this._sdk.update({"status": this.STATUS_FAILED, "error_log": error}, jobId)
    } catch (error) {
      throw error
    }
  }

  async releaseJob(job) {
    try {
      this._sdk.setTable("job")
      if (job.time_interval == null || job.time_interval == "once") {
        await this._sdk.delete({}, job.id)
        return;
      }
      await this._sdk.update({"last_run": sqlDateTimeFormat(new Date()), }, job.id)

    } catch (error) {
      throw error
    }
  }

  isTaskDue(cronSchedule, lastRunTime) {
    const options = {
        currentDate: lastRunTime,
        endDate: new Date() // Current time
    };

    const interval = cronParser.parseExpression(cronSchedule, options);
    return interval.hasNext() && interval.next().getTime() <= new Date().getTime();
  }



};

