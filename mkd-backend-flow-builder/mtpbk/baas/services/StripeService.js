const Stripe = require("stripe");
const config = require("../../config");
const { filterEmptyFields } = require("./UtilService");
const axios = require("axios");
const stripeSecret = config.stripe.secret_key;
/**
 * stripe service
 */
module.exports = class StripeService {
  stripe;
  constructor() {
    this.stripe = Stripe(stripeSecret);
  }
  getConfig() {
    return config?.stripe ?? {};
  }
  async createStripeCardToken(payload) {
    const { card_number, exp_month, exp_year, cvc } = payload;
    const token = await this.stripe.tokens.create({
      card: {
        number: card_number,
        exp_month: exp_month,
        exp_year: exp_year,
        cvc: cvc,
      },
    });
    return token;
  }

  async createStripePaymentMethod(payload) {
    const { type, details, billing_details } = payload;
    let obj = {
      type: type,
      [type]: details,
      billing_details: billing_details,
    };

    console.log("obj ", obj);
    const paymentMethod = await this.stripe.paymentMethods.create(obj);
    console.log("paymentMethod ", paymentMethod);
    return paymentMethod;
  }

  async createAndVerifyBankAccount(payload) {
    const { account_number, country, currency, customerId, routing_number } =
      payload;

    const object = "bank_account";
    const bankAccount = await this.stripe.customers.createSource(customerId, {
      source: {
        object: "bank_account",
        account_holder_name: "John Doe",
        account_number: account_number,
        country: country,
        currency: currency,
        routing_number: routing_number,
        account_holder_type: "individual",
      },
    });

    const verification = await this.stripe.customers.verifySource(
      customerId,
      bankAccount.id,
      {
        amounts: [32, 45],
      }
    );

    return bankAccount;
  }

  async detachStripePaymentMethod(payload) {
    const { paymentMethodId, customerId } = payload;

    let customer = await this.stripe.customers.retrieve(customerId);

    await this.stripe.paymentMethods.detach(paymentMethodId);

    let paymentMethods = await this.retrieveStripePaymentMethodAll({
      customerId,
    });

    if (customer.invoice_settings?.default_payment_method == paymentMethodId) {
      await this.stripe.customers.update(customerId, {
        invoice_settings: {
          default_payment_method:
            paymentMethods.length > 0 ? paymentMethods[0].id : null,
        },
      });
    }

    return;
  }

  async attachStripePaymentMethod(payload) {
    const { paymentMethodId, customerId } = payload;
    const paymentMethod = await this.stripe.paymentMethods.attach(
      paymentMethodId,
      {
        customer: customerId,
      }
    );
    return paymentMethod;
  }

  async retrieveStripePaymentMethod(payload) {
    const { paymentMethodId } = payload;
    const paymentMethod = await this.stripe.paymentMethods.retrieve(
      paymentMethodId
    );
    return paymentMethod;
  }

  async listPaymentMethodsConfiguration(payload) {
    const stripeApi = axios.create({
      baseURL: "https://api.stripe.com/v1",
      headers: {
        Authorization: `Bearer ${config.stripe.secret_key}`,
      },
    });

    // Make GET request to payment_method_configurations endpoint
    const response = await stripeApi.get("/payment_method_configurations");

    // Handle successful response
    console.log(response.data);
    return response.data;
  }

  async retrieveStripePaymentMethodAll(payload) {
    const { customerId } = payload;
    const paymentMethods = await this.stripe.customers.listPaymentMethods(
      customerId
    );
    return paymentMethods.data;
  }

  async createStripeCustomer(payload) {
    const { tokenId, email, metadata } = payload;
    const customer = await this.stripe.customers.create(
      filterEmptyFields({
        email: email,
        source: tokenId,
        metadata,
      })
    );
    return customer;
  }
  async createStripeCustomerWithCard(payload) {
    const { tokenId, email, metadata } = payload;
    const customer = await this.stripe.customers.create({
      email: email,
      source: tokenId,
      metadata,
    });

    return customer;
  }
  async setDefaultCard(payload) {
    const { customer_id, card_id } = payload;
    const card = await this.stripe.customers.update(customer_id, {
      default_source: card_id,
    });
    return card;
  }

  async setDefaultPaymentMethod(payload) {
    const { customer_id, paymentMethod_id } = payload;
    const card = await this.stripe.customers.update(customer_id, {
      invoice_settings: {
        default_payment_method: paymentMethod_id,
      },
    });
    return card;
  }

  async retrieveStripeCustomer(payload) {
    const { customerId } = payload;
    const customer = await this.stripe.customers.retrieve(customerId, {
      expand: ["sources"],
    });
    return customer;
  }
  async retrieveStripeCustomerCardDetails(payload) {
    const { customerId, cardId } = payload;
    const card = await this.stripe.customers.retrieveSource(customerId, cardId);
    return card;
  }
  async retrieveStripeCustomerAllCards(payload) {
    const { customerId, after, before, limit } = payload;
    const cards = await this.stripe.customers.listSources(
      customerId,
      filterEmptyFields({
        object: "card",
        limit,
        starting_after: after,
        ending_before: before,
        expand: ["data.customer"],
      })
    );

    return cards;
  }
  async addNewCardToStripeCustomer(payload) {
    const { tokenId, customerId, metadata } = payload;
    const card = await this.stripe.customers.createSource(customerId, {
      source: tokenId,
      metadata,
    });
    return card;
  }
  async stripeCharge(payload) {
    const { amount, currency, customer_id, token_id, description } = payload;
    const charge = await this.stripe.charges.create({
      amount: amount * 100,
      currency: currency,
      customer: customer_id,
      source: token_id,
      description: description ?? new Date().toISOString(),
    });
    return charge;
  }
  async retrieveStripeCharge(payload) {
    const { chargeId } = payload;
    const charge = await this.stripe.charges.retrieve(chargeId);
    return charge;
  }
  async retrieveStripeChargeAll(payload) {
    const { customer_id } = payload;
    const charges = await this.stripe.charges.list({
      customer: customer_id,
    });
    return charges;
  }
  async deleteStripeCustomerCard(payload) {
    const { customerId, cardId } = payload;
    const card = await this.stripe.customers.deleteSource(customerId, cardId);
    return card;
  }
  async deleteStripeCustomer(payload) {
    const { customerId } = payload;
    const customer = await this.stripe.customers.del(customerId);
    return customer;
  }
  async createStripeProduct(payload) {
    const {
      name,
      description,
      metadata,
      // active = true,
      // default_price_data,
      // images,
      // url,
    } = payload;
    const product = await this.stripe.products.create(
      filterEmptyFields({
        name,
        description,
        metadata,
      })
    );
    return product;
  }

  async retrieveStripeProducts(payload) {
    const { limit, ids, starting_after, ending_before } = payload;

    const products = await this.stripe.products.list(
      filterEmptyFields({ limit, ids, starting_after, ending_before })
    );
    return products;
  }

  async retrieveStripeProduct(payload) {
    const { product_id } = payload;
    const product = await this.stripe.products.retrieve(product_id);
    return product;
  }

  async deleteStripeProduct(payload) {
    const { productId } = payload;
    const product = await this.stripe.products.del(productId);
    return product;
  }

  async createStripeOnetimePrice(payload) {
    const { productId, name, amount, currency, metadata } = payload;
    const params = {
      product: productId,
      unit_amount: +amount * 100,
      nickname: name,
      currency: currency,
      metadata,
    };
    const price = await this.stripe.prices.create(filterEmptyFields(params));
    return price;
  }

  async createStripeRecurringPrice(payload) {
    const {
      productId,
      name,
      amount,
      currency,
      interval,
      interval_count = 1,
      trial_days,
      metadata,
    } = payload;
    const params = {
      product: productId,
      unit_amount: +amount * 100,
      nickname: name,
      currency: currency,
      recurring: {
        interval,
        interval_count: +interval_count,
        trial_period_days: +trial_days,
      },
      metadata,
    };
    if (interval === "lifetime") {
      delete params.recurring;
      params.metadata = {
        is_lifetime_subscription: "true",
      };
    }
    const price = await this.stripe.prices.create(filterEmptyFields(params));
    return price;
  }

  async createStripeRecurringMeteredPrice(payload) {
    const {
      productId,
      name,
      amount,
      currency,
      usage_limit,
      interval,
      interval_count = 1,
      trial_days,
      metadata,
    } = payload;

    const params = {
      product: productId,
      unit_amount: +amount * 100,
      nickname: name,
      currency: currency,
      recurring: {
        interval,
        interval_count: +interval_count,
        trial_period_days: +trial_days,
        usage_type: "metered",
        aggregate_usage: "sum",
      },
      metadata: { ...metadata, usage_limit: +usage_limit },
    };

    if (interval === "lifetime") {
      delete params.recurring;
      delete params.metadata.usage_limit;
      params.metadata.is_lifetime_subscription = "true";
    }
    const price = await this.stripe.prices.create(filterEmptyFields(params));
    return price;
  }

  async retrieveStripePrice(payload) {
    const { priceId } = payload;
    const price = await this.stripe.prices.retrieve(priceId);
    return price;
  }

  async updateStripeProduct(product_id, payload) {
    const { name, description, active } = payload;

    const product = await this.stripe.products.update(
      product_id,
      filterEmptyFields(
        {
          name,
          description,
          active,
        },
        true
      )
    );
    return product;
  }
  async updateStripePrice(payload) {
    const { price_id, name, status } = payload;
    const plan = await this.stripe.prices.update(
      price_id,
      filterEmptyFields(
        {
          nickname: name,
          active: status,
        },
        true
      )
    );
    return plan;
  }
  async deactivateStripePrice(payload) {
    const { price_id } = payload;
    return await this.stripe.prices.update(price_id, { active: false });
  }
  async retrieveStripePriceAll(payload) {
    const { product_id } = payload;
    const prices = await this.stripe.prices.list({
      product: product_id,
    });
    return prices;
  }
  async retrieveStripePrices(payload) {
    const { limit } = payload;
    const prices = await this.stripe.prices.list({
      limit,
    });
    return prices;
  }
  async searchProjectStripePrices(payload) {
    const { projectId, limit } = payload;
    const result = await this.stripe.prices.search({
      query: `active:\'true\' AND metadata[\'project_id\']:\'${projectId}\'`,
      limit: limit || 50,
      expand: ["data.product"],
    });

    return result;
  }
  async retrieveStripePaymentLinks() {
    const paymentLinks = await this.stripe.paymentLinks.list({
      limit: 100,
    });
    return paymentLinks;
  }
  async createStripeCoupon(payload) {
    const { name, percent_off, duration, duration_in_months, currency } =
      payload;
    const coupon = await this.stripe.coupons.create({
      name: name,
      percent_off: percent_off,
      duration: duration,
      duration_in_months: duration_in_months,
      currency: currency,
    });
    return coupon;
  }
  async createStripeCouponCustom(payload) {
    const {
      name,
      percent_off,
      duration,
      amount_off,
      duration_in_months,
      currency,
    } = payload;

    if (amount_off) {
      const coupon = await this.stripe.coupons.create({
        name: name,
        amount_off: amount_off,
        duration: duration,
        duration_in_months: duration_in_months,
        currency: currency,
      });
      return coupon;
    }

    const coupon = await this.stripe.coupons.create({
      name: name,
      percent_off: percent_off,
      duration: duration,
      duration_in_months: duration_in_months,
      currency: currency,
    });
    return coupon;
  }
  async retrieveStripeCoupon(payload) {
    const { couponId } = payload;
    const coupon = await this.stripe.coupons.retrieve(couponId);
    return coupon;
  }
  async deleteStripeCoupon(payload) {
    const { couponId } = payload;
    const coupon = await this.stripe.coupons.delete(couponId);
    return coupon;
  }
  async retrieveStripeCouponAll(payload) {
    const coupons = await this.stripe.coupons.list();
    return coupons;
  }
  async createStripeSubscription(payload) {
    const {
      customerId,
      priceId,
      coupon = null,
      default_payment_method,
      trial_from_plan = false,
      metadata,
    } = payload;
    console.log("coupon is ", coupon);
    const subscription = await this.stripe.subscriptions.create(
      filterEmptyFields({
        customer: customerId,
        items: [{ price: priceId }],
        coupon: coupon,
        payment_behavior: "error_if_incomplete",
        default_payment_method,
        trial_from_plan,
        metadata,
      })
    );
    return subscription;
  }
  async reactivateStripeSubscription(payload) {
    const { subscriptionId } = payload;
    const subscription = await this.stripe.subscriptions.update(
      subscriptionId,
      {
        cancel_at_period_end: false,
        proration_behavior: "create_prorations",
        collection_method: "charge_automatically",
      }
    );
    return subscription;
  }
  async changeStripeSubscriptionPlan(payload) {
    const { subscriptionId, subItemId, newPriceId } = payload;
    const updatedSubscription = await this.stripe.subscriptions.update(
      subscriptionId,
      {
        items: [
          {
            id: subItemId,
            price: newPriceId,
          },
        ],
      }
    );
    return updatedSubscription;
  }
  async updateStripeSubscription(payload) {
    const { subscriptionId, priceId, proration } = payload;
    const subscription = await this.stripe.subscriptions.retrieve(
      subscriptionId
    );
    const updatedSubscription = await this.stripe.subscriptions.update(
      subscriptionId,
      {
        cancel_at_period_end: true,
        proration_behavior: proration ? proration : "create_prorations",
        items: [
          {
            id: subscription.items.data[0].id,
            price: priceId,
          },
        ],
      }
    );
    return updatedSubscription;
  }
  async updateStripeSubscriptions(payload) {
    const { subscriptionId, stripe_coupon_id, proration } = payload;
    // const subscription = await this.stripe.subscriptions.retrieve(subscriptionId);
    const updatedSubscription = await this.stripe.subscriptions.update(
      subscriptionId,
      {
        coupon: stripe_coupon_id,
      }
    );
    return updatedSubscription;
  }
  async updateStripeSubscriptionAnchor(payload) {
    const { subscriptionId, priceId, proration, stripe_coupon_id } = payload;
    const subscription = await this.stripe.subscriptions.retrieve(
      subscriptionId
    );
    const updatedSubscription = await this.stripe.subscriptions.update(
      subscriptionId,
      {
        cancel_at_period_end: false,
        proration_behavior: "create_prorations",
        billing_cycle_anchor: "now",
        coupon: stripe_coupon_id,
        items: [
          {
            id: subscription.items.data[0].id,
            price: priceId,
          },
        ],
      }
    );
    return updatedSubscription;
  }
  async updateStripeSubscriptionAnchorTest(payload) {
    const { subscriptionId, priceId, proration, stripe_coupon_id } = payload;
    const subscription = await this.stripe.subscriptions.retrieve(
      subscriptionId
    );
    const updatedSubscription = await this.stripe.subscriptions.update(
      subscriptionId,
      {
        cancel_at_period_end: false,
        proration_behavior: "always_invoice",
        billing_cycle_anchor: "now",
        coupon: stripe_coupon_id,
        items: [
          {
            id: subscription.items.data[0].id,
            price: priceId,
          },
        ],
      }
    );
    return updatedSubscription;
  }

  async createUsageCharge(payload) {
    const { subItemId, quantity } = payload;
    const usageRecord = await this.stripe.subscriptionItems.createUsageRecord(
      subItemId,
      { quantity: quantity, timestamp: "now" }
    );
    return usageRecord;
  }
  async updateSubscriptionItem(payload) {
    const { id, params } = payload;
    const usageRecord = await this.stripe.subscriptionItems.update(id, params);
    return usageRecord;
  }

  async cancelStripeSubscription(payload) {
    const { subscriptionId } = payload;
    const subscription = await this.stripe.subscriptions.del(subscriptionId);
    return subscription;
  }

  async cancelStripeSubscriptionAtPeriodEnd(payload) {
    const {
      subscriptionId,
      days = null,
      collection_method = "send_invoice",
    } = payload;
    const subscription = await this.stripe.subscriptions.update(
      subscriptionId,
      filterEmptyFields({
        cancel_at_period_end: true,
        collection_method: collection_method,
        days_until_due: days,
      })
    );
    return subscription;
  }

  async listStripeSubscription(payload) {
    const subscriptions = await this.stripe.subscriptions.list(payload);
    return subscriptions;
  }
  async retrieveStripeSubscription(payload) {
    const { subscriptionId } = payload;
    const subscription = await this.stripe.subscriptions.retrieve(
      subscriptionId
    );
    return subscription;
  }
  async createStripeRefund(payload) {
    const { charge_id, amount, reason, metadata } = payload;
    const refund = await this.stripe.refunds.create({
      charge: charge_id,
      amount: Number(amount) * 100,
      reason: reason ? reason : null,
      metadata,
    });
    return refund;
  }
  async retrieveStripeRefund(payload) {
    const { refund_id } = payload;
    const refund = await this.stripe.refunds.retrieve(refund_id);
    return refund;
  }
  async cancelStripeRefund(payload) {
    const { refund_id } = payload;
    const refund = await this.stripe.refunds.cancel(refund_id);
    return refund;
  }
  async retrieveStripeRefundAll(payload) {
    const { chargeId } = payload;
    const refunds = await this.stripe.refunds.list({
      charge: chargeId,
    });
    return refunds;
  }
  async createStripeDispute(payload) {
    const { amount, reason, reason_description, metadata } = payload;
    const dispute = await this.stripe.issuing.disputes.create({
      amount: Number(amount) * 100,
      evidence: {
        reason: reason,
        [reason]: {
          explanation: reason_description.explanation ?? null,
        },
      },
      metadata,
    });
    return dispute;
  }
  async submitStripeDispute(payload) {
    const { dispute_id } = payload;
    const dispute = await this.stripe.issuing.disputes.submit(dispute_id);
    return dispute;
  }
  async retrieveStripeDispute(payload) {
    const { dispute_id } = payload;
    const dispute = await this.stripe.disputes.retrieve(dispute_id);
    return dispute;
  }
  async updateStripeDispute(payload) {
    const { dispute_id, reason, reason_description } = payload;
    const dispute = await this.stripe.issuing.disputes.update(dispute_id, {
      evidence: {
        reason: reason,
        [reason]: {
          expected_at: reason_description.expected_at ?? null,
          explanation: reason_description.explanation ?? null,
          product_description: reason_description.product_description ?? null,
          product_type: reason_description.product_type ?? null,
        },
      },
    });
    return dispute;
  }
  async closeStripeDispute(payload) {
    const { dispute_id } = payload;
    const dispute = await this.stripe.disputes.close(dispute_id);
    return dispute;
  }

  async retrieveCustomerInvoices(payload) {
    const { customerId, limit, after, before } = payload;
    const invoices = await this.stripe.invoices.list(
      filterEmptyFields({
        customer: customerId,
        limit,
        starting_after: after,
        ending_before: before,
      })
    );
    return invoices;
  }
  // async updateInvoice(invoiceId, metadata) {
  //   const invoices = await this.stripe.invoices.update(invoiceId, { metadata });
  //   return invoices;
  // }
  async createPaymentIntentAutomatic(payload) {
    const paymentIntent = await this.stripe.paymentIntents.create(
      filterEmptyFields({
        amount: payload.amount,
        currency: payload.currency ? payload.currency : "usd",
        customer: payload.customer,
        automatic_payment_methods: { enabled: true },
      })
    );
    return paymentIntent;
  }
  async createPaymentIntentManual(payload) {
    const paymentIntent = await this.stripe.paymentIntents.create({
      amount: payload.amount,
      currency: payload.currency ? payload.currency : "usd",
      payment_method_types: payload.payment_method_types,
    });
    return paymentIntent;
  }

  async updatePaymentIntent(payload) {
    const paymentIntentId = payload.paymentIntentId;
    delete payload.paymentIntentId;
    const updatedIntent = await this.stripe.paymentIntents.update(
      paymentIntentId,
      ...payload
    );
    return updatedIntent;
  }
  async retrievePaymentIntent(payload) {
    const paymentIntentId = payload;
    const intents = await this.stripe.paymentIntents.retrieve(paymentIntentId);
    return intents;
  }

  async listShippingMethods(payload = {}) {
    const shippingRates = await this.stripe.shippingRates.list({
      limit: payload.limit ? payload.limit : 3,
    });
    return shippingRates;
  }
  async createShippingMethod(payload) {
    const shippingRate = await this.stripe.shippingRates.create({
      display_name: payload.display_name,
      type: payload.type ? payload.type : "fixed_amount",
      fixed_amount: {
        amount: payload.amount,
        currency: payload.currency ? payload.currency : "usd",
      },
    });
    return shippingRate;
  }
  async deactivateShippingMethod(payload) {
    const shippingRate = await this.stripe.shippingRates.update(
      payload.shippingRateId,
      {
        active: false,
      }
    );
    return shippingRate;
  }
  async updateShippingMethod(payload) {
    const shippingRate = await this.stripe.shippingRates.update(payload);
    return shippingRate;
  }

  async retrieveCustomerCharges(payload) {
    const { customerId, limit, after, before } = payload;
    const invoices = await this.stripe.charges.list(
      filterEmptyFields({
        customer: customerId,
        limit,
        starting_after: after,
        ending_before: before,
      })
    );
    return invoices;
  }

  async createWebhookEndpoint(payload) {
    const { url, events } = payload;
    const webhookEndpoint = await this.stripe.webhookEndpoints.create({
      url: url,
      enabled_events: events,
    });
    return webhookEndpoint;
  }

  async updateWebhookEndpoint(payload) {
    const { url, events, metadata, id } = payload;
    const webhookEndpoint = await this.stripe.webhookEndpoints.update(id, {
      url: url,
      enabled_events: events,
      metadata,
    });
    return webhookEndpoint;
  }

  async deleteWebhookEndpoint(payload) {
    const { id } = payload;
    const webhookEndpoint = await this.stripe.webhookEndpoints.del(id);
    return webhookEndpoint;
  }

  async getAllWebhooks() {
    const webhooks = [];
    for await (const webhook of this.stripe.webhookEndpoints.list({
      limit: 100,
    })) {
      webhooks.push(webhook);
    }
    return webhooks;
  }
  async createCheckoutSession(payload) {
    // const { success_url, mode, cancel_url, customer, customer_email, payment_method_types, payment_intent_data, metadata, line_items } = payload;
    const checkout = await this.stripe.checkout.sessions.create(
      filterEmptyFields(payload)
    );
    return checkout;
  }

  async createCustomerEphemeralKey(payload) {
    try {
      const ephemeralKey = await this.stripe.ephemeralKeys.create(
        { customer: payload.customer },
        { apiVersion: payload.apiVersion ? payload.apiVersion : "2023-10-16" }
      );
      return ephemeralKey;
    } catch (error) {
      console.log("error :>> ", error);
      throw error;
    }
  }
};
