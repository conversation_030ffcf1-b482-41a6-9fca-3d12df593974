const puppeteer = require("puppeteer")
const eta = require("eta");

module.exports = class ExcelService {
  async DBtoExcel(pdf_path = "example.xlsx", payload, eta_path, format = 'A4',) {
    const browser = await puppeteer.launch({ headless: true, args: ['--no-sandbox'] });
    const page = await browser.newPage();
    await page.setContent(eta
      .
      renderFile(eta_path, {
        payload
      }));
    await page.pdf({ path: pdf_path, format });
    await browser.close();
  }

  async DownloadtoExcel(path = "example.xlsx", url = 'https:example.com', format = 'A4') {
    const browser = await puppeteer.launch({ headless: true, args: ['--no-sandbox'] });
    const page = await browser.newPage();
    await page.goto(url);
    await page.pdf({ path, format });
    await browser.close();
  }
}