const nodemailer = require("nodemailer");

class MailService {
  constructor(config) {
    this.transporter = nodemailer.createTransport({
      host: config.mail.mail_host,
      port: config.mail.mail_port,
      auth: {
        user: config.mail.mail_user,
        pass: config.mail.mail_pass
      }
    });
    this.from = config.mail.from_mail;
  }

  /**
   * Get email template from database
   * @name mailService.template
   * @param {String} slug email template slug
   * @reject {Error}
   * @returns {Promise.<{error: Boolean, data: any, message: string}>} email template
   */
  async template(slug, sdk) {
    try {
      sdk.getDatabase();
      // sdk.setProjectId(req.projectId);
      sdk.setTable("email");
      const result = await sdk.find("email", {
        slug: slug
      });
      if (typeof result == "string") {
        return {
          error: true,
          data: result
        };
      }
      return {
        error: false,
        data: result[0]
      };
    } catch (error) {
      console.log("Template Error", error);
      return {
        error: true,
        message: error.message
      };
    }
  }
  /**
   * Inject values into email template
   * @name mailService.inject
   * @param {{body: String, subject: String}} template email template
   * @param {Object.<string, string>} payload template values
   * @returns {{from: String, subject: String, html: String}}  Value injected email template
   */
  inject(template, payload) {
    let mailBody = template.body;
    let mailSubject = template.subject;

    for (const key in payload) {
      const value = payload[key];
      mailBody = mailBody.replace(new RegExp("{{{" + key + "}}}", "g"), value);
    }

    for (const key in payload) {
      const value = payload[key];
      mailSubject = mailSubject.replace(
        new RegExp("{{{" + key + "}}}", "g"),
        value
      );
    }

    return {
      from: this.from,
      subject: mailSubject,
      html: mailBody
    };
  }
  /**
   * Send email
   * @name mailService.send
   * @param {nodemailer.SendMailOptions} template email template
   * @reject {Error} send mail error
   * @returns {Promise.<nodemailer.SentMessageInfo>} send mail info
   */
  async send(from, to, subject, html, attachments = null) {
    let self = this;
    try {
      const mailOptions = {
        from,
        to,
        subject,
        html,
        attachDataUrls: true
      };

      if (attachments) {
        mailOptions.attachments = attachments;
      }

      const response = await self.transporter.sendMail(mailOptions);
      return {
        error: false,
        message: response
      };
    } catch (error) {
      return {
        error: true,
        message: error.message
      };
    }
  }
  // Sends a password reset email to the specified recipient
  async sendPasswordResetEmail(to, resetCode, token) {
    const template = this.getPasswordResetTemplate(resetCode);

    await this.transporter.sendMail({
      from: this.from,
      to: to,
      subject: "Password Reset Request",
      html: template
    });
  }

  // Sends a magic link email to the specified recipient
  async sendMagicLinkEmail(to, magicLink) {
    const template = this.getMagicLinkTemplate(magicLink);

    await this.transporter.sendMail({
      from: this.from,
      to: to,
      subject: "Magic Link Login",
      html: template
    });
  }

  // Sends a verification email to the specified recipient
  async sendVerificationEmail(to, verificationToken, verificationUrl) {
    const template = this.getVerificationEmailTemplate(
      verificationToken,
      verificationUrl
    );

    await this.transporter.sendMail({
      from: this.from,
      to: to,
      subject: "Verify Your Email Address",
      html: template
    });
  }

  // Generates the HTML template for the password reset email
  getPasswordResetTemplate(resetCode) {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Password Reset Request</h2>
        <p>You have requested to reset your password. Please use the following code to reset your password:</p>
        <div style="background: #f4f4f4; padding: 15px; text-align: center; font-size: 24px; letter-spacing: 5px;">
          <strong>${resetCode}</strong>
        </div>
        <p>This code will expire in 24 hours.</p>
        <p>If you did not request this password reset, please ignore this email.</p>
      </div>
    `;
  }

  // Generates the HTML template for the magic link email
  getMagicLinkTemplate(magicLink) {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Magic Link Login</h2>
        <p>Click the button below to log in to your account:</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${magicLink}" 
             style="background: #007bff; color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; font-weight: bold;">
            Login to Your Account
          </a>
        </div>
        <p>Or copy and paste this link in your browser:</p>
        <p style="background: #f4f4f4; padding: 15px; word-break: break-all;">
          ${magicLink}
        </p>
        <p>This link will expire in 24 hours.</p>
        <p>If you did not request this login link, please ignore this email.</p>
      </div>
    `;
  }

  // Generates the HTML template for the verification email
  getVerificationEmailTemplate(token, verificationUrl) {
    const fullVerificationUrl = `${verificationUrl}?token=${token}`;

    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Email Verification</h2>
        <p>Thank you for registering! Please verify your email address by clicking the button below:</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${fullVerificationUrl}" 
             style="background: #007bff; color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; font-weight: bold;">
            Verify Email Address
          </a>
        </div>
        <p>Or copy and paste this link in your browser:</p>
        <p style="background: #f4f4f4; padding: 15px; word-break: break-all;">
          ${fullVerificationUrl}
        </p>
        <p>This verification link will expire in 24 hours.</p>
        <p>If you did not create an account, please ignore this email.</p>
      </div>
    `;
  }
}

module.exports = MailService;
