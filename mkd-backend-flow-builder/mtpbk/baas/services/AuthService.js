const PasswordService = require("./PasswordService");
const { sqlDateFormat, sqlDateTimeFormat } = require("./UtilService");

class AuthService {
  // Basic authentication method
  static basic(app, req) {
    const config = app.get("configuration");
    
    // Get Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Basic ')) {
      return false;
    }

    // Extract credentials
    try {
      // Basic Auth header format: "Basic base64(username:password)"
      const base64Credentials = authHeader.split(' ')[1];
      const credentials = Buffer.from(base64Credentials, 'base64').toString('utf8');
      const [username, password] = credentials.split(':');

      // Compare with configured basic auth key
      // Expected format in config: "username:password"
      const [configUsername, configPassword] = config.basic_auth.split(':');
      
      return username === configUsername && password === configPassword;
    } catch (error) {
      console.error('Basic auth error:', error);
      return false;
    }
  }

  
  async register(sdk, projectId, email, password, role, verify = 0, first_name = "", last_name = "", photo = null, phone = null) {
    sdk.setProjectId(projectId);
    sdk.setTable("user");
    try {
      const exist = await sdk.findOne("user", {
        email: email
      });

      if (exist) {
        throw new Error("User exists");
      } else {
        const hashPassword = await PasswordService.hash(password);
        const result = await sdk.create("user", {
          email: email,
          password: hashPassword,
          role_id: role,
          verify: verify,
          status: 1,
          login_type: 0,
          data: JSON.stringify({ first_name, last_name, photo, phone }),
          created_at: sqlDateFormat(new Date()),
          updated_at: sqlDateTimeFormat(new Date())
        });


        await sdk.create("preference", {
          user_id: result.id,
          first_name: first_name,
          last_name: last_name,
          photo: photo,
          phone: phone,
          created_at: sqlDateFormat(new Date()),
          updated_at: sqlDateTimeFormat(new Date())
        });

        return result;
      }
    } catch (error) {
      return error.message;
    }
  }

  async login(app, projectId, email, password, role) {
    const sdk = app.get("sdk");
    sdk.getDatabase();
    sdk.setProjectId(projectId);

    try {
      const exist = await sdk.findOne("user", {
        email: email,
        role_id: role
      });

      if (exist) {
        const hashPassword = exist.password;
        const passwordValid = await PasswordService.compareHash(password, hashPassword);

        if (!passwordValid) {
          throw new Error("Invalid Password");
        }

        return exist;
      } else {
        throw new Error("User does not exist");
      }
    } catch (error) {
      console.log("error", error);
      return error.message;
    }
  }

  async googleLogin(sdk, projectId, user, tokens, role, company_id) {
    sdk.setProjectId(projectId);

    try {
      const exist = await sdk.findOne("user",{
        email: user.email
      });

      if (exist && exist.login_type != 1) {
        throw new Error("User already registered without google login!");
      }

      if (exist) {
        if (company_id && exist.company_id != company_id) {
          throw new Error("This account is not associated with this company");
        }
        return exist.id;
      } else {
        const result = await sdk.create("user",{
          login_type: 1,
          email: user.email ? user.email : "na",
          password: " ",
          role_id: role,
          data: JSON.stringify({ first_name: user.given_name, last_name: user.family_name }),
          verify: 1,
          status: 1,
          created_at: sqlDateFormat(new Date()),
          updated_at: sqlDateTimeFormat(new Date()),
          company_id: company_id ?? 0
        });

        

        await sdk.create("preference", {
          user_id: result.id,
          first_name: user.given_name,
          last_name: user.family_name,
          created_at: sqlDateFormat(new Date()),
          updated_at: sqlDateTimeFormat(new Date())
        });

        await sdk.create("tokens",{
          user_id: result.id,
          token: tokens.access_token,
          type: 1,
          data: JSON.stringify({ user, tokens }),
          status: 1,
          created_at: sqlDateFormat(new Date()),
          updated_at: sqlDateTimeFormat(new Date()),
          expired_at: sqlDateTimeFormat(new Date(tokens.expiry_date))
        });

        return result.id;
      }
    } catch (error) {
      return error.message;
    }
  }

  async appleLogin(sdk, projectId, user, tokens, role, company_id) {
    sdk.setProjectId(projectId);

    const exist = await sdk.findOne("user", {
      email: user.email
    });

    if (exist.length && exist.login_type != 3) {
      throw new Error("User already registered without apple login!");
    }

    if (exist.length > 0) {
      if (exist.role_id != role) throw new Error("Unauthorized");
      if (company_id && exist.company_id !== company_id) {
        throw new Error("This account is not associated with this company");
      }
      return { id: exist.id, is_newuser: false };
    } else {
      const result = await sdk.create("user",{
        login_type: 3,
        email: user.email,
        password: " ",
        role_id: role,
        data: JSON.stringify({ first_name: user.first_name, last_name: user.last_name }),
        verify: 1,
        status: 1,
        created_at: sqlDateFormat(new Date()),
        company_id: company_id ?? 0,
        updated_at: sqlDateTimeFormat(new Date())
      });

      await sdk.create("preference", {
        user_id: result.id,
        first_name: user.first_name,
        last_name: user.last_name,
        created_at: sqlDateFormat(new Date()),
        updated_at: sqlDateTimeFormat(new Date())
      });

      return { id: result.id, is_newuser: true };
    }
  }
  async twitterLogin(sdk, projectId, user, role, company_id) {
    sdk.setProjectId(projectId);

    const exist = await sdk.findOne("user", {
      email: user.email
    });

    if (exist && exist.login_type != 4) {
      throw new Error("User already registered without twitter login!");
    }

    if (exist) {
      if (exist.role_id != role) throw new Error("Unauthorized");
      return exist.id;
    } else {
      const result = await sdk.create("user",{
        login_type: 4,
        email: user.email,
        password: " ",
        role_id: role,
        data: JSON.stringify({ first_name: user.first_name, last_name: user.last_name }),
        verify: 1,
        status: 1,
        company_id: company_id ?? 0,
        created_at: sqlDateFormat(new Date()),
        updated_at: sqlDateTimeFormat(new Date())
      });

      await sdk.create("preference", {
        user_id: result.id,
        first_name: user.first_name,
        last_name: user.last_name,
        created_at: sqlDateFormat(new Date()),
        updated_at: sqlDateTimeFormat(new Date())
      });

      return result.id;
    }
  }

  async facebookLogin(sdk, projectId, user, tokens, role, company_id) {
    sdk.getDatabase();
    sdk.setProjectId(projectId);

    try {
      const exist = await sdk.findOne("user", {
        email: user.email ?? user.id
      });

      if (exist && exist.login_type != 5) {
        throw new Error("User already registered without facebook login!");
      }

      if (exist) {
        if (company_id && exist.company_id !== company_id) {
          throw new Error("This account is not associated with this company");
        }
        return exist.id;
      } else {
        const result = await sdk.create("user ", {
          login_type: 5,
          email: user.email ?? user.id,
          password: " ",
          role_id: role,
          data: JSON.stringify({ first_name: user.first_name, last_name: user.last_name }),
          verify: 1,
          status: 1,
          company_id: company_id ?? 0,
          created_at: sqlDateFormat(new Date()),
          updated_at: sqlDateTimeFormat(new Date())
        });


        await sdk.create("preference", {
          user_id: result.id,
          first_name: user.first_name,
          last_name: user.last_name,
          created_at: sqlDateFormat(new Date()),
          updated_at: sqlDateTimeFormat(new Date())
        });


        await sdk.create("tokens", {
          user_id: result.id,
          token: tokens.access_token,
          type: 1,
          data: JSON.stringify({ user, tokens }),
          status: 1,
          created_at: sqlDateFormat(new Date()),
          updated_at: sqlDateTimeFormat(new Date()),
          expired_at: sqlDateTimeFormat(new Date(new Date().setSeconds(tokens.expires_in)))
        });
        return result.id;
      }
    } catch (error) {
      return error.message;
    }
  }


  async forgot(sdk, projectId, email, userId) {
    sdk.setProjectId(projectId);
    sdk.setTable("tokens");

    try {
      let tomorrow = new Date();
      tomorrow.setHours(new Date().getHours() + 24);
      const code = Math.floor(Math.random() * 1000000);
      const token = JwtService.generateString(50);
      const payload = {
        token: token,
        type: 0,
        data: '{"email": "' + email + '", "tokens": "' + token + '", "code": "' + code + '"}',
        user_id: userId,
        status: 1,
        created_at: sqlDateFormat(new Date()),
        updated_at: sqlDateTimeFormat(new Date()),
        expired_at: sqlDateTimeFormat(tomorrow)
      };
      const result = await sdk.create("tokens", payload);
      return { ...payload, code, token };
    } catch (error) {
      return error.message;
    }
  }

  async mobileForgot(sdk, projectId, email, userId) {
    sdk.getDatabase();
    sdk.setProjectId(projectId);
    sdk.setTable("tokens");

    try {
      let tomorrow = new Date();
      tomorrow.setHours(new Date().getHours() + 1);
      const code = Math.floor(Math.random() * 1000000);
      const token = JwtService.generateString(50);
      const payload = {
        token: code,
        type: 0,
        data: '{"email": "' + email + '", "tokens": "' + token + '", "code": "' + code + '"}',
        user_id: userId,
        status: 1,
        created_at: sqlDateFormat(new Date()),
        updated_at: sqlDateTimeFormat(new Date()),
        expired_at: sqlDateTimeFormat(tomorrow)
      };
      const result = await sdk.create("tokens", payload);
      return { ...payload, code, token: code };
    } catch (error) {
      return error.message;
    }
  }

  async mobileVerify(sdk, projectId, email) {
    sdk.getDatabase();
    sdk.setProjectId(projectId);
    sdk.setTable("tokens");

    try {
      let tomorrow = new Date();
      tomorrow.setHours(new Date().getHours() + 1);
      const code = Math.floor(Math.random() * 1000000);
      const payload = {
        token: code,
        type: 5,
        data: '{"email": "' + email + '", "code": "' + code + '"}',
        user_id: 0,
        status: 1,
        created_at: sqlDateFormat(new Date()),
        updated_at: sqlDateTimeFormat(new Date()),
        expired_at: sqlDateTimeFormat(tomorrow)
      };
      const result = await sdk.create("tokens", payload);

      return { ...payload, code, token: code };
    } catch (error) {
      return error.message;
    }
  }

  async reset(sdk, projectId, token, password) {
    sdk.getDatabase();
    sdk.setTable("user");
    sdk.setProjectId(projectId);

    try {
      const exist = await sdk.findOne("user",{
        id: token.user_id
      });

      if (exist) {
        const hashPassword = await PasswordService.hash(password);
        await sdk.update("user",
          {id:exist.id},
          {
            password: hashPassword,
            updated_at: sqlDateTimeFormat(new Date())
          },
        );

        if (exist.id) {
          await sdk.delete("tokens",{id:token.id});
          return exist.id;
        } else {
          throw new Error("Password Update Failed");
        }
      } else {
        throw new Error("Invalid User");
      }
    } catch (error) {
      console.log("error", error);
      return error.message;
    }
  }

  async saveRefreshToken(sdk, projectId, user_id, token, expireDate) {
    sdk.getDatabase();
    sdk.setProjectId(projectId);

    try {
      sdk.setTable("tokens");
      const result = await sdk.create("tokens", {
        user_id: user_id,
        token: token,
        type: 2,
        data: JSON.stringify({ user_id, token }),
        status: 1,
        created_at: sqlDateFormat(new Date()),
        updated_at: sqlDateTimeFormat(new Date()),
        expired_at: sqlDateTimeFormat(expireDate)
      });
      return result;
    } catch (error) {
      return error.message;
    }
  }

  async checkRefreshToken(sdk, projectId, userId, token) {
    sdk.getDatabase();
    sdk.setProjectId(projectId);

    try {
      const exist = await sdk.findOne("tokens",{
        user_id: userId,
        token: token,
        type: 2,
        status: 1
      });

      if (exist) {
        return exist;
      } else {
        throw new Error("Invalid Token");
      }
    } catch (error) {
      return error.message;
    }
  }

  async updateRefreshToken(sdk, projectId, oldToken, newToken, expireDate) {
    sdk.getDatabase();
    sdk.setProjectId(projectId);
    sdk.setTable("tokens");

    try {
      const exist = await sdk.findOne("tokens",{
        token: oldToken,
        type: 2,
        status: 1
      });

      if (exist) {
        // change old token status to 0
        await sdk.update("tokens",
          {
            id: exist.id
          },
          {
            status: 0,
            updated_at: sqlDateTimeFormat(new Date())
          },
        );

        // insert new token
        const result = await sdk.insert("tokens",
          {
            user_id: exist.user_id,
            token: newToken,
            type: 2,
            data: JSON.stringify({ user_id: exist.user_id, token: newToken }),
            status: 1,
            created_at: sqlDateFormat(new Date()),
            updated_at: sqlDateTimeFormat(new Date()),
            expired_at: sqlDateTimeFormat(expireDate)
          }
        );

        return result;
      } else {
        throw new Error("Invalid Token");
      }
    } catch (error) {
      return error.message;
    }
  }


  // Bearer token authentication method
  static bearer(app, req) {
    const config = app.get("configuration");
    
    // Get Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return false;
    }

    // Extract and validate token
    try {
      const token = authHeader.split(' ')[1];
      
      // Use JwtService to verify the token
      const result = JwtService.verifyAccessToken(token, config.jwt_key);
      
      if (!result) {
        return false;
      }

      req.user_id = result.user_id;
      req.role = result.role;

      return true;
    } catch (error) {
      console.error('Bearer auth error:', error);
      return false;
    }
  }

  // API Key authentication method
  static apiKey(app, req) {
    const config = app.get("configuration");
    
    // Check for API key in various common locations
    const apiKey = 
      req.headers['x-api-key'] || 
      req.headers['api-key'] || 
      req.query.api_key;

    if (!apiKey) {
      return false;
    }

    return apiKey === config.api_key;
  }

  // OAuth2 authentication method
  static oauth2(app, req) {
    const config = app.get("configuration");
    
    // Get Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('OAuth ')) {
      return false;
    }

    try {
      const token = authHeader.split(' ')[1];
      return token === config.oauth2;
    } catch (error) {
      console.error('OAuth2 auth error:', error);
      return false;
    }
  }

  // Digest authentication method
  static digest(app, req) {
    const config = app.get("configuration");
    
    // Get Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Digest ')) {
      return false;
    }

    try {
      const token = authHeader.split(' ')[1];
      return token === config.digest;
    } catch (error) {
      console.error('Digest auth error:', error);
      return false;
    }
  }

  // HMAC authentication method
  static hmac(app, req) {
    const config = app.get("configuration");
    
    // Get Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('HMAC ')) {
      return false;
    }

    try {
      const token = authHeader.split(' ')[1];
      const hmacKey = config.hmac; // Assuming hmac is the secret key
      const computedHmac = require('crypto')
        .createHmac('sha256', hmacKey)
        .update(req.body) // Use the raw body of the request for HMAC calculation
        .digest('hex');

      return token === computedHmac;
    } catch (error) {
      console.error('HMAC auth error:', error);
      return false;
    }
  }

  // Session authentication method
  static session(app, req) {
    const config = app.get("configuration");
    
    // Check session token in cookie
    const sessionToken = req.cookies?.session;
    if (!sessionToken) {
      return false;
    }

    try {
      // Validate the session token against the stored session data
      const isValidSession = validateSessionToken(sessionToken, config.session);
      return isValidSession;
    } catch (error) {
      console.error('Session auth error:', error);
      return false;
    }
  }

  // Helper function to validate session token
  static validateSessionToken(token, expectedSession) {
    // Here you can implement the logic to validate the session token
    // For example, check if the token matches the expected session or
    // if it exists in a session store (like Redis or a database)
    return token === expectedSession; // Simplified for demonstration
  }

  // LDAP authentication method
  static ldap(app, req) {
    const config = app.get("configuration");
    
    // Get Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('LDAP ')) {
      return false;
    }

    try {
      const token = authHeader.split(' ')[1];
      return token === config.ldap;
      // Here you would typically validate the token against an LDAP server
      // For demonstration, we will assume a simple comparison
      // const ldapClient = require('ldapjs').createClient({
      //   url: config.ldap.url // Assuming the LDAP URL is stored in the config
      // });

      // return new Promise((resolve, reject) => {
      //   ldapClient.bind(config.ldap.bindDN, config.ldap.bindCredentials, (err) => {
      //     if (err) {
      //       console.error('LDAP bind error:', err);
      //       return resolve(false);
      //     }

      //     // Perform a search to validate the token
      //     ldapClient.search(config.ldap.baseDN, {
      //       filter: `(uid=${token})`, // Assuming the token is the user's UID
      //       scope: 'sub'
      //     }, (err, search) => {
      //       if (err) {
      //         console.error('LDAP search error:', err);
      //         return resolve(false);
      //       }

      //       search.on('searchEntry', (entry) => {
      //         // If we find an entry, the authentication is successful
      //         resolve(true);
      //       });

      //       search.on('end', (result) => {
      //         if (result.status !== 0) {
      //           console.error('LDAP search failed with status:', result.status);
      //           resolve(false);
      //         }
      //       });
      //     });
      //   });
      // });
    } catch (error) {
      console.error('LDAP auth error:', error);
      return false;
    }
  }

  // SAML authentication method
  static saml(app, req) {
    const config = app.get("configuration");
    
    // Get Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('SAML ')) {
      return false;
    }

    try {
      const token = authHeader.split(' ')[1];
      return token === config.saml;
    } catch (error) {
      console.error('SAML auth error:', error);
      return false;
    }
  }
}

module.exports = AuthService; 