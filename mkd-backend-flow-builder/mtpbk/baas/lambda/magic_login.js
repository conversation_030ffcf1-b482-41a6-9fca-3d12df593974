const JwtService = require("../../../baas/services/JwtService");
const MailService = require("../../../baas/services/MailService");
const { sqlDateFormat, sqlDateTimeFormat } = require("../../../baas/services/UtilService");

// Helper functions
const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!email || !emailRegex.test(email)) {
    return {
      status: 403,
      response: {
        error: true,
        message: "Invalid Email",
        validation: [{ field: "email", message: "Email is invalid or missing" }]
      }
    };
  }
  return null;
};

const validateRole = (Role) => {
  if (!Role.canMagicLinkLogin()) {
    return {
      status: 403,
      response: {
        error: true,
        message: "Magic link login not allowed"
      }
    };
  }
  return null;
};

const generateMagicToken = () => {
  const token = JwtService.generateString(50);
  let tomorrow = new Date();
  tomorrow.setMinutes(new Date().getMinutes() + 10);

  return { token, expireAt: tomorrow };
};

const handleMagicLinkGenerate = async (req, res, sdk, mailService, config) => {
  try {
    
    // Validate email
    const emailError = validateEmail(req.body.email);
    if (emailError) return res.status(emailError.status).json(emailError.response);

    // Validate role permissions
    const Role = require(`../roles/wxy`);
    const roleError = validateRole(Role);
    if (roleError) return res.status(roleError.status).json(roleError.response);

    sdk.setProjectId("xyz");

    // Find or create user
    let user = await sdk.findOne('user', {
      email: req.body.email,
      role_id: Role.slug
    });

    if (!user) {
      const userResult = await sdk.create('user', {
        email: req.body.email,
        company_id: req.body.company_id ?? 0,
        role_id: Role.slug,
        status: 1,
        verify: 1,
        login_type: 1,
        password: " ",
        created_at: sqlDateFormat(new Date()),
        updated_at: sqlDateTimeFormat(new Date()),
      });
      user = userResult;
    }

    // Generate magic token
    const { token, expireAt } = generateMagicToken();

    // Save token
    sdk.setTable("tokens");
    const tokenPayload = {
      token,
      type: 4, // Magic link type
      data: JSON.stringify({
        email: req.body.email,
        role: Role.slug
      }),
      user_id: user.id,
      status: 1,
      created_at: sqlDateFormat(new Date()),
      updated_at: sqlDateTimeFormat(new Date()),
      expired_at: sqlDateTimeFormat(expireAt)
    };

    await sdk.create("tokens", tokenPayload);

    // Generate magic link
    const magicLink = `${config.app_url}/magic-login?token=${token}`;

    // Send magic link email
    await mailService.sendMagicLinkEmail(req.body.email, magicLink);

    return res.status(200).json({
      error: false,
      message: "Magic link sent successfully"
    });

  } catch (err) {
    console.error('Magic link generation error:', err);
    return res.status(403).json({
      error: true,
      message: err.message
    });
  }
};

const handleMagicLogin = async (req, res, sdk, config) => {
  try {
    if (!req.query.token) {
      return res.status(403).json({
        error: true,
        message: "Token Missing",
        validation: [{ field: "token", message: "Token missing" }]
      });
    }

    // Verify token
    sdk.setProjectId("xyz");
    sdk.setTable("tokens");

    const token = await sdk.findOne('tokens', {
      token: req.query.token.trim(),
      type: 4,
      status: 1
    });

    if (!token) {
      return res.status(403).json({
        error: true,
        message: "Invalid magic link"
      });
    }

    // Check token expiration
    if (new Date(token.expire_at) < new Date()) {
      return res.status(403).json({
        error: true,
        message: "Token has expired"
      });
    }

    // Get user data
    const tokenData = JSON.parse(token.data);
    sdk.setTable("user");
    const user = await sdk.findOne('user', { id: token.user_id, status: 1, verify: 1 });

    if (!user) {
      return res.status(403).json({
        error: true,
        message: "User not found"
      });
    }

    // Invalidate token
    sdk.setTable("token");
    await sdk.deleteById('tokens', token.id);

    // Generate JWT token
    const accessToken = JwtService.createAccessToken(
      {
        user_id: user.id,
        role: tokenData.role
      },
      config.access_jwt_expire,
      config.jwt_key
    );

    return res.status(200).json({
      error: false,
      token: accessToken,
      expire_at: config.access_jwt_expire,
      user_id: user.id,
      role: tokenData.role
    });

  } catch (err) {
    console.error('Magic login error:', err);
    return res.status(403).json({
      error: true,
      message: err.message
    });
  }
};

module.exports = function (app) {
  const config = app.get("configuration");
  const mailService = new MailService(config);

  app.post("/v1/api/xyz/wxy/lambda/magic-login/generate", async (req, res) => {
    await handleMagicLinkGenerate(req, res, app.get("sdk"), mailService, app.get("configuration"));
  });

  app.post("/v1/api/xyz/wxy/lambda/magic-login", async (req, res) => {
    await handleMagicLogin(req, res, app.get("sdk"), app.get("configuration"));
  });
};

// API definition for Postman collection
module.exports.getPostmanDefinition = function () {
  return [
    {
      method: "POST",
      name: "Generate Magic Link",
      url: "/v1/api/xyz/wxy/lambda/magic-login/generate",
      successPayload: "['Magic link sent successfully']",
      queryBody: [{ key: "email", value: "<EMAIL>" }],
      needToken: false,
      errors: [],
    },
    {
      method: "POST",
      name: "Magic Login",
      url: "/v1/api/xyz/wxy/lambda/magic-login",
      successPayload: "['JWT token']",
      queryBody: [{ key: "token", value: "magic-link-token" }],
      needToken: false,
      errors: [],
    },
  ];
};
