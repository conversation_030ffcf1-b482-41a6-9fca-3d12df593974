const AuthService = require("../services/AuthService");
const JwtService = require("../services/JwtService");
const NodeGoogleLogin = require("node-google-login");
const ManaKnightSDK = require("../core/ManaKnightSDK");
const BackendSDK = require("../core/BackendSDK");
const fs = require("fs");
const path = require("path");


module.exports = function (app) {
  app.get("/v2/api/lambda/google/code", async function (req, res) {
    const state = JSON.parse(req.query.state);
    let hostname = state.hostname;
    try {
      const base64DecodeBuffer = Buffer.from(state.project_id, "base64");
      const config = app.get("configuration");
      let base64Decode = base64DecodeBuffer.toString().split(":");
      const projectId = base64Decode[0];
      const role = state.role;
      let needRefreshToken = false;
      let refreshToken = undefined;

  

      if (state.is_refresh) {
        needRefreshToken = true;
      }

      // Note: Checking for permission as we can't use PermissionMiddleware here

      const database = base64Decode[2] ?? config.databaseName;

      let sdk = app.get("sdk");
      // sdk.setDatabase(database);
      sdk.setProjectId(projectId); 

      // let manaknightSDK = new ManaKnightSDK();
      let manaknightSDK = app.get("sdk")

      manaknightSDK.getDatabase();
      manaknightSDK.setProjectId(projectId);

      let originalUrl = req.originalUrl;

      manaknightSDK.getDatabase();
      manaknightSDK.setProjectId(projectId);

      const googleConfig = {
        clientID: config.google.client_id,
        clientSecret: config.google.client_secret,
        redirectURL: config.google.redirect_url,
        defaultScope: [
          "https://www.googleapis.com/auth/userinfo.email",
          "https://www.googleapis.com/auth/userinfo.profile",
        ],
      };



      // **Injection Point Start:** Attempt to load and run custom code
      const injectionPath = path.join(__dirname, "..", "custom", projectId, "social_callback.js"); // Path to the injection file
      try {
        if (fs.existsSync(injectionPath)) {
          const { google_callback } = require(injectionPath);

          await google_callback({
            req,
            res,
            project: { hostname },
            state,
            projectId,
            role,
            needRefreshToken,
            refreshToken,
            database,
            sdk,
            manaknightSDK,
            originalUrl,
            config,
            googleConfig
          });
          if (res.headersSent) return;
        }

      } catch (injectError) {
        console.error("Error during injection:", injectError);
      }
      // **Injection Point End:**

    

      const googleLogin = new NodeGoogleLogin(googleConfig);

      const userProfile = await googleLogin.getUserProfile(req.query.code);
      let service = new AuthService();
      //verify if that user belongs to that company
      // check if parts has third item
      let id;
      if (state.company_id) {
        const company_id = state.company_id;
        sdk.setProjectId(projectId);
        const company = await sdk.findOne("company", { id: company_id });
        if (!company) {
          return res
            .status(404)
            .json({ message: "Company Not found", error: true });
        }
        id = await service.googleLogin(
          sdk,
          projectId,
          userProfile.user,
          userProfile.tokens,
          role,
          company_id
        );
      } else {
        id = await service.googleLogin(
          sdk,
          projectId,
          userProfile.user,
          userProfile.tokens,
          role
        );
      }

      if (typeof id == "string") {
        // redirect to the frontend with error
        const response = {
          error: true,
          message: id,
        };
        const data = JSON.stringify(response);
        const encodedURI = encodeURI(data);
        return res.redirect(
          `https://${hostname}/login/oauth?data=${encodedURI}`
        );
      }

      if (needRefreshToken) {
        refreshToken = JwtService.createAccessToken(
          {
            user_id: id,
            role: role,
          },
          config.refresh_jwt_expire,
          config.jwt_key
        );
        let expireDate = new Date();
        expireDate.setSeconds(
          expireDate.getSeconds() + config.refresh_jwt_expire
        );
        await service.saveRefreshToken(
          sdk,
          req.projectId,
          id,
          refreshToken,
          expireDate
        );
      }

      const data = JSON.stringify({
        error: false,
        role: role,
        token: JwtService.createAccessToken(
          {
            user_id: id,
            role: role,
          },
          config.access_jwt_expire,
          config.jwt_key
        ),
        expire_at: config.access_jwt_expire,
        user_id: id,
        refresh_token: refreshToken,
      });

      const encodedURI = encodeURI(data);

      // console.log(`https://${project.hostname}/login/oauth?data=${encodedURI}`);

      res.redirect(
        `https://${hostname}/login/oauth?data=${encodedURI}`
      );
    } catch (error) {
      console.log(error);
      // redirect to frontend with errors
      const response = {
        error: true,
        message: error.message,
      };
      const data = JSON.stringify(response);
      const encodedURI = encodeURI(data);
      return res.redirect(
        `https://${hostname}/login/oauth?data=${encodedURI}`
      );
    }
  });



  return [
    {
      method: "GET",
      name: "Google Code API",
      url: "/v2/api/lambda/google/code",
      successPayload:
        "{error: false, role: 'admin', token: 'jwt token', expire_at: 60, user_id: 1}",
      queryBody: [{ code: "role", state: "projectId~secret" }],
      needToken: false,
      errors: [
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "access token", "message": "Something went wrong"}',
        },
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "me", "message": "Something went wrong"}',
        },
      ],
    },
    {
      method: "GET",
      name: "Google Login API",
      url: "/v2/api/lambda/google/login",
      successPayload: "['Will redirect to google login with auth link']",
      queryBody: [{ key: "role", value: "admin" }],
      needToken: false,
      errors: [
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "access token", "message": "Something went wrong"}',
        },
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "me", "message": "Something went wrong"}',
        },
      ],
    },
  ];
};

// API definition for Postman collection
module.exports.getPostmanDefinition = function () {
  return [
    {
      method: "GET",
      name: "Google Code API",
      url: "/v2/api/lambda/google/code",
      successPayload:
        "{error: false, role: 'admin', token: 'jwt token', expire_at: 60, user_id: 1}",
      queryBody: [{ code: "role", state: "projectId~secret" }],
      needToken: false,
      errors: [
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "access token", "message": "Something went wrong"}',
        },
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "me", "message": "Something went wrong"}',
        },
      ],
    },
    {
      method: "GET",
      name: "Google Login API",
      url: "/v2/api/lambda/google/login",
      successPayload: "['Will redirect to google login with auth link']",
      queryBody: [{ key: "role", value: "admin" }],
      needToken: false,
      errors: [
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "access token", "message": "Something went wrong"}',
        },
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "me", "message": "Something went wrong"}',
        },
      ],
    },
  ];
};

