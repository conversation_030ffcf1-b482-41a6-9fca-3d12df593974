const AuthService = require("../services/AuthService");
const JwtService = require("../services/JwtService");
const ProjectMiddleware = require("../middleware/ProjectMiddleware");
const PermissionMiddleware = require("../middleware/PermissionMiddleware");
const UrlMiddleware = require("../middleware/UrlMiddleware");
const HostMiddleware = require("../middleware/HostMiddleware");
const DevLogService = require("../services/DevLogService");
// const config = require("../config");
const jwt = require("jsonwebtoken");
const Twitter = require("node-twitter-api");

const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  PermissionMiddleware,
  // RateLimitMiddleware,
  // LogMiddleware,
  // UsageMiddleware
  // CheckProjectMiddleware,
  // AnalyticMiddleware,
  // RoleMiddleware
];

let logService = new DevLogService();

module.exports = function (app) {
  app.get(
    "/v2/api/lambda/twitter/login",
    middlewares,
    async function (req, res) {
      try {
        const twitter = new Twitter({
          consumerKey: config.twitter.consumer_key,
          consumerSecret: config.twitter.consumer_secret,
          callback: config.twitter.callbackUrl,
        });

        let _requestSecret;
        twitter.getRequestToken(function (err, requestToken, requestSecret) {
          if (err)
            return res.status(403).json({
              error: true,
              message: err.message || "something went wrong!",
            });
          else {
            _requestSecret = requestSecret;
            const authorizationUrl =
              "https://api.twitter.com/oauth/authenticate?oauth_token=" +
              requestToken;
            logService.log(authorizationUrl);
            return res.send(authorizationUrl);
          }
        });
      } catch (err) {
        return res.status(403).json({
          error: true,
          message: err.message,
        });
      }
    }
  );

  app.get(
    "/v2/api/lambda/twitter/access-token",
    middlewares,
    async function (req, res) {
      const sdk = app.get("sdk");

      const Role = require(`../roles/wxy`);
      if (!Role.canTwitterLogin()) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access",
        });
      }

      const twitter = new Twitter({
        consumerKey: config.twitter.consumer_key,
        consumerSecret: config.twitter.consumer_secret,
        callback: config.twitter.callbackUrl,
      });
      var requestToken = req.query.oauth_token,
        verifier = req.query.oauth_verifier;

      twitter.getAccessToken(
        requestToken,
        _requestSecret,
        verifier,
        function (err, accessToken, accessSecret) {
          if (err)
            return res.status(403).json({
              error: true,
              message: err.message || "something went wrong!",
            });
          else
            twitter.verifyCredentials(
              accessToken,
              accessSecret,
              async function (err, user) {
                if (err) res.status(500).send(err);
                else {
                  const payload = user;

                  const user_details = {
                    first_name: " ",
                    last_name: " ",
                    email: payload.email,
                  };
                  const service = new AuthService();
                  if (!user_details.email) {
                    throw new Error("Could not access email address");
                  }
                  sdk.setProjectId(projectId);
                  const twitter_user = await service.twitterLogin(
                    req.app.get("sdk"),
                    projectId,
                    user_details,
                    role
                  );
                  const { id, is_newuser } = twitter_user;

                  let new_jwt = JwtService.createAccessToken(
                    {
                      user_id: id,
                      role: role,
                    },
                    config.jwt_expire,
                    config.jwt_key
                  );

                  let refreshToken = JwtService.createAccessToken(
                    {
                      user_id: id,
                      role: role,
                    },
                    config.refresh_jwt_expire,
                    config.jwt_key
                  );
                  let expireDate = new Date();
                  expireDate.setSeconds(
                    expireDate.getSeconds() + config.refresh_jwt_expire
                  );
                  await service.saveRefreshToken(
                    app.get("sdk"),
                    req.projectId,
                    id,
                    refreshToken,
                    expireDate
                  );

                  // Remark: Fetching Project
                  let project = {};

                  if (config.env == "production") {
                    project = require("../project");
                  } else {
                    sdk.setProjectId(req.projectId);
                    sdk.setTable("projects");

                    // project = (
                    //   await sdk.get({
                    //     project_id: req.projectId,
                    //   })
                    // )[0];
                  }

                  const resData = JSON.stringify({
                    error: false,
                    role: role,
                    access_token: new_jwt,
                    refresh_token: refreshToken,
                    expire_at: config.jwt_expire,
                    user_id: id,
                    is_newuser: is_newuser,
                  });

                  const encodedURI = encodeURI(resData);

                  res.redirect(
                    `https://${project.hostname}/login/oauth?data=${encodedURI}`
                  );
                }
              }
            );
        }
      );
    }
  );

  return [];
};
// API definition for Postman collection
module.exports.getPostmanDefinition = function () {
  return [
    {
      method: "GET",
      name: "Twitter Login API",
      url: "/v1/api/xyz/wxy/lambda/twitter/login",
      successPayload: "['Will redirect to Twitter login with auth link']",
      queryBody: [{ key: "role", value: "wxy" }],
      needToken: false,
      errors: [
        {
          name: "403",
          query: [{ key: "role", value: "wxy" }],
          response: '{"error": true, "message": "Forbidden access."}'
        },
        {
          name: "501",
          query: [{ key: "role", value: "wxy" }],
          response: '{"error": true, "message": "Twitter login not yet implemented"}'
        }
      ]
    }
  ];
};
