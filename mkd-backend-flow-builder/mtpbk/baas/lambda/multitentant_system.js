const TokenMiddleware = require("../../../baas/middleware/TokenMiddleware");
const UploadService = require("../../../baas/services/UploadService");
const upload = UploadService.local_upload();
const { sqlDateFormat, sqlDateTimeFormat } = require("../../../baas/services/UtilService");
const { checkUserCompanyAdmin } = require("../../../baas/middleware/multiTenantMiddleware");
const AuthService = require("../../../baas/services/AuthService");
const PasswordService = require("../../../baas/services/PasswordService");
const JwtService = require("../../../baas/services/JwtService");
const StripeService = require("../../../baas/services/StripeService");
const NodeGoogleLogin = require("node-google-login");


const middlewares = [TokenMiddleware(), upload.single("file")];

/**
 * TODO:
 * 1. If company need subscription to register, then we charge per user. So every user will cost per month.
 * For example $5 per user per month is $15 /month for 3 users
 * 2. If company doesn't pay disable account and all users can't login
 * 3. need to be able to set which pages users can't access and can access control by admin
 * 4. Do we need 2fa or email verification. These need to be options made ahead of time
 * 5. Do we need social login for users? I don't think company account can be social login
 */

module.exports = function (app) {
  const stripe = new StripeService();
  const config = app.get('configuration');

  // Register A company Account
  app.post("/v1/api/xyz/wxy/lambda/company", middlewares, async function (req, res) {
    try {
      let sdk = app.get("sdk");
      const config = app.get("configuration");
      sdk.setProjectId("xyz");
      sdk.getDatabase();
      // const role = "company";
      const Role = require(`../roles/wxy`);
      if (!Role.permissions.canMultitenant) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }
      const { name, address, phone, city, state, country, zip, logo, owner_id, website, status } = req.body;
      sdk.setTable("user");
      if (req.role !== "admin" && req.role !== "super_admin") {
        return res.status(403).json({ error: true, message: "You are not allowed to perform this action" });
      }
      if (!owner_id) {
        return res.status(403).json({ error: true, message: "User not found" });
      }
      const owner = await sdk.find("user",{ id: owner_id });
      if (!owner) {
        return res.status(403).json({ error: true, message: "User not found" });
      }

      if (!name) {
        return res.status(403).json({ error: true, message: "Company name is required" });
      }

      sdk.setTable("company");
      let params = {
        logo: logo ?? "",
        website: website ?? "",
        status: status ?? 1,
        name: name ?? "",
        phone: phone ?? "",
        city: city ?? "",
        state: state ?? "",
        country: country ?? "",
        address: address ?? "",
        owner_id,
        created_at: sqlDateFormat(new Date()),
        updated_at: sqlDateFormat(new Date()),
        ...(zip && { zip })
      };

      const result = await sdk.create("company",params);

      sdk.setTable("company_admin");
      await sdk.create("company_admin",{ company_id: result.id, user_id: owner_id, created_at: sqlDateFormat(new Date()), updated_at: sqlDateFormat(new Date()) });

      return res.status(201).json({ id: result, error: false });
    } catch (error) {
      return res.status(403).json({ error: "Something went wrong", error: true });
    }
  });
  // Register A company Account
  app.post("/v1/api/xyz/wxy/lambda/company/register", middlewares, async function (req, res) {
    try {
      let sdk = app.get("sdk");
      sdk.setProjectId("xyz");
      sdk.getDatabase();
      // const role = "company";
      const Role = require(`../roles/wxy`);
      if (!Role.permissions.canMultitenant) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }
      const { name, address, phone, city, state, country, zip, logo, website, status } = req.body;

      if (!name) {
        return res.status(403).json({ error: true, message: "Company name is required" });
      }

      sdk.setTable("company");
      const company_exist = (await sdk.find("company",{ owner_id: req.user_id }))[0];
      if (company_exist) {
        return res.status(403).json({ error: true, message: "Company already exists for this user" });
      }

      let params = {
        logo: logo ?? "",
        website: website ?? "",
        status: status ?? 1,
        name: name ?? "",
        phone: phone ?? "",
        city: city ?? "",
        state: state ?? "",
        country: country ?? "",
        address: address ?? "",
        owner_id: req.user_id,
        created_at: sqlDateFormat(new Date()),
        updated_at: sqlDateFormat(new Date()),
        ...(zip && { zip })
      };

      const result = await sdk.create("company",params);

      sdk.setTable("company_admin");
      await sdk.create("company_admin",{ company_id: result.id, user_id: req.user_id, created_at: sqlDateFormat(new Date()), updated_at: sqlDateFormat(new Date()) });

      return res.status(201).json({ id: result, error: false });
    } catch (error) {
      return res.status(403).json({ message: "Something went wrong", error: true });
    }
  });

  // Update company Account
  app.patch("/v1/api/xyz/wxy/lambda/company/:id", [...middlewares, checkUserCompanyAdmin(app, "xyz")], async function (req, res) {
    try {
      let sdk = app.get("sdk");
      sdk.setProjectId("xyz");
      sdk.getDatabase();
      sdk.setTable("company");

      const Role = require(`../roles/wxy`);
      if (!Role.permissions.canMultitenant) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      const { name, address, phone, city, state, country, logo, owner_id, website, status } = req.body;
      sdk.setTable("user");
      let owner;
      if (owner_id) {
        owner = await sdk.find("company",{ id: owner_id });
      }
      if (owner_id && !owner) {
        return res.status(403).json({ error: true, message: "User not found" });
      }
      sdk.setTable("company");
      let params = {
        ...(logo && { logo }),
        ...(website && { website }),
        ...(status && { status }),
        ...(name && { name }),
        ...(phone && { phone }),
        ...(city && { city }),
        ...(state && { state }),
        ...(country && { country }),
        ...(address && { address }),
        ...(owner_id && { owner_id }),
        updated_at: sqlDateFormat(new Date())
      };

      const company = await sdk.update("company", { id: req.params.id }, params);
      return res.status(200).json({ id: req.params.id, error: false });
    } catch (error) {
      return res.status(403).json({ message: "Something went wrong", error: true });
    }
  });

  // Delete company Account
  app.delete("/v1/api/xyz/wxy/lambda/company/:id", [...middlewares, checkUserCompanyAdmin(app, "xyz")], async function (req, res) {
    try {
    let sdk = app.get("sdk");
      sdk.setProjectId("xyz");
      sdk.getDatabase();

      const Role = require(`../roles/wxy`);
      if (!Role.permissions.canMultitenant) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      sdk.setTable("company");

      await sdk.delete("company",{ id: req.params.id });
      return res.status(200).json({ id: req.params.id, error: false });
    } catch (error) {
      return res.status(403).json({ error: true, message: "Something went wrong" });
    }
  });

  // get all companies in the system
  app.get("/v1/api/xyz/wxy/lambda/company/all", middlewares, async function (req, res) {
    try {
      if (req.role !== "admin" && req.role !== "super_admin") {
        return res.status(403).json({ message: "Permission denied", error: true });
      }
      let sdk = app.get("sdk");
      sdk.getDatabase();
      sdk.setProjectId("xyz");

      const Role = require(`../roles/wxy`);
      if (!Role.permissions.canMultitenant) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      // async joinPaginateStr(table1, table2, joinId1, joinId2, selectStr, where, page, limit, orderBy, direction) {
      const projectId = "xyz";
      const limit = req.body.limit || 10;
      const page = req.body.page - 1 || 0;
      const result = await sdk.joinPaginate(
        "company",
        "user",
        "owner_id",
        "id",
        `
        ${projectId}_company.id,
        ${projectId}_company.name,
        ${projectId}_company.website,
        ${projectId}_company.logo,
        ${projectId}_company.owner_id,
        ${projectId}_company.phone,
        ${projectId}_company.city,
        ${projectId}_company.state,
        ${projectId}_company.country,
        ${projectId}_company.address,
        ${projectId}_company.status,
        ${projectId}_company.created_at,
        ${projectId}_company.updated_at,
        ${projectId}_user.id as user_id,
        ${projectId}_user.role_id,
        ${projectId}_user.email,
        ${projectId}_user.login_type,
        ${projectId}_user.verify,
        ${projectId}_user.company_id,
        ${projectId}_user.two_factor_authentication,
        ${projectId}_user.status as user_status,
        ${projectId}_user.created_at as user_created_at
        `,
        [],
        page,
        limit
      );

      return res.status(200).json({
        error: false,
        list: result
      });
    } catch (err) {
      console.log(err);
      return res.status(403).json({
        error: true,
        message: "Something went wrong"
      });
    }
  });

  // get all companies per owner
  app.get("/v1/api/xyz/wxy/lambda/company/owner", [...middlewares], async function (req, res) {
    try {
      let sdk = app.get("sdk");
      const config = app.get("configuration");
      sdk.getDatabase();
      sdk.setProjectId("xyz");

      const Role = require(`../roles/wxy`);
      if (!Role.permissions.canMultitenant) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }
      // async joinPaginateStr(table1, table2, joinId1, joinId2, selectStr, where, page, limit, orderBy, direction) {
      const projectId = "xyz";
      const limit = req.body.limit || 10;
      const page = req.body.page - 1 || 0;
      const result = await sdk.joinPaginate(
        "company",
        "user",
        "owner_id",
        "id",
        `
        ${projectId}_company.id,
        ${projectId}_company.name,
        ${projectId}_company.website,
        ${projectId}_company.logo,
        ${projectId}_company.owner_id,
        ${projectId}_company.phone,
        ${projectId}_company.city,
        ${projectId}_company.state,
        ${projectId}_company.country,
        ${projectId}_company.address,
        ${projectId}_company.status,
        ${projectId}_company.created_at,
        ${projectId}_company.updated_at,
        ${projectId}_user.id as user_id,
        ${projectId}_user.role_id,
        ${projectId}_user.email,
        ${projectId}_user.login_type,
        ${projectId}_user.verify,
        ${projectId}_user.company_id,
        ${projectId}_user.two_factor_authentication,
        ${projectId}_user.status as user_status,
        ${projectId}_user.created_at as user_created_at
        `,
        [`${projectId}_company.owner_id=${req.user_id}`],
        page,
        limit
      );

      return res.status(200).json({
        error: false,
        list: result
      });
    } catch (err) {
      console.log(err);
      return res.status(403).json({
        error: true,
        message: "Something went wrong"
      });
    }
  });

  // get all companies per admins
  app.get("/v1/api/xyz/wxy/lambda/company/admins", [...middlewares], async function (req, res) {
    try {
      let sdk = app.get("sdk");
      sdk.getDatabase();
      sdk.setProjectId("xyz");

      const Role = require(`../roles/wxy`);
      if (!Role.permissions.canMultitenant) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      if (req.role !== "admin" && req.role !== "super_admin") {
        return res.status(403).json({ message: "Permission denied", error: true });
      }

      const projectId = "xyz";
      const limit = req.body.limit || 10;
      const page = req.body.page - 1 || 0;
      const result = await sdk.joinPaginate(
        "company",
        "company_admin",
        "id",
        "company_id",
        `
        ${projectId}_company.id,
        ${projectId}_company.name,
        ${projectId}_company.website,
        ${projectId}_company.logo,
        ${projectId}_company.owner_id,
        ${projectId}_company.phone,
        ${projectId}_company.city,
        ${projectId}_company.state,
        ${projectId}_company.country,
        ${projectId}_company.address,
        ${projectId}_company.status,
        ${projectId}_company.created_at,
        ${projectId}_company.updated_at
        `,
        [`${projectId}_company_admin.user_id=${req.user_id}`],
        page,
        limit
      );

      return res.status(200).json({
        error: false,
        list: result
      });
    } catch (err) {
      console.log(err);
      return res.status(403).json({
        error: true,
        message: "Something went wrong"
      });
    }
  });

  //   Add new users
  app.post("/v1/api/xyz/wxy/lambda/company/:id/user-add", [...middlewares, checkUserCompanyAdmin(app, "xyz")], async function (req, res) {
    try {
      const sdk = app.get("sdk");
      const config = app.get("configuration");

      const Role = require(`../roles/wxy`);
      if (!Role.permissions.canMultitenant) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      sdk.setProjectId("xyz");
      let subscribed;
      sdk.setTable("company");
      const company = (await sdk.find("company",{ id: req.params.id }))[0];
      sdk.setTable("user");
      const owner = (await sdk.find("user",{ id: company.owner_id }))[0];
      if (!owner) {
        return res.status(403).json({
          error: true,
          message: "the owner id of this company is invalid"
        });
      }
      if (!owner.stripe_uid) {
        const customer = await stripe.createStripeCustomer({ email: owner.email });
        owner.stripe_uid = customer.id;
        await sdk.updateById("user", owner.id, { stripe_uid: customer.id });
      }
      const customerSub = await stripe.listStripeSubscription({ customer: owner.stripe_uid });
      subscribed = customerSub?.data?.find((sub) => sub.status === "active" && sub.plan?.metadata?.multitenant);
      if (!subscribed) {
        return res.status(403).json({ error: true, message: "You need to subscribe to perform this action" });
      }

      sdk.setTable("user");
      const { email, password, role, first_name, last_name, verify } = req.body;

      if (!email) res.status(403).json({ error: true, message: "Email is required" });
      if (!password) res.status(403).json({ error: true, message: "Password is required" });
      if (!role) res.status(403).json({ error: true, message: "role is required" });
      if (!first_name) res.status(403).json({ error: true, message: "First Name is required" });
      if (!last_name) res.status(403).json({ error: true, message: "Last name is required" });
      if (!verify) res.status(403).json({ error: true, message: "Verify is required" });


      const exist = await sdk.find("user",{
        email: email
      });

      if (exist.length > 0) {
        return res.status(403).json({
          error: true,
          message: "User exists"
        });
      } else {
        const hashPassword = await PasswordService.hash(password);
        const result = await sdk.create("user",{
          email,
          company_id: req.params.id,
          password: hashPassword,
          role_id: role,
          data: JSON.stringify({first_name, last_name}),
          verify,
          status: 1,
          login_type: 0,
          created_at: sqlDateFormat(new Date()),
          updated_at: sqlDateTimeFormat(new Date())
        });
        const users = await sdk.find("user",{ company_id: req.params.id });
        if (subscribed) {
          await stripe.updateSubscriptionItem({ id: subscribed.items.data[0].id, params: { quantity: users?.length, proration_behavior: "always_invoice" } });
        }


        await sdk.create("preference",{
          user_id: result.id,
          first_name,
          last_name,
          created_at: sqlDateFormat(new Date()),
          updated_at: sqlDateTimeFormat(new Date())
        });

        return res.status(200).json({
          error: false,
          message: "User added successfully"
        });
      }
    } catch (err) {
      console.log(err);
      return res.status(403).json({
        error: true,
        message: "Something went wrong"
      });
    }
  });

  //   company users login
  app.post("/v1/api/xyz/wxy/lambda/company/:id/user-login", async function (req, res) {
    try {
      let sdk = app.get("sdk");
      const config = app.get("configuration");
      sdk.getDatabase();
      sdk.setProjectId("xyz");
      sdk.setTable("user");

      const Role = require(`../roles/wxy`);
      if (!Role.permissions.canMultitenant) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      let service = new AuthService();
      let refreshToken = undefined;
      const needRefreshToken = req.body.is_refresh ? true : false;
      const { email, password, role } = req.body;
      if (!email) {
        return res.status(403).json({
          error: true,
          message: "Invalid Credentials",
          validation: [{ field: "email", message: "Email missing" }]
        });
      }
      if (!password) {
        return res.status(403).json({
          error: true,
          message: "Invalid Credentials",
          validation: [{ field: "password", message: "Password missing" }]
        });
      }
      const user_exist = (await sdk.find("user",{ email }))[0];

      if (!user_exist || user_exist.company_id != req.params.id) {
        return res.status(403).json({ error: true, message: "Invalid Credentials" });
      }
      const result = await service.login(app, "xyz", email, password, role);

      if (!result.status || !result.verify) {
        return res.status(403).json({
          error: true,
          message: "Failed to login"
        });
      }

      if (typeof result == "string") {
        return res.status(403).json({
          error: true,
          message: result
        });
      } else {
        //TODO: Use the secret from project

        if (needRefreshToken) {
          refreshToken = JwtService.createAccessToken(
            {
              user_id: result.id,
              role: role
            },
            config.refresh_jwt_expire,
            config.jwt_key
          );
          let expireDate = new Date();
          expireDate.setSeconds(expireDate.getSeconds() + config.refresh_jwt_expire);
          await service.saveRefreshToken(sdk, "xyz", result.id, refreshToken, expireDate);
        }

        return res.status(200).json({
          error: false,
          role,
          token: JwtService.createAccessToken(
            {
              user_id: result.id,
              role
            },
            config.access_jwt_expire,
            config.jwt_key
          ),
          refresh_token: refreshToken,
          expire_at: config.access_jwt_expire,
          user_id: result.id
        });
      }
    } catch (err) {
      console.log(err);
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  // company users google auth
  app.get("/v1/api/xyz/wxy/lambda/company/:id/google/login", middlewares, async function (req, res) {
    if (req.query.role === "admin" || req.query.role === "super_admin") 
        return res.status(403).json({ error: true, message: "Can't register admin with this API" });


    const Role = require(`../roles/wxy`);
    if (!Role.permissions.canGoogleLogin) {
      return res.status(403).json({
        error: true,
        message: "Forbidden access",
      });
    }

    let sdk = app.get("sdk");
    sdk.getDatabase();
    sdk.setProjectId("xyz");
    let projectId = "xyz";
    const userRole = "wxy";

    const googleConfig = {
      clientID: config.google.client_id,
      clientSecret: config.google.client_secret,
      redirectURL: config.google.redirect_url,
      defaultScope: ["https://www.googleapis.com/auth/userinfo.email", "https://www.googleapis.com/auth/userinfo.profile"]
    };
    const googleLogin = new NodeGoogleLogin(googleConfig);

    const encodedProjectId = Buffer.from(`xyz:_`).toString("base64");

    
    let state = {
      project_id: encodedProjectId,
      role: userRole,
      hostname: "xyz.manaknightdigital.com"
    }

    if (req.params.id != undefined) {
      state.company_id = req.params.id;
    }
    if (req.query.is_refresh != undefined) {
      state.is_refresh = "with_refresh";
    }


    let authURL = googleLogin.generateAuthUrl() + "&state=" + JSON.stringify(state);

    return res.send(authURL);
  });

  //   Add admin to a company
  app.post("/v1/api/xyz/wxy/lambda/company/:id/admin-add", [...middlewares, checkUserCompanyAdmin(app, "xyz")], async function (req, res) {
    try {
      const { user_id } = req.body;
      let sdk = app.get("sdk");
      sdk.getDatabase();
      sdk.setProjectId("xyz");

      const Role = require(`../roles/wxy`);
      if (!Role.permissions.canMultitenant) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      if (!user_id) {
        return res.status(403).json({ error: true, message: "user_id is required" });
      }
      const user = (await sdk.find("user",{ id: user_id }))[0];
      if (!user) {
        return res.status(403).json({ error: true, message: "Invalid user_id" });
      }
      if (user.company_id != req.params.id) {
        return res.status(403).json({ error: true, message: "User does not belong to the company" });
      }

      await sdk.create("company_admin",{ company_id: req.params.id, user_id, created_at: sqlDateFormat(new Date()) });
      return res.status(200).json({
        error: false,
        message: "Admin added successfully"
      });
    } catch (err) {
      console.log(err);
      return res.status(403).json({
        error: true,
        message: "Something went wrong"
      });
    }
  });

  //   remove admin to a company
  app.post("/v1/api/xyz/wxy/lambda/company/:id/admin-remove", [...middlewares, checkUserCompanyAdmin(app, "xyz")], async function (req, res) {
    try {
      const { user_id } = req.body;
      let sdk = app.get("sdk");
      sdk.getDatabase();
      sdk.setProjectId("xyz");

      const Role = require(`../roles/wxy`);
      if (!Role.permissions.canMultitenant) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      if (!user_id) {
        return res.status(403).json({ error: true, message: "user_id is required" });
      }
      const user = (await sdk.find("user",{ id: user_id }))[0];
      if (!user) {
        return res.status(403).json({ error: true, message: "Invalid user_id" });
      }
      if (user_id == req.params.id) {
        return res.status(403).json({ error: true, message: "Cannot remove company owner" });
      }

      await sdk.delete("company_admin", { company_id: req.params.id, user_id });
      return res.status(200).json({ error: false, message: "Admin removed from company" });
    } catch (err) {
      console.log(err);
      return res.status(403).json({
        error: true,
        message: "Something went wrong"
      });
    }
  });

  //   remove user from a company
  app.post("/v1/api/xyz/wxy/lambda/company/:id/user-remove", [...middlewares, checkUserCompanyAdmin(app, "xyz")], async function (req, res) {
    try {
      const { user_id } = req.body;
      let sdk = req.app.get("sdk");
      sdk.getDatabase();
      sdk.setProjectId("xyz");

      const Role = require(`../roles/wxy`);
      if (!Role.permissions.canMultitenant) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      if (!user_id) {
        return res.status(403).json({ error: true, message: "user_id is required" });
      }
      const user = (await sdk.find("user",{ id: user_id, company_id: req.params.id }))[0];
      if (!user) {
        return res.status(403).json({ error: true, message: "Invalid user_id" });
      }
      if (user_id == req.params.id) {
        return res.status(403).json({ error: true, message: "Cannot remove company owner" });
      }

      await sdk.deleteById("user", user_id);
      return res.status(200).json({ error: false, message: "User removed from company" });
    } catch (err) {
      console.log(err);
      return res.status(403).json({
        error: true,
        message: "Something went wrong"
      });
    }
  });

  //   list company admins
  app.get("/v1/api/xyz/wxy/lambda/company/:id/admins", [...middlewares, checkUserCompanyAdmin(app, "xyz")], async function (req, res) {
    try {
      let sdk = app.get("sdk");
      sdk.getDatabase();
      sdk.setProjectId("xyz");

      const Role = require(`../roles/wxy`);
      if (!Role.permissions.canMultitenant) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      const projectId = "xyz";
      const limit = req.body.limit || 10;
      const page = req.body.page - 1 || 0;
      const result = await sdk.joinPaginate(
        "company_admin",
        "user",
        "user_id",
        "id",
        `
        ${projectId}_user.id as user_id,
        ${projectId}_user.role_id,
        ${projectId}_user.data,
        ${projectId}_user.email,
        ${projectId}_user.login_type,
        ${projectId}_user.verify,
        ${projectId}_user.company_id,
        ${projectId}_user.stripe_uid,
        ${projectId}_user.two_factor_authentication,
        ${projectId}_user.status as user_status,
        ${projectId}_user.created_at as user_created_at
        `,
        [`${projectId}_company_admin.company_id=${req.params.id}`],
        page,
        limit
      );
      return res.status(200).json({
        error: false,
        list: result
      });
    } catch (err) {
      console.log(err);
      return res.status(403).json({
        error: true,
        message: "Something went wrong"
      });
    }
  });



  //get user
  app.get("/v1/api/xyz/wxy/lambda/company/:id/user/:user_id", middlewares, checkUserCompanyAdmin(app, "xyz"), async function (req, res) {
    try {
      let sdk = app.get("sdk");
      sdk.setProjectId("xyz");
      sdk.getDatabase();
      sdk.setTable("user");

      const Role = require(`../roles/wxy`);
      if (!Role.permissions.canMultitenant) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      const { user_id, id } = req.params;
      const user = (await sdk.find("user",{ id: user_id, company_id: id }))[0];
      return res.status(200).json({
        error: false,
        result: user
      });
    } catch (err) {
      res.status(403).json({
        message: "Something went wrong",
        error: true
      });
    }
  });
  //get user
  app.put("/v1/api/xyz/wxy/lambda/company/:id/user/:user_id", middlewares, checkUserCompanyAdmin(app, "xyz"), async function (req, res) {
    try {
      let sdk = app.get("sdk");
      sdk.setProjectId("xyz");
      sdk.getDatabase();

      const Role = require(`../roles/wxy`);
      if (!Role.permissions.canMultitenant) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      const { user_id, id } = req.params;
      const user = (await sdk.find("user",{ id: user_id, company_id: id }))[0];

      if (!user) {
        return res.status(403).json({
          message: "Invalid user id",
          error: true
        });
      }

      let { first_name, last_name, email, password } = req.body;

      if (password) {
        password = await PasswordService.hash(req.body.password);
      }

      if (email) {
        const exist = await sdk.find("user",{
          email
        });

        if (exist.length > 0 && exist[0].id != user.id) {
          return res.status(403).json({
            error: true,
            message: "Email Exist Already",
            validation: [{ field: "email", message: "Email Exist Already" }]
          });
        }
      }

      await sdk.update(
        "user",
        {
          id: user.id
        },
        {
          ...(email && { email }),
          ...(password && { password }),
          ...(first_name && { data: JSON.stringify({ first_name, last_name }) }),
          updated_at: sqlDateTimeFormat(new Date())
        },
      );

      await sdk.update("preference",{ user_id }, { first_name, last_name });

      return res.status(200).json({
        error: false,
        message: "User updated successfully"
      });
    } catch (err) {
      res.status(403).json({
        message: "Something went wrong",
        error: true
      });
    }
  });
  //  Report Usage
  app.post("/v1/api/xyz/wxy/lambda/company/report-usage", middlewares, checkUserCompanyAdmin(app, "xyz"), async function (req, res) {
    try {
      let sdk = app.get("sdk");
      sdk.setProjectId("xyz");
      sdk.getDatabase();

      const Role = require(`../roles/wxy`);
      if (!Role.permissions.canMultitenant) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      const { id } = req.body;

      

      sdk.setProjectId("xyz");

      let subscribed;
      const company = (await sdk.find("company",{ id }))[0];

      const owner = (await sdk.find("user",{ id: company.owner_id }))[0];
      if (!owner.stripe_uid) {
        const customer = await stripe.createStripeCustomer({ email: owner.email });
        owner.stripe_uid = customer.id;
        await sdk.update("user",{id: owner.id},{ stripe_uid: customer.id });
      }

      const customerSub = await stripe.listStripeSubscription({ customer: owner.stripe_uid });
      subscribed = customerSub?.data?.find((sub) => sub.status === "active" && sub.plan?.metadata?.multitenant_usage == 1);
      if (!subscribed) {
        return res.status(403).json({ error: true, message: "You need to subscribe to perform this action" });
      }

      sdk.setProjectId("xyz");
      sdk.setTable("company_usage");
      const usage_reported = await sdk.create("company_usage",{ company_id: id, created_at: new Date(), updated_at: new Date() });
      if (usage_reported && subscribed) {
        await stripe.createUsageCharge({ subItemId: subscribed.items.data[0].id, quantity: 1 });
      }
      return res.status(200).json({
        message: "Usage reported successfully",
        error: false
      });
    } catch (err) {
      console.log(err);
      res.status(403).json({
        message: "Something went wrong",
        error: true
      });
    }
  });

  app.post("/v1/api/xyz/wxy/lambda/company/subscribe", middlewares, TokenMiddleware({}), async function (req, res) {
    /**
     * Params: success_url, mode, cancel_url, client_reference_id, customer, customer_email
     *
     */
    let sdk = app.get("sdk");
    sdk.setProjectId("xyz");
    sdk.getDatabase();


    const Role = require(`../roles/wxy`);
    if (!Role.permissions.canMultitenant) {
      return res.status(403).json({
        error: true,
        message: "Forbidden access"
      });
    }

    const {
      mode,
      success_url,
      cancel_url,
      shipping_address_collection,
      shipping_options,
      payment_method_types,
      payment_intent_data,
      phone_number_collection,
      line_items,
      metadata,
      owner_id
    } = req.body;

    try {
      // const validationResult = await ValidationService.validateObject(
      //   {
      //     mode: "required",
      //     success_url: "required",
      //     cancel_url: "required",
      //     payment_method_types: "required",
      //     line_items: "required",
      //     owner_id: "required"
      //   },
      //   { mode, success_url, cancel_url, payment_method_types, line_items, owner_id }
      // );
      // if (validationResult.error) {
      //   return res.status(400).json(validationResult);
      // }

      sdk.setTable("user");
      const customer = await sdk.find("user",{ id: owner_id });
      if (!customer[0]) {
        return res.status(404).json({ error: true, message: "Customer not found" });
      }
      if (!customer[0].stripe_uid) {
        const stripe_customer = await stripe.createStripeCustomer({ email: customer[0].email });
        customer[0].stripe_uid = stripe_customer.id;
        await sdk.update("user", { id: customer[0].id }, { stripe_uid: stripe_customer.id });
      }

      if (mode == "subscription") {
        sdk.setTable("stripe_subscription");
        const customerSubscriptions = await sdk.rawQuery(`SELECT * FROM ${sdk.getProjectId()}_stripe_subscription WHERE user_id = ${+customer[0].id} AND (status = 'active' OR status = 'trialing')`);
        if (customerSubscriptions.length > 0) {
          return res.status(401).json({ error: true, message: "Customer already has an active subscription" });
        }
      }

      // const stripeCustomer = await stripe.retrieveStripeCustomer({ customerId: customer[0].stripe_uid });
      // if (!stripeCustomer.default_source && !stripeCustomer.sources?.data?.length) {
      //   return res.status(403).json({ error: true, message: "You don't have a valid card attached, please add one and try again" });
      // }

      let params = {
        mode,
        success_url,
        cancel_url,
        customer: customer[0].stripe_uid,
        payment_method_types,
        shipping_address_collection,
        shipping_options,
        payment_intent_data,
        line_items,
        metadata: {
          ...metadata,
          projectId: sdk.getProjectId()
        },
        phone_number_collection
      };

      const checkout = await stripe.createCheckoutSession(params);

      sdk.setTable("stripe_checkout");
      await sdk.create("stripe_checkout",{
        user_id: customer[0].id,
        stripe_id: checkout.id,
        object: JSON.stringify(checkout),
        created_at: sqlDateFormat(new Date()),
        updated_at: sqlDateTimeFormat(new Date())
      });

      res.status(200).json({
        error: false,
        model: checkout
      });
    } catch (err) {
      console.error(err);
      let payload = {
        error: true,
        trace: err,
        message: err.message || "Something went wrong"
      };
      res.status(500).json(payload);
    }
  });

  return [
    {
      method: "POST",
      name: "Register Company Account API",
      url: "/v1/api/lambda/company",
      successBody: "",
      successPayload: "",
      errors: [
        {
          name: "401",
          body: "",
          response: ""
        },
        {
          name: "404",
          body: "",
          response: ""
        }
      ],
      needToken: true
    },
    {
      method: "POST",
      name: "Update Company Account API",
      url: "/v1/api/lambda/company/:id",
      successBody: "",
      successPayload: "",
      errors: [
        {
          name: "401",
          body: "{}",
          response: ""
        },
        {
          name: "401",
          body: "",
          response: ""
        },
        {
          name: "404",
          body: "",
          response: ""
        }
      ],
      needToken: true
    },
    {
      method: "POST",
      name: "Add Users API",
      url: "/v1/api/lambda/company/user-add",
      successBody: "",
      successPayload: "",
      errors: [
        {
          name: "401",
          body: "{}",
          response: ""
        },
        {
          name: "401",
          body: "",
          response: ""
        },
        {
          name: "404",
          body: "",
          response: ""
        }
      ],
      needToken: true
    },
    {
      method: "POST",
      name: "Suspend/Disable Company API",
      url: "/v1/api/lambda/company/suspend/:id",
      successBody: "",
      successPayload: "",
      errors: [
        {
          name: "401",
          body: "{}",
          response: ""
        },
        {
          name: "401",
          body: "",
          response: ""
        },
        {
          name: "404",
          body: "",
          response: ""
        }
      ],
      needToken: true
    },
    {
      method: "POST",
      name: "Suspend Employee API",
      url: "/v1/api/lambda/company/employee/:id",
      successBody: "",
      successPayload: "",
      errors: [
        {
          name: "401",
          body: "{}",
          response: ""
        },
        {
          name: "401",
          body: "",
          response: ""
        },
        {
          name: "404",
          body: "",
          response: ""
        }
      ],
      needToken: true
    },
    {
      method: "POST",
      name: "Suspend User API",
      url: "/v1/api/lambda/company/user/:id",
      successBody: "",
      successPayload: "",
      errors: [
        {
          name: "401",
          body: "{}",
          response: ""
        },
        {
          name: "401",
          body: "",
          response: ""
        },
        {
          name: "404",
          body: "",
          response: ""
        }
      ],
      needToken: true
    }
  ];
};


// API definition for Postman collection
module.exports.getPostmanDefinition = function () {
  return [
    {
      method: "POST",
      name: "Register Company Account API",
      url: "/v1/api/lambda/company",
      successBody: "",
      successPayload: "",
      errors: [
        {
          name: "401",
          body: "",
          response: ""
        },
        {
          name: "404",
          body: "",
          response: ""
        }
      ],
      needToken: true
    },
    {
      method: "POST",
      name: "Update Company Account API",
      url: "/v1/api/lambda/company/:id",
      successBody: "",
      successPayload: "",
      errors: [
        {
          name: "401",
          body: "{}",
          response: ""
        },
        {
          name: "401",
          body: "",
          response: ""
        },
        {
          name: "404",
          body: "",
          response: ""
        }
      ],
      needToken: true
    },
    {
      method: "POST",
      name: "Add Users API",
      url: "/v1/api/lambda/company/user-add",
      successBody: "",
      successPayload: "",
      errors: [
        {
          name: "401",
          body: "{}",
          response: ""
        },
        {
          name: "401",
          body: "",
          response: ""
        },
        {
          name: "404",
          body: "",
          response: ""
        }
      ],
      needToken: true
    },
    {
      method: "POST",
      name: "Suspend/Disable Company API",
      url: "/v1/api/lambda/company/suspend/:id",
      successBody: "",
      successPayload: "",
      errors: [
        {
          name: "401",
          body: "{}",
          response: ""
        },
        {
          name: "401",
          body: "",
          response: ""
        },
        {
          name: "404",
          body: "",
          response: ""
        }
      ],
      needToken: true
    },
    {
      method: "POST",
      name: "Suspend Employee API",
      url: "/v1/api/lambda/company/employee/:id",
      successBody: "",
      successPayload: "",
      errors: [
        {
          name: "401",
          body: "{}",
          response: ""
        },
        {
          name: "401",
          body: "",
          response: ""
        },
        {
          name: "404",
          body: "",
          response: ""
        }
      ],
      needToken: true
    },
    {
      method: "POST",
      name: "Suspend User API",
      url: "/v1/api/lambda/company/user/:id",
      successBody: "",
      successPayload: "",
      errors: [
        {
          name: "401",
          body: "{}",
          response: ""
        },
        {
          name: "401",
          body: "",
          response: ""
        },
        {
          name: "404",
          body: "",
          response: ""
        }
      ],
      needToken: true
    }
  ];
};


