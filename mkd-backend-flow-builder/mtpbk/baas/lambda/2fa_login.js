const JwtService = require("../../../baas/services/JwtService");
const TokenMiddleware = require("../../../baas/middleware/TokenMiddleware");
const TwoFactorService = require("../../../baas/services/TwoFactorService");
const TwilloSmsService = require("../../../baas/services/TwilloSmsService");
const AuthService = require("../../../baas/services/AuthService")


module.exports = function (app) {
  app.post("/v1/api/xyz/wxy/lambda/2fa/login", async function (req, res) {
    try {
      let sdk = app.get("sdk");
      const config = app.get("configuration");
      // sdk.setDatabase("xyz");
      sdk.setProjectId("xyz");

      // Check if 2FA is enabled for this role
      // const Role = require(`../custom/${req.params.xyz}/roles/${req.params.wxy}`);
      const Role = require(`../roles/wxy`);
      if (!Role.needs2FA()) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      // Validate required fields
      if (!req.body.email || !req.body.password || !req.body.role) {
        return res.status(403).json({
          error: true,
          message: "Invalid Credentials",
          validation: [
            { field: "email", message: "Email required" },
            { field: "password", message: "Password required" },
            { field: "role", message: "Role required" }
          ]
        });
      }

      // Find user
      let service = new AuthService();

      const result = await service.login(app, "xyz", req.body.email, req.body.password, req.body.role);

      if (!result.status)
        return res.status(403).json({
          error: true,
          message: "Failed to login"
        });

      if (typeof result == "string") {
        return res.status(403).json({
          error: true,
          message: result
        });
      }
      // Generate 2FA code
      const twoFaPayload = await TwoFactorService.getTwoFactorAuthenticationCode("xyz");
      const qrCode = await TwoFactorService.getDataURL(twoFaPayload.otpauthUrl);

      return res.status(200).json({
        error: false,
        role: req.body.role,
        qr_code: qrCode,
        one_time_token: JwtService.createAccessToken(
          {
            user_id: result.id,
            role: req.body.role,
            nonce: (Math.random() + 1).toString(36).substring(7)
          },
          60,
          config.jwt_key
        ),
        expire_at: 60,
        user_id: result.id
      });

    } catch (err) {
      console.log(err)
      return res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });
  app.get("/v1/api/lambda/2fa/test", async function (req, res) {
    try {
      let sdk = app.get("sdk");
      sdk.setProjectId("xyz");
      const user =await sdk.findOne("user", {
        id:1})
      
      console.log(user)
      return res.status(200).json({
        error: false,
        role: "wxy",
        user,
        qr_code: "test",
        one_time_token: "test",
        expire_at: 60
      });

    } catch (err) {
      return res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  app.post("/v2/api/xyz/wxy/lambda/2fa/signin", [TokenMiddleware()], async function (req, res) {
    try {
      // Check if 2FA is enabled for this role
      const Role = require(`../roles/wxy`);
      const config = app.get("configuration");
      if (!Role.needs2FA()) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      let sdk = app.get("sdk");
      // sdk.setDatabase("xyz");
      sdk.setProjectId("xyz");

      if (!req.body.email || !req.body.password || !req.body.role) {
        return res.status(403).json({
          error: true,
          message: "Invalid Credentials",
          validation: [
            { field: "email", message: "Email required" },
            { field: "password", message: "Password required" },
            { field: "role", message: "Role required" }
          ]
        });
      }

      // Find user
      const result = await sdk.findOne("user", {
        email: req.body.email,
        password: req.body.password,
        role_id: req.body.role,
        status: 1
      });

      if (!result) {
        return res.status(403).json({
          error: true,
          message: "Invalid credentials"
        });
      }

      const twoFaPayload = await TwoFactorService.setUpTwoFactorAuth("xyz", result.id, sdk);
      const qrCode = await TwoFactorService.getDataURL(twoFaPayload.otpauthUrl);

      if (twoFaPayload.type === "sms") {
        await TwilloSmsService.send(
          twoFaPayload.phone,
          `Your 2FA code is ${twoFaPayload.token}. It expires in ${twoFaPayload.expire_at}`
        );
      }

      return res.status(200).json({
        error: false,
        qr_code: qrCode,
        access_token: JwtService.createAccessToken(
          {
            user_id: result.id,
            nonce: (Math.random() + 1).toString(36).substring(7)
          },
          60,
          config.jwt_key
        ),
        expire_at: twoFaPayload.expire_at,
        user_id: result.id
      });
    } catch (err) {
      return res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  app.post("/v2/api/xyz/wxy/lambda/2fa/authorize", [TokenMiddleware()], async function (req, res) {
    try {

      // Check if 2FA is enabled for this role
      const Role = require(`../roles/wxy`);
      if (!Role.needs2FA()) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      let sdk = app.get("sdk");
      const config = app.get("configuration");
      // sdk.setDatabase("xyz");
      const user_id = req.user_id;
      sdk.setProjectId("xyz");

      // Find user
      const result = await sdk.findOne("user", { id: user_id });
      if (!result) {
        return res.status(403).json({
          error: true,
          message: "Invalid credentials"
        });
      }

      // Set up 2FA
      const twoFaPayload = await TwoFactorService.setUpTwoFactorAuth("xyz", result.id, sdk);
      const qrCode = await TwoFactorService.getDataURL(twoFaPayload.otpauthUrl);

      // Send SMS if needed
      if (twoFaPayload.type === "sms" && twoFaPayload.phone) {
        await TwilloSmsService.send(
          twoFaPayload.phone,
          `Your 2FA code is ${twoFaPayload.token}. It expires in ${twoFaPayload.expire_at}`
        );
      }

      return res.status(200).json({
        error: false,
        qr_code: qrCode,
        type: twoFaPayload.type ?? "qr",
        access_token: JwtService.createAccessToken(
          {
            user_id: result.id,
            nonce: (Math.random() + 1).toString(36).substring(7)
          },
          60,
          config.jwt_key
        ),
        expire_at: twoFaPayload.expire_at,
        user_id: result.id
      });

    } catch (err) {
      return res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  app.post("/v2/api/xyz/wxy/lambda/2fa/enable", [TokenMiddleware()], async function (req, res) {
    try {
      // Check if 2FA is enabled for this role
      const Role = require(`../roles/wxy`);
      if (!Role.needs2FA()) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }
      let sdk = app.get("sdk");
      const config = app.get("configuration");
      // sdk.setDatabase("xyz");
      sdk.setProjectId("xyz");

      const user_id = req.user_id;

      // Find user
      const result = await sdk.findOne("user", { id: user_id, status: 1 });

      if (!result) {
        return res.status(403).json({
          error: true,
          message: "Invalid credentials"
        });
      }
      // Enable 2FA
      const type = req.body.type ?? 'qr';
      const phone = req.body.phone;
      const token = req.body.token;

      let twoFaPayload;
      if (type === "sms") {
        if (!phone) {
          return res.status(400).json({
            error: true,
            message: "Phone number required for SMS 2FA"
          });
        }
        twoFaPayload = await TwoFactorService.enable2FA(sdk, user_id, "xyz", type, token, phone);
      } else {
        twoFaPayload = await TwoFactorService.enable2FA(sdk, user_id, "xyz", type, token);
      }

      // If already verified, return success
      if (twoFaPayload.verified) {
        return res.status(200).json({
          error: false,
          message: "2FA enabled successfully"
        });
      }

      // Otherwise return setup info
      const qrCode = await TwoFactorService.getDataURL(twoFaPayload.otpauthUrl);

      if (type === "sms") {
        await TwilloSmsService.send(
          phone,
          `Your 2FA code is ${twoFaPayload.token}. It expires in ${twoFaPayload.expire_at}`
        );
      }

      return res.status(200).json({
        error: false,
        qr_code: qrCode,
        access_token: JwtService.createAccessToken(
          {
            user_id: result.id,
            nonce: (Math.random() + 1).toString(36).substring(7)
          },
          60,
          config.jwt_key
        ),
        expire_at: twoFaPayload.expire_at,
        user_id: result.id
      });

    } catch (err) {
      return res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  app.post("/v2/api/xyz/wxy/lambda/2fa/disable", [TokenMiddleware()], async function (req, res) {
    try {
      // Check if 2FA is enabled for this role
      const Role = require(`../roles/wxy`);
      if (!Role.needs2FA()) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      let sdk = app.get("sdk")
      const config = app.get("configuration");
      // sdk.setDatabase("xyz");
      sdk.setProjectId("xyz");
      const user_id = req.user_id;

      const data = await TwoFactorService.disable2FA(sdk, user_id, "xyz");
      return res.status(200).json({
        error: false,
        message: data.message
      })
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  app.post("/v2/api/xyz/wxy/lambda/2fa/verify", [TokenMiddleware()], async function (req, res) {
    try {
      // Check if 2FA is enabled for this role
      const Role = require(`../roles/wxy`);
      if (!Role.needs2FA()) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }
      let sdk = app.get("sdk")
      // sdk.setDatabase("xyz");
      sdk.setProjectId("xyz");

      if (!req.body.token) {
        return res.status(403).json({
          error: true,
          message: "Token Invalid",
          validation: [{ field: "token", message: "Token missing" }]
        });
      }

      const verifyToken = JwtService.verifyAccessToken(req.body.token, config.jwt_key);


      if (verifyToken && verifyToken.nonce) {

        const speakEasyToken = await TwoFactorService.verifyTotp(req.body.token, verifyToken.user_id, sdk);

        if (speakEasyToken) {
          return res.status(200).json({
            error: false,
            valid: true,
            message: "Verified Successfully",
          });
        } else {
          return res.status(403).json({
            error: true,
            valid: false,
            message: "Invalid Token",
            validation: [{ field: "token", message: "Invalid Token" }]
          });
        }

      } else {
        return res.status(403).json({
          error: true,
          message: "Invalid Credential",
          validation: [{ field: "access_token", message: "Invalid Credentials" }]
        });
      }
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });
  app.post("/v2/api/xyz/wxy/lambda/2fa/auth", [TokenMiddleware()], async function (req, res) {
    try {
      // Check if 2FA is enabled for this role
      const Role = require(`../roles/wxy`);
      const config = app.get("configuration");
      if (!Role.needs2FA()) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      if (!req.body.code) {
        return res.status(403).json({
          error: true,
          message: "Invalid Code",
          validation: [{ field: "code", message: "Code missing" }]
        });
      }

      if (!req.body.token) {
        return res.status(403).json({
          error: true,
          message: "Token Invalid",
          validation: [{ field: "token", message: "Token missing" }]
        });
      }
      const verifyToken = JwtService.verifyAccessToken(req.body.token, config.jwt_key);

      if (verifyToken && verifyToken.nonce) {
        return res.status(200).json({
          error: false,
          role: verifyToken.role,
          token: JwtService.createAccessToken(
            {
              user_id: verifyToken.user_id,
              role: verifyToken.role
            },
            config.jwt_expire,
            config.jwt_key
          ),
          expire_at: config.jwt_expire,
          user_id: verifyToken.user_id
        });
      } else {
        return res.status(403).json({
          error: true,
          message: "Invalid Credential",
          validation: [{ field: "code", message: "Invalid Credentials" }]
        });
      }
    } catch (err) {
      // console.error(err);
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });
};

// API definition for Postman collection
module.exports.getPostmanDefinition = function () {
  return [
    {
      method: "POST",
      name: "2FA Login API",
      url: "/v1/api/xyz/wxy/lambda/2fa/login",
      successBody: '{ "email": "<EMAIL>", "password": "password123", "role": "user" }',
      successPayload: '{"error":false, "role": "user", "qr_code": "base64_qr_code", "one_time_token": "token", "expire_at": 60, "user_id": 1}',
      errors: [
        {
          name: "403",
          body: '{ "email": "<EMAIL>", "password": "wrongpassword", "role": "user" }',
          response: '{"error":true,"message":"Failed to login"}',
        },
        {
          name: "403",
          body: '{}',
          response: '{"error":true,"message":"Invalid Credentials","validation":[{"field": "email", "message": "Email required"},{"field": "password", "message": "Password required"},{"field": "role", "message": "Role required"}]}',
        }
      ],
      needToken: false,
    },
    {
      method: "POST",
      name: "2FA Verify API",
      url: "/v2/api/xyz/wxy/lambda/2fa/verify",
      successBody: '{ "token": "verification_code" }',
      successPayload: '{"error":false, "valid": true, "message": "Verified Successfully"}',
      errors: [
        {
          name: "403",
          body: '{ "token": "invalid_code" }',
          response: '{"error":true,"valid":false,"message":"Invalid Token","validation":[{"field": "token", "message": "Invalid Token"}]}',
        }
      ],
      needToken: true,
    },
    {
      method: "POST",
      name: "2FA Authorization API",
      url: "/v2/api/xyz/wxy/lambda/2fa/auth",
      successBody: '{ "code": "verification_code", "token": "one_time_token" }',
      successPayload: '{"error":false, "role": "user", "token": "access_token", "expire_at": 3600, "user_id": 1}',
      errors: [
        {
          name: "403",
          body: '{ "code": "invalid_code", "token": "one_time_token" }',
          response: '{"error":true,"message":"Invalid Credential","validation":[{"field": "code", "message": "Invalid Credentials"}]}',
        }
      ],
      needToken: true,
    }
  ];
};
