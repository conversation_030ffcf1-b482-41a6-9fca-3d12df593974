const PasswordService = require("../../../baas/services/PasswordService");

// Helper functions to reduce code duplication
const validateResetInput = (body) => {
  const validationErrors = [];

  if (!body.code || isNaN(body.code)) {
    validationErrors.push({
      field: "code",
      message: "Invalid Reset Code"
    });
  }

  if (!body.password) {
    validationErrors.push({
      field: "password",
      message: "Password missing"
    });
  }

  if (validationErrors.length > 0) {
    return {
      status: 403,
      response: {
        error: true,
        message: validationErrors[0].message,
        validation: validationErrors
      }
    };
  }

  return null;
};

const validateRole = (Role) => {
  if (!Role.canReset()) {
    return {
      status: 403,
      response: {
        error: true,
        message: "Cannot reset password"
      }
    };
  }
  return null;
};

const updateUserPassword = async (sdk, userId, hashedPassword) => {
  sdk.setProjectId("xyz");
  sdk.setTable("user");
  const updateResult = await sdk.updateById("user", userId, {
    password: hashedPassword
  });


  if (!updateResult) {
    throw new Error("Failed to update password");
  }

  return true;
};

const handlePasswordReset = async (req, res, sdk) => {
  try {
    // Validate input
    const validationError = validateResetInput(req.body);
    if (validationError) {
      return res.status(validationError.status).json(validationError.response);
    }

    const Role = require(`../roles/wxy`);
    const roleError = validateRole(Role);
    if (roleError) return res.status(roleError.status).json(roleError.response);

    // Setup SDK
    sdk.setProjectId("xyz");
    sdk.setTable("tokens");

    // Find token
    const token = await sdk.findOne('tokens', { code: req.body.code });

    if (!token) {
      return res.status(403).json({
        error: true,
        message: "Invalid Reset Request"
      });
    }

    // Check if token is expired
    if (new Date(token.expired_at) < new Date()) {
      return res.status(403).json({
        error: true,
        message: "Reset token has expired"
      });
    }

    // Verify user exists and is local account
    const data = JSON.parse(token.data);
    const userEmail = data.email;
    sdk.setProjectId("xyz");
    sdk.setTable("user");
    const user = await sdk.findOne('user', {
      email: userEmail,
      login_type: 0
    });

    if (!user) {
      return res.status(403).json({
        error: true,
        message: "Invalid user account"
      });
    }

    // Hash new password
    const hashedPassword = await PasswordService.hash(req.body.password);

    // Update user password
    await updateUserPassword(sdk, user.id, hashedPassword);

    // Invalidate used token
    sdk.setTable("tokens");
    await sdk.deleteById('tokens', token.id);

    return res.status(200).json({
      error: false,
      message: "Password Reset Successfully"
    });

  } catch (err) {
    console.error('Password reset error:', err);
    return res.status(403).json({
      error: true,
      message: err.message
    });
  }
};

module.exports = function (app) {
  // Web version
  app.post("/v1/api/xyz/wxy/lambda/reset", async (req, res) => {
    await handlePasswordReset(req, res, app.get("sdk"));
  });

  // Mobile version
  app.post("/v1/api/xyz/wxy/lambda/mobile/reset", async (req, res) => {
    const isMobileApp = req.headers['user-agent'] && req.headers['user-agent'].includes('Mobile');
    if (!isMobileApp) {
      return res.status(403).json({
        error: true,
        message: "Forbidden: This endpoint is only accessible from the mobile app."
      });
    }
    await handlePasswordReset(req, res, app.get("sdk"));
  });

  return [
    {
      method: "POST",
      name: "Reset Password API",
      url: "/v1/api/xyz/wxy/lambda/reset",
      successBody: '{ "code": 234556, "password": "password"}',
      successPayload: '{"error": false,"message": "Password Reset Successfully"}',
      errors: [
        {
          name: "403",
          body: '{"code": 234556, "password": "password"}',
          response:
            '{"error": true,"message": "Invalid Reset Password Request","validation": [{ "field": "token", "message": "Invalid Reset Password Request" }]}'
        },
        {
          name: "403",
          body: '{"password": "password"}',
          response: '{"error": true,"message": "Invalid Reset Code","validation": [{ "field": "code", "message": "Invalid Reset Code" }]}'
        },
        {
          name: "403",
          body: '{"code": 234556}',
          response: '{"error": true, "message": "Password Missing", "validation": [{ "field": "password", "message": "Invalid Reset Code" }]}'
        },
        {
          name: "403",
          body: '{"code": 234556, "password": "password"}',
          response: '{"error": true, "message": "Invalid Reset Request"}'
        },
        {
          name: "403",
          body: '{"code": 234556, "password": "password"}',
          response: '{"error": true, "message": "Mismatch Reset Code", "validation": [{ "field": "code", "message": "Mismatch Reset Code" }]}'
        },
        {
          name: "403",
          body: '{"email": "<EMAIL>"}',
          response: '{"error": true,"message": "Some error message"}'
        }
      ],
      needToken: false
    },
    {
      method: "POST",
      name: "Mobile Reset Password API",
      url: "/v1/api/xyz/wxy/lambda/reset",
      successBody: '{ "code": 234556, "password": "password"}',
      successPayload: '{"error": false,"message": "Password Reset Successfully"}',
      errors: [
        {
          name: "403",
          body: '{"code": 234556, "password": "password"}',
          response:
            '{"error": true,"message": "Invalid Reset Password Request","validation": [{ "field": "token", "message": "Invalid Reset Password Request" }]}'
        },
        {
          name: "403",
          body: '{"password": "password"}',
          response: '{"error": true,"message": "Invalid Reset Code","validation": [{ "field": "code", "message": "Invalid Reset Code" }]}'
        },
        {
          name: "403",
          body: '{"code": 234556}',
          response: '{"error": true, "message": "Password Missing", "validation": [{ "field": "password", "message": "Invalid Reset Code" }]}'
        },
        {
          name: "403",
          body: '{"code": 234556, "password": "password"}',
          response: '{"error": true, "message": "Invalid Reset Request"}'
        },
        {
          name: "403",
          body: '{"code": 234556, "password": "password"}',
          response: '{"error": true, "message": "Mismatch Reset Code", "validation": [{ "field": "code", "message": "Mismatch Reset Code" }]}'
        },
        {
          name: "403",
          body: '{"email": "<EMAIL>"}',
          response: '{"error": true,"message": "Some error message"}'
        }
      ],
      needToken: false
    }
  ];
};

// API definition for Postman collection
module.exports.getPostmanDefinition = function () {
  return [
    {
      method: "POST",
      name: "Reset Password API",
      url: "/v1/api/xyz/wxy/lambda/reset",
      successBody: '{ "code": 234556, "password": "password"}',
      successPayload: '{"error": false,"message": "Password Reset Successfully"}',
      errors: [
        {
          name: "403",
          body: '{"code": 234556, "password": "password"}',
          response:
            '{"error": true,"message": "Invalid Reset Password Request","validation": [{ "field": "token", "message": "Invalid Reset Password Request" }]}'
        },
        {
          name: "403",
          body: '{"password": "password"}',
          response: '{"error": true,"message": "Invalid Reset Code","validation": [{ "field": "code", "message": "Invalid Reset Code" }]}'
        },
        {
          name: "403",
          body: '{"code": 234556}',
          response: '{"error": true, "message": "Password Missing", "validation": [{ "field": "password", "message": "Invalid Reset Code" }]}'
        },
        {
          name: "403",
          body: '{"code": 234556, "password": "password"}',
          response: '{"error": true, "message": "Invalid Reset Request"}'
        },
        {
          name: "403",
          body: '{"code": 234556, "password": "password"}',
          response: '{"error": true, "message": "Mismatch Reset Code", "validation": [{ "field": "code", "message": "Mismatch Reset Code" }]}'
        },
        {
          name: "403",
          body: '{"email": "<EMAIL>"}',
          response: '{"error": true,"message": "Some error message"}'
        }
      ],
      needToken: false
    },
    {
      method: "POST",
      name: "Mobile Reset Password API",
      url: "/v1/api/xyz/wxy/lambda/reset",
      successBody: '{ "code": 234556, "password": "password"}',
      successPayload: '{"error": false,"message": "Password Reset Successfully"}',
      errors: [
        {
          name: "403",
          body: '{"code": 234556, "password": "password"}',
          response:
            '{"error": true,"message": "Invalid Reset Password Request","validation": [{ "field": "token", "message": "Invalid Reset Password Request" }]}'
        },
        {
          name: "403",
          body: '{"password": "password"}',
          response: '{"error": true,"message": "Invalid Reset Code","validation": [{ "field": "code", "message": "Invalid Reset Code" }]}'
        },
        {
          name: "403",
          body: '{"code": 234556}',
          response: '{"error": true, "message": "Password Missing", "validation": [{ "field": "password", "message": "Invalid Reset Code" }]}'
        },
        {
          name: "403",
          body: '{"code": 234556, "password": "password"}',
          response: '{"error": true, "message": "Invalid Reset Request"}'
        },
        {
          name: "403",
          body: '{"code": 234556, "password": "password"}',
          response: '{"error": true, "message": "Mismatch Reset Code", "validation": [{ "field": "code", "message": "Mismatch Reset Code" }]}'
        },
        {
          name: "403",
          body: '{"email": "<EMAIL>"}',
          response: '{"error": true,"message": "Some error message"}'
        }
      ],
      needToken: false
    }
  ];
};


