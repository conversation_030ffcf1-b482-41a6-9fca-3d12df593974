const AuthService = require("../services/AuthService");
const JwtService = require("../services/JwtService");
const jwt = require("jsonwebtoken");
const appleSignin = require("apple-signin-auth");
const BackendSDK = require("../core/BackendSDK");

const middlewares = [

];

module.exports = function (app) {
  // @ignore
  app.post(
    "/v2/api/lambda/apple/login/mobile",
    middlewares,
    async function (req, res) {
      //This endpoint is for login via IOS APP.
      const sdk = app.get("sdk");
      const config = app.get("configuration");
      try {
        const { first_name, last_name, identityToken, apple_id, role, projectId } =
          req.body;
        const data = jwt.decode(identityToken, { complete: true });
        const kid = data.header.kid;

        const appleSigningKey = await JwtService.getAppleSigningKeys(kid);

        const payload = await JwtService.verifyAppleLogin(
          identityToken,
          appleSigningKey
        );


        if (payload.sub != apple_id) {
          throw new Error("Invalid Identity");
        }
        const user_details = {
          first_name: first_name,
          last_name: last_name,
          email: payload.email,
          apple_id: apple_id,
        };
        const service = new AuthService();

        const { id, is_newuser } = await service.appleLogin(
          sdk,
          projectId,
          user_details,
          identityToken,
          role
        );
        // **Injection Point Start:** Attempt to load and run custom code
        const injectionPath = path.join(__dirname, "..", "custom", project.project_id, "social_callback.js"); // Path to the injection file
        try {
            if (fs.existsSync(injectionPath)) {
                const {apple_callback} = require(injectionPath);
              
                await apple_callback({
                    req,
                    res,
                    project: projectId,
                    parts,
                    projectId,
                    role,
                    needRefreshToken: needRefreshToken ?? false,
                    refreshToken,
                    database,
                    sdk,
                    manaknightSDK,
                    originalUrl,
                    config,
                    appleSigningKey
                  
                });
                if(res.headersSent) return;
            }

        } catch (injectError) {
            console.error("Error during injection:", injectError);
            // Proceed with normal logic even if injection fails.
        }
      // **Injection Point End:**

        let new_jwt = JwtService.createAccessToken(
          {
            user_id: id,
            role: role,
          },
          config.jwt_expire,
          config.jwt_key
        );

        let refreshToken = JwtService.createAccessToken(
          {
            user_id: id,
            role: role,
          },
          config.refresh_jwt_expire,
          config.jwt_key
        );
        let expireDate = new Date();
        expireDate.setSeconds(
          expireDate.getSeconds() + config.refresh_jwt_expire
        );
        await service.saveRefreshToken(
          app.get("sdk"),
          projectId,
          id,
          refreshToken,
          expireDate
        );

        return res.status(200).json({
          error: false,
          role: role,
          access_token: new_jwt,
          refresh_token: refreshToken,
          expire_at: config.access_jwt_expire,
          user_id: id,
        });
      } catch (error) {
        console.log(error);
        return res.status(403).json({
          error: true,
          message: error.message,
        });
      }
    }
  );
  // @ignore_end

  return [
    {
      method: "POST",
      name: "Apple Code API",
      url: "/v2/api/lambda/apple/code",
      successPayload:
        "{error: false, role: 'admin', token: 'jwt token', expire_at: 60, user_id: 1}",
      sucessBody: [{ code: "role", state: "projectId~secret" }],
      needToken: false,
      errors: [
        {
          name: "403",
          body: { key: "state", value: "projectId~secret" },
          response:
            '{"error": true, "failure": "access token", "message": "Something went wrong"}',
        },
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "me", "message": "Something went wrong"}',
        },
      ],
    },
    {
      method: "GET",
      name: "Apple Login API",
      url: "/v2/api/lambda/apple/login",
      successPayload: "['Will redirect to apple login with auth link']",
      queryBody: [{ key: "role", value: "admin" }],
      needToken: false,
      errors: [
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "access token", "message": "Something went wrong"}',
        },
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "me", "message": "Something went wrong"}',
        },
      ],
    },
  ];
};

// API definition for Postman collection
module.exports.getPostmanDefinition = function () {
  return  [
    {
      method: "POST",
      name: "Apple Code API",
      url: "/v2/api/lambda/apple/code",
      successPayload:
        "{error: false, role: 'admin', token: 'jwt token', expire_at: 60, user_id: 1}",
      sucessBody: [{ code: "role", state: "projectId~secret" }],
      needToken: false,
      errors: [
        {
          name: "403",
          body: { key: "state", value: "projectId~secret" },
          response:
            '{"error": true, "failure": "access token", "message": "Something went wrong"}',
        },
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "me", "message": "Something went wrong"}',
        },
      ],
    },
    {
      method: "GET",
      name: "Apple Login API",
      url: "/v2/api/lambda/apple/login",
      successPayload: "['Will redirect to apple login with auth link']",
      queryBody: [{ key: "role", value: "admin" }],
      needToken: false,
      errors: [
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "access token", "message": "Something went wrong"}',
        },
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "me", "message": "Something went wrong"}',
        },
      ],
    },
  ];
};
