const config = require('../../config');
const axios = require('axios').default;


/*
@success status 201 - Created

*/
module.exports.createDomainRecord = async function (name, server = "23.29.118.76") {
    try {
        let result = await axios({
            url: 'https://api.digitalocean.com/v2/domains/manaknightdigital.com/records',
            method: 'POST',
            data:{
                "type": "A",
                "name": name,
                "data": server,
                "priority": null,
                "port": null,
                "ttl": 3600,
                "weight": null,
                "flags": null,
                "tag": null
            },
            headers: {
                'Authorization' : 'Bearer '+ config.digital_ocean.token,
                'Content-Type'  : 'application/json'
            },
            validateStatus: function (status) {
                return status < 500; // Resolve only if the status code is less than 500
            }
        })

        return result;
    } catch(err) {
        throw new Error(err.message);
    }
}

// @success status 204 No Content
module.exports.deleteDomainRecord = async function (id) {
    try {
        let result = await axios({
            url: 'https://api.digitalocean.com/v2/domains/manaknightdigital.com/records/'+id,
            method: 'DELETE',
            data:{},
            headers: {
                'Authorization' : 'Bearer '+ config.digital_ocean.token,
                'Content-Type'  : 'application/json'
            }
        })

        return result;
    } catch(err) {
        throw new Error(err.message);
    }
}

module.exports.getDomainByName = async function (name, type = "A", root = "manaknightdigital.com") {
    try {
        let result = await axios({
            url: `https://api.digitalocean.com/v2/domains/manaknightdigital.com/records?type=${type}&name=${name}.${root}`,
            method: 'GET',
            data:{},
            headers: {
                'Authorization' : 'Bearer '+ config.digital_ocean.token,
                'Content-Type'  : 'application/json'
            },
            validateStatus: function (status) {
                return status < 500; // Resolve only if the status code is less than 500
            }
        })

        return result;
    } catch(err) {
        throw new Error(err.message);
    }
}