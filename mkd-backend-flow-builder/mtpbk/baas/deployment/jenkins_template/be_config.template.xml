<?xml version='1.1' encoding='UTF-8'?>
<project>
  <description>{{{project}}} backend {{{branch}}}</description>
  <keepDependencies>false</keepDependencies>
  <properties>
    <jenkins.model.BuildDiscarderProperty>
      <strategy class="hudson.tasks.LogRotator">
        <daysToKeep>1</daysToKeep>
        <numToKeep>1</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
      </strategy>
    </jenkins.model.BuildDiscarderProperty>
  </properties>
  <scm class="hudson.scm.NullSCM"/>
  <canRoam>true</canRoam>
  <disabled>false</disabled>
  <blockBuildWhenDownstreamBuilding>false</blockBuildWhenDownstreamBuilding>
  <blockBuildWhenUpstreamBuilding>false</blockBuildWhenUpstreamBuilding>
  <triggers/>
  <concurrentBuild>false</concurrentBuild>
  <builders/>
  <publishers/>
  <buildWrappers>
    <org.jvnet.hudson.plugins.SSHBuildWrapper plugin="ssh@2.6.1">
      <siteName>deploy@***************:22</siteName>
      <preScript>cd ~/mkd-backend-flow-builder/mtpbk/custom/{{{project}}}_backend;
echo &quot;pull&quot;;
echo -e &quot;Bs)F&lt;T9Jc]zJtZE+#8nPQ!@\n&quot; | sudo -S git switch {{{branch}}}
echo -e &quot;Bs)F&lt;T9Jc]zJtZE+#8nPQ!@\n&quot; | sudo -S git pull origin {{{branch}}};
echo -e &quot;Bs)F&lt;T9Jc]zJtZE+#8nPQ!@\n&quot; | sudo -S pm2 restart baas-v5;

# Sleep for 20 seconds to give time for PM2 to restart the application
sleep 20;

# Define the URL to check
URL=&quot;https://baas.mytechpassport.com&quot;;

# Make a GET request to the URL and capture the HTTP status code
RESPONSE_CODE=$(curl -s -o /dev/null -w &quot;%{http_code}&quot; &quot;$URL&quot;);

# Check the response code
if [ &quot;$RESPONSE_CODE&quot; -eq 200 ]; then
    echo &quot;The URL is accessible and returned a 200 status code.&quot;;
else
    echo &quot;There might be an issue. The response status code is $RESPONSE_CODE&quot;;
    # Git reset
    echo -e &quot;Bs)F&lt;T9Jc]zJtZE+#8nPQ!@\n&quot; | sudo -S git reset --hard HEAD^;
fi
echo &quot;done&quot;;</preScript>
      <postScript></postScript>
      <hideCommand>false</hideCommand>
    </org.jvnet.hudson.plugins.SSHBuildWrapper>
  </buildWrappers>
</project>