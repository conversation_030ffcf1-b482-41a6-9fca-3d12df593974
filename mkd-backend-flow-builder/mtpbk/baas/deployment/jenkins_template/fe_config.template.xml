<?xml version='1.1' encoding='UTF-8'?>
<project>
  <actions/>
  <description>{{{project}}} frontend {{{branch}}} </description>
  <keepDependencies>false</keepDependencies>
  <properties>
    <jenkins.model.BuildDiscarderProperty>
      <strategy class="hudson.tasks.LogRotator">
        <daysToKeep>1</daysToKeep>
        <numToKeep>1</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
      </strategy>
    </jenkins.model.BuildDiscarderProperty>
  </properties>
  <scm class="hudson.scm.NullSCM"/>
  <canRoam>true</canRoam>
  <disabled>false</disabled>
  <blockBuildWhenDownstreamBuilding>false</blockBuildWhenDownstreamBuilding>
  <blockBuildWhenUpstreamBuilding>false</blockBuildWhenUpstreamBuilding>
  <triggers/>
  <concurrentBuild>false</concurrentBuild>
  <builders>
    <org.jvnet.hudson.plugins.SSHBuilder plugin="ssh@2.6.1">
      <siteName>deploy@************:22</siteName>
      <command>cd {{{project}}}_frontend/;
echo &quot;pull&quot;;
echo -e &quot;ln8kmyhfezQ4kjyngk2cgA\n&quot; | sudo -S git switch {{{branch}}}
echo -e &quot;ln8kmyhfezQ4kjyngk2cgA\n&quot; | sudo -S git pull origin {{{branch}}}
echo -e &quot;ln8kmyhfezQ4kjyngk2cgA\n&quot; | sudo -S chmod +x deploy.sh
echo -e &quot;ln8kmyhfezQ4kjyngk2cgA\n&quot; | sudo -S ./deploy.sh
cd ../;
echo &quot;done&quot;;</command>
      <execEachLine>false</execEachLine>
      <hideCommand>false</hideCommand>
    </org.jvnet.hudson.plugins.SSHBuilder>
  </builders>
  <publishers/>
  <buildWrappers>
    <hudson.plugins.timestamper.TimestamperBuildWrapper plugin="timestamper@1.21"/>
  </buildWrappers>
</project>