const config = require('../../config');
const axios = require("axios").default;
const fs = require("fs");
const path = require("path");
const FormData = require("form-data");

module.exports.createFrontendJob = async function (project, branch) {
  try {
    let location = generateFrontendTemplate(project, branch);

    let [crumb, crumbHeaderName] = await getJenkinsCrumb();
    let configFile = fs.readFileSync(location);
    let url = `http://23.29.118.76:8080/createItem?name=${project}_frontend`;
    let res = await axios({
      url: url,
      method: "POST",
      data: configFile,
      headers: {
        "Jenkins-Crumb": crumb,
        "Content-Type": "text/xml"
      },
      auth: {
        username: config.jenkins.user,
        password: config.jenkins.token
      },
      validateStatus: function (status) {
        return status < 500; // Resolve only if the status code is less than 500
      }
    });

    fs.unlinkSync(location);
    return res;
  } catch (error) {
    console.log("error :>> ", error);
    throw new Error("Failed to create job");
  }
};

module.exports.createBackendJob = async function (project, branch) {
  try {
    let location = generateBackendTemplate(project, branch);

    let [crumb, crumbHeaderName] = await getJenkinsCrumb();

    let configFile = fs.readFileSync(location);
    let url = `http://23.29.118.76:8080/createItem?name=${project}_backend`;
    let res = await axios({
      url: url,
      method: "POST",
      data: configFile,
      headers: {
        "Jenkins-Crumb": crumb,
        "Content-Type": "text/xml"
      },
      auth: {
        username: config.jenkins.user,
        password: config.jenkins.token
      },
      validateStatus: function (status) {
        return status < 500; // Resolve only if the status code is less than 500
      }
    });

    fs.unlinkSync(location);
    return res;
  } catch (error) {
    console.log("error :>> ", error);
    throw new Error(error);
  }
};

const generateFrontendTemplate = (project, branch = "master") => {
  let configTemplate = fs.readFileSync(path.resolve(__dirname, "jenkins_template", "fe_config.template.xml"), "utf8");
  configTemplate = configTemplate.replaceAll("{{{project}}}", project);
  configTemplate = configTemplate.replaceAll("{{{branch}}}", branch);

  let location = path.resolve(__dirname, "jenkins_template", `${project}_fe.xml`);
  fs.writeFileSync(location, configTemplate);

  return location;
};

const generateBackendTemplate = (project, branch = "master") => {
  let configTemplate = fs.readFileSync(path.resolve(__dirname, "jenkins_template", "be_config.template.xml"), "utf8");
  configTemplate = configTemplate.replaceAll("{{{project}}}", project);
  configTemplate = configTemplate.replaceAll("{{{branch}}}", branch);

  let location = path.resolve(__dirname, "jenkins_template", `${project}_be.xml`);
  fs.writeFileSync(location, configTemplate);

  return location;
};

async function getJenkinsCrumb() {
  try {
    let res = await axios({
      url: "http://23.29.118.76:8080/crumbIssuer/api/json",
      method: "GET",
      auth: {
        username: config.jenkins.user,
        password: config.jenkins.token
      }
    });
    return [res.data.crumb, res.data.crumbRequestField];
  } catch (error) {
    throw new Error("Failed to get jenkins crumb");
  }
}
