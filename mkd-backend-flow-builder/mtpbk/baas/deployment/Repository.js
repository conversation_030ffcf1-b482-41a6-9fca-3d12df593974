const config = require('../../config');
const axios = require('axios').default;

const org = 'mkdlabs';

module.exports.createRepo = async function (name) {
    try {
        let res = await axios({
            url: `http://23.29.118.76:3000/api/v1/org/${org}/repos`,
            method: 'POST',
            data: {
                "name": name,
                "default_branch": "master",
                // "auto_init": true,
                // "readme": "",
                // "private"       : true,
            },
            headers: {
                'Authorization': 'Bearer ' + config.git.token,
                'Content-Type': 'application/json'
            },
            validateStatus: function (status) {
                return status < 500; // Resolve only if the status code is less than 500
            }
        });

        return res;
    } catch (err) {
        throw new Error(err.message);
    }
}

module.exports.addDevTeam = async function (name) {
    try {
        const devTeamID = 5;
        let res = await axios({
            url: `http://23.29.118.76:3000/api/v1/teams/${devTeamID}/repos/${org}/${name}`,
            method: 'PUT',
            data: {},
            headers: {
                'Authorization': 'Bearer ' + config.git.token,
                'Content-Type': 'application/json'
            },
            validateStatus: function (status) {
                return status < 500; // Resolve only if the status code is less than 500
            }
        });

        return res;
    } catch (err) {
        throw new Error(err.message);
    }
}


const createBranch = async function (repo, branch) {
    try {
        const devTeamID = 5;
        let res = await axios({
            url: `http://23.29.118.76:3000/api/v1/repos/${org}/${repo}/branches`,
            method: 'POST',
            data: {
                new_branch_name: branch,
                old_branch_name: "master"
            },
            headers: {
                'Authorization': 'Bearer ' + config.git.token,
                'Content-Type': 'application/json'
            },
            validateStatus: function (status) {
                return status < 500; // Resolve only if the status code is less than 500
            }
        });

        return res;
    } catch (err) {
        throw new Error(err.message);
    }
}
module.exports.createBranch = createBranch;

module.exports.listRepositoryBranches = async function (repo) {
    try {
        let res = await axios({
            url: `http://23.29.118.76:3000/api/v1/repos/${org}/${repo}/branches`,
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + config.git.token,
                'Content-Type': 'application/json'
            },
            validateStatus: function (status) {
                return status < 500; // Resolve only if the status code is less than 500
            }
        });

        return res;
    } catch (err) {
        throw new Error(err.message);
    }
}

const commitFileToRepo = async function (repo, filePath, base64EncodedFileContent, branch = "master", commitMsg = "baas commit") {
    try {


        const authorName = 'manaknight';
        const authorEmail = '<EMAIL>';

        let res = await axios({
            url: `http://23.29.118.76:3000/api/v1/repos/${org}/${repo}/contents/${filePath}`,
            method: 'POST',
            data: {
                message: commitMsg,
                content: base64EncodedFileContent,
                branch: branch,
                author: {
                    name: authorName,
                    email: authorEmail
                }
            },
            headers: {
                'Authorization': 'Bearer ' + config.git.token,
                'Content-Type': 'application/json'
            },
            validateStatus: function (status) {
                return status < 500; // Resolve only if the status code is less than 500
            }
        });

        return res;
    } catch (err) {
        throw new Error(err.message);
    }
}


module.exports.commitFileToRepo = commitFileToRepo;

module.exports.commitFilesToRepo = async function (repo, files, branch = "master", commitMsg = "baas commit") {
    try {

        const responseCollection = [];
        let result = null;
        result = await createBranch(repo, branch);
        responseCollection.push(result);

        for (let index = 0; index < files.length; index++) {
            const element = files[index];
            result = await commitFileToRepo(repo, element.path, element.content, branch, commitMsg)
            // responseCollection.push(result);
        }
        console.log('responseCollection :>> ', responseCollection);
        return responseCollection;
    } catch (err) {
        throw new Error(err.message);
    }
}