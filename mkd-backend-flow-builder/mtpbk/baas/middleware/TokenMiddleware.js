const JwtService = require("../services/JwtService");

// Middleware function to handle token verification
module.exports = function (options) {
  // Returns a middleware function
  return function (req, res, next) {
    // Retrieve the token from the request
    const token = JwtService.getToken(req);

    // Check if the token is missing
    if (!token) {
      return res.status(401).json({
        error: true,
        message: "UNAUTHORIZED",
        code: "UNAUTHORIZED",
      });
    } else {
      // Get the configuration from the app
      const config = req.app.get("configuration");

      // Verify the access token
      const result = JwtService.verifyAccessToken(
        token,
        config.jwt_key,
        options
      );

      // Check if the token verification failed
      if (!result) {
        return res.status(401).json({
          error: true,
          message: "TOKEN_EXPIRED",
          code: "TOKEN_EXPIRED",
        });
      }

      // Attach user ID and role to the request object
      req.user_id = result.user_id;
      req.role = result.role;

      // Proceed to the next middleware
      next();
    }
  };
};
