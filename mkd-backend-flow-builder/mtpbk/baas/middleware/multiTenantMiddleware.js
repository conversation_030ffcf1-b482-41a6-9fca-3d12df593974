// Middleware to check if the user is a company admin
exports.checkUserCompanyAdmin = (app, projectId) => async (req, res, next) => {
  try {
    let sdk = app.get("sdk");
    // Set the project ID for the SDK
    sdk.setProjectId(projectId);
    // Initialize the database connection
    sdk.getDatabase();
    // Set the table to "user" to retrieve user information
    sdk.setTable("user");

    const { user_id } = req;
    let { id } = req.params;
    // If no ID is provided in the parameters, check the request body
    if (!id) {
      id = req.body.id;
    }

    console.log(user_id)

    // Fetch the user details from the database
    const user = (await sdk.find("user", { id: user_id }))[0];
    // If the user is not found, return a 404 error
    if (!user) {
      return res.status(404).json({ error: true, message: "User not found" });
    }
    // Set the table to "company_admin" to check admin status
    sdk.setTable("company_admin");

    // Check if the user is a company admin for the specified company
    const is_company_admin = (await sdk.find("company_admin", { user_id, company_id: id }))[0];
    // Set the table to "company" to retrieve company information
    sdk.setTable("company");
    // Fetch the company details from the database
    const company = (await sdk.find("company", { id }))[0];
    // If the company is not found, return a 403 error
    if (!company) {
      return res.status(403).json({ error: true, message: "Invalid company id" });
    }

    // Check if the user is not the owner and not an admin, return a 403 error if so
    if (company.owner_id != user_id && !is_company_admin && req.role != "admin" && req.role != "super_admin") {
      return res.status(403).json({ error: true, message: "You are not allowed to perform this action" });
    }

    // Proceed to the next middleware if all checks pass
    next();
  } catch (e) {
    // Handle any errors that occur during the process
    res.status(401).json({ error: true, code: 401, message: "Something went wrong" });
  }
};
