class BaseRole {
  /**
   * Static permissions object to define role-based access control
   * @type {Object}
   */
  static permissions = {};

  /**
   * Checks if authentication is required for this role
   * @returns {boolean} Whether authentication is mandatory
   */
  static isAuthRequired() {
    return this.permissions.authRequired;
  }

  /**
   * Validates route access for the current role
   * @param {string} routeId - Unique identifier for the route
   * @returns {boolean} Whether the route is accessible
   * @throws {Error} Throws a 401 Forbidden Access error if route is not permitted
   */
  static hasRouteAccess(routeId) {
    // Check if routes are defined
    if (!this.permissions.routes || !this.permissions.routes.length) {
      return false;
    }

    // Check if specific route is allowed
    if (!this.permissions.routes.includes(routeId)) {
      const error = new Error('Forbidden Access');
      error.statusCode = 401;
      throw error;
    }
    return true;
  }

  // User Management Permission Methods
  /**
   * Checks if the role can create new users
   * @returns {boolean} Whether user creation is allowed
   */
  static canCreateUsers() {
    return !!this.permissions.canCreateUsers;
  }

  /**
   * Checks if the role can edit existing users
   * @returns {boolean} Whether user editing is allowed
   */
  static canEditUsers() {
    return !!this.permissions.canEditUsers;
  }

  /**
   * Checks if the role can delete users
   * @returns {boolean} Whether user deletion is allowed
   */
  static canDeleteUsers() {
    return !!this.permissions.canDeleteUsers;
  }
  
  /**
   * Checks if the role can verify user emails
   * @returns {boolean} Whether email verification is allowed
   */
  static canVerifyEmail() {
    return !!this.permissions.canVerifyEmail;
  }

  /**
   * Checks if the role can manage user roles
   * @returns {boolean} Whether role management is allowed
   */
  static canManageRoles() {
    return !!this.permissions.canManageRoles;
  }

  /**
   * Checks if the role can set permissions
   * @returns {boolean} Whether permission setting is allowed
   */
  static canSetPermissions() {
    return !!this.permissions.canSetPermissions;
  }

  /**
   * Checks if the role can update other users' information
   * @returns {boolean} Whether updating other users is allowed
   */
  static canUpdateOtherUsers() {
    return !!this.permissions.canUpdateOtherUsers;
  }

  // Authentication Permission Methods
  /**
   * Checks if login is allowed for this role
   * @returns {boolean} Whether login is permitted
   */
  static canLogin() {
    return !!this.permissions.canLogin;
  }

  /**
   * Checks if user preference management is allowed
   * @returns {boolean} Whether preference management is permitted
   */
  static canPreference() {
    return !!this.permissions.canPreference;
  }

  /**
   * Checks if profile access is allowed
   * @returns {boolean} Whether profile access is permitted
   */
  static canProfile() {
    return !!this.permissions.canProfile;
  }

  /**
   * Checks if user registration is allowed
   * @returns {boolean} Whether registration is permitted
   */
  static canRegister() {
    return !!this.permissions.canRegister;
  }
  
  /**
   * Checks if password update is allowed
   * @returns {boolean} Whether password update is permitted
   */
  static canUpdatePassword() {
    return !!this.permissions.canUpdatePassword;
  }

  /**
 * Checks if password update is allowed
 * @returns {boolean} Whether password update is permitted
 */
  static canUpdateEmail() {
    return !!this.permissions.canUpdateEmail;
  }


  /**
   * Checks if forgot password functionality is allowed
   * @returns {boolean} Whether forgot password is permitted
   */
  static canForgot() {
    return !!this.permissions.canForgot;
  }

  /**
   * Checks if password reset is allowed
   * @returns {boolean} Whether password reset is permitted
   */
  static canReset() {
    return !!this.permissions.canReset;
  }

  // Social Login Permission Methods
  /**
   * Checks if Google login is allowed
   * @returns {boolean} Whether Google login is permitted
   */
  static canGoogleLogin() {
    return !!this.permissions.canGoogleLogin;
  }

  /**
   * Checks if Apple login is allowed
   * @returns {boolean} Whether Apple login is permitted
   */
  static canAppleLogin() {
    return !!this.permissions.canAppleLogin;
  }

  /**
   * Checks if Microsoft login is allowed
   * @returns {boolean} Whether Microsoft login is permitted
   */
  static canMicrosoftLogin() {
    return !!this.permissions.canMicrosoftLogin;
  }

  /**
   * Checks if Magic Link login is allowed
   * @returns {boolean} Whether Magic Link login is permitted
   */
  static canMagicLinkLogin() {
    return !!this.permissions.canMagicLinkLogin;
  }

  /**
   * Checks if Two-Factor Authentication is required
   * @returns {boolean} Whether 2FA is needed
   */
  static needs2FA() {
    return !!this.permissions.needs2FA;
  }

  // TreeQL Permission Methods
  /**
   * Checks if TreeQL access is enabled for this role
   * @returns {boolean} Whether TreeQL is accessible
   */
  static hasTreeQLAccess() {
    return !!this.permissions.treeql?.enabled;
  }

  /**
   * Checks if a specific model can be accessed
   * @param {string} modelName - Name of the model to check
   * @returns {boolean} Whether the model is accessible
   */
  static canAccessModel(modelName) {
    return !!this.permissions.treeql?.models?.[modelName]?.allowed;
  }

  /**
   * Retrieves blacklisted fields for a specific model
   * @param {string} modelName - Name of the model
   * @returns {string[]} List of blacklisted fields
   */
  static getModelBlacklistedFields(modelName) {
    return this.permissions.treeql?.models?.[modelName]?.blacklistedFields || [];
  }

  /**
   * Checks if a specific operation is allowed on a model
   * @param {string} modelName - Name of the model
   * @param {string} operation - Type of operation to check
   * @returns {boolean} Whether the operation is permitted
   */
  static canPerformModelOperation(modelName, operation) {
    return !!this.permissions.treeql?.models?.[modelName]?.operations?.[operation];
  }
}

module.exports = BaseRole; 