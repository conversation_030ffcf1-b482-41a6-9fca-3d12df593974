const DatabaseService = require('./DatabaseService');

class BackendSDK extends DatabaseService {
    /**
     * Constructor for BackendSDK
     * Initializes database connection and project-specific settings
     * @param {Object} dbConnector - Database connector instance
     */
    constructor(dbConnector) {
        super(dbConnector); // Call the parent constructor
        // Define fields for database and project configuration
        this._database = null;     // Stores the database name
        this._dbConnection = null; // Stores the active database connection
        this._table = null;        // Stores the current table name
        this._projectId = null;    // Stores the current project identifier
        this._secret = null;       // Stores project-specific secret
    }

    /**
     * Sets the database name for the current connection
     * @param {string} database - Name of the database
     * @returns {void}
     */
    setDatabase(database) {
        this._database = database;
    }

    /**
     * Retrieves the current database connection
     * @returns {Object} Current database connection
     */
    getDatabase() {
        return this._dbConnection;
    }

    /**
     * Sets the current table with project-specific namespace
     * @param {string} table - Name of the table
     * @returns {BackendSDK} Current SDK instance for method chaining
     */
    setTable(table) {
        this._table = this._database + "." + this._projectId + "_" + table;
        return this;
    }

    /**
     * Alternative method to set the current table (alias for setTable)
     * @param {string} table - Name of the table
     * @returns {BackendSDK} Current SDK instance for method chaining
     */
    table(table) {
        this._table = this._database + "." + this._projectId + "_" + table;
        return this;
    }

    /**
     * Retrieves the current table name
     * @returns {string} Current table name
     */
    getTable() {
        return this._table;
    }

    /**
     * Sets the project identifier for namespacing
     * @param {string} projectId - Unique identifier for the project
     * @returns {BackendSDK} Current SDK instance for method chaining
     */
    setProjectId(projectId) {
        this._projectId = projectId;
        return this;
    }

    /**
     * Alternative method to set project identifier (alias for setProjectId)
     * @param {string} projectId - Unique identifier for the project
     * @returns {BackendSDK} Current SDK instance for method chaining
     */
    projectId(projectId) {
        this._projectId = projectId;
        return this;
    }

    /**
     * Retrieves the current project identifier
     * @returns {string} Current project identifier
     */
    getProjectId() {
        return this._projectId;
    }

    /**
     * Sets the project-specific secret key
     * @param {string} secret - Secret key for authentication or encryption
     */
    setSecret(secret) {
        this._secret = secret;
    }

    /**
     * Retrieves the project-specific secret key
     * @returns {string} Project secret key
     */
    getSecret() {
        return this._secret;
    }

    /**
     * Creates a new record in the specified collection
     * @param {string} collection - Name of the collection/table
     * @param {Object} data - Data to be inserted
     * @returns {Promise<Object>} Inserted record
     */
    async create(collection, data) {
        collection = this._projectId + "_" + collection;
        return await super.create(collection, data);
    }

    /**
     * Finds records in the specified collection matching the query
     * @param {string} collection - Name of the collection/table
     * @param {Object} [query={}] - Search query parameters
     * @param {Object} [options={}] - Additional query options
     * @returns {Promise<Array>} Array of matching records
     */
    async find(collection, query = {}, options = {}) {
        collection = this._projectId + "_" + collection;
        return await super.find(collection, query, options);
    }

    /**
     * Finds a single record by its unique identifier
     * @param {string} collection - Name of the collection/table
     * @param {string|number} id - Unique identifier of the record
     * @returns {Promise<Object>} Matching record
     */
    async findById(collection, id) {
        collection = this._projectId + "_" + collection;
        return await super.findById(collection, id);
    }

    /**
     * Finds the first record matching the query
     * @param {string} collection - Name of the collection/table
     * @param {Object} query - Search query parameters
     * @returns {Promise<Object>} First matching record
     */
    async findOne(collection, query) {
        collection = this._projectId + "_" + collection;
        return await super.findOne(collection, query);
    }

    /**
     * Updates records matching the query
     * @param {string} collection - Name of the collection/table
     * @param {Object} query - Search query to identify records
     * @param {Object} update - Update data
     * @returns {Promise<Object>} Update result
     */
    async update(collection, query, update) {
        collection = this._projectId + "_" + collection;
        return await super.update(collection, query, update);
    }

    /**
     * Updates a single record by its unique identifier
     * @param {string} collection - Name of the collection/table
     * @param {string|number} id - Unique identifier of the record
     * @param {Object} update - Update data
     * @returns {Promise<Object>} Update result
     */
    async updateById(collection, id, update) {
        collection = this._projectId + "_" + collection;
        return await super.updateById(collection, id, update);
    }

    /**
     * Deletes records matching the query
     * @param {string} collection - Name of the collection/table
     * @param {Object} query - Search query to identify records
     * @returns {Promise<Object>} Delete result
     */
    async delete(collection, query) {
        collection = this._projectId + "_" + collection;
        return await super.delete(collection, query);
    }

    /**
     * Deletes a single record by its unique identifier
     * @param {string} collection - Name of the collection/table
     * @param {string|number} id - Unique identifier of the record
     * @returns {Promise<Object>} Delete result
     */
    async deleteById(collection, id) {
        collection = this._projectId + "_" + collection;
        return await super.deleteById(collection, id);
    }

    /**
     * Counts the number of records matching the query
     * @param {string} collection - Name of the collection/table
     * @param {Object} [query={}] - Search query parameters
     * @returns {Promise<number>} Number of matching records
     */
    async count(collection, query = {}) {
        collection = this._projectId + "_" + collection;
        return await super.count(collection, query);
    }

    /**
     * Executes a raw SQL query with optional parameters
     * @param {string} query - SQL query string
     * @param {Array} [params=[]] - Query parameters
     * @returns {Promise<Array>} Query result
     */
    async rawQuery(query, params = []) {
       // collection = this._projectId + "_" + collection;
        return await super.rawQuery(query, params);
    }

    /**
     * Sanitizes input to prevent SQL injection
     * @param {*} input - Input to be sanitized
     * @returns {*} Sanitized input
     */
    sanitize(input) {
        return super.sanitize(input);
    }

    /**
     * Prepares a safe query object by sanitizing inputs
     * @param {Object} queryObject - Query object to be sanitized
     * @returns {Object} Sanitized query object
     */
    prepareSafeQuery(queryObject) {
        return super.prepareSafeQuery(queryObject);
    }
}

module.exports = BackendSDK;
