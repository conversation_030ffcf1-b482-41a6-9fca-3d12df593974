module.exports = class DBUtil {
    
    constructor(sdk) {
        this.sdk = sdk;
        this.projectId = sdk.getProjectId();
        this.HAS = 1;
        this.BELONGS_TO = 2;
    }

    getProjectId() {
        return this.projectId;
    }

    async getProjectSchema() {
        let schema = [];
        const sql = `select a.table_name, b.column_name, b.data_type
        from information_schema.tables a
        INNER JOIN information_schema.columns b
        ON a.table_name = b.table_name
        where a.table_type = 'BASE TABLE' and a.table_name LIKE "${this.getProjectId()}_%"`;

        try {
            schema = await this.sdk.rawQuery(sql);
        } catch (error) {
            throw new Error(error.message);
        }
        return schema;
    }

    async getProjectTables() {
        let schema = [];
        const sql = `
        select a.table_name
        from information_schema.tables a
        where a.table_type = 'BASE TABLE' and a.table_name LIKE "${this.getProjectId()}_%"
        `;

        try {
            schema = await this.sdk.rawQuery(sql);
        } catch (error) {
            throw new Error(error.message);
        }
        return schema;
    }

    async getTableSchema(table) {
        let schema = [];
        if (typeof table == "undefined") return schema;
        const sql = `select table_name, column_name 
        from information_schema.columns 
        where table_name LIKE "${this.getProjectId()}_${table}"`;

        try {
             schema = await this.sdk.rawQuery(sql);
        } catch (error) {
            throw new Error(error.message);
        }
        return schema;
    }

    async guessRelationInfo(subject, relation, foreignKey) {
        try {
            let relationship = [];
            // TODO Deep Joins
            // if (relation.split(",").length == 2) {
            //     let relationTokens = relation.split(",");

            //     relationship = [...relationship, ...(await this.guessRelationInfo(relationTokens[0], relationTokens[1]))]

            //     relation = relationTokens[0];
            // }
            let subjectSchema = await this.getTableSchema(subject);
            let relationSchema = await this.getTableSchema(relation);

            let subjectTable = `${this.getProjectId()}_${subject}`;
            let relationTable = `${this.getProjectId()}_${relation}`;

            // Find Belongs to
            for (let column of subjectSchema) {
                if ( typeof column['COLUMN_NAME'] !== "undefined") column['column_name'] = column['COLUMN_NAME'];
                
                if ( typeof column['TABLE_NAME'] !== "undefined") column['table_name'] = column['TABLE_NAME'];
                
                if (column.column_name == `${relation}_id` ) {
                    relationship
                        .push({type: this.BELONGS_TO, column, subject, relation, subjectTable, relationTable, subjectSchema, relationSchema});
                    break;
                }

                if (column.column_name == `${relation.slice(0, -1)}_id` /* remove last character */)  {
                    relationship
                    .push({type: this.BELONGS_TO, column, subject, relation, subjectTable, relationTable,  subjectSchema, relationSchema});
                    break;
                }

                if (column.column_name == foreignKey ) {
                    relationship
                        .push({type: this.BELONGS_TO, column, subject, relation, subjectTable, relationTable, subjectSchema, relationSchema});
                    break;
                }
            }

            // Has one or many
            for (let column of relationSchema) {
                if ( typeof column['COLUMN_NAME'] !== "undefined") column['column_name'] = column['COLUMN_NAME'];
                
                if ( typeof column['TABLE_NAME'] !== "undefined") column['table_name'] = column['TABLE_NAME'];
                
                if (column.column_name == `${subject}_id`) {
                    relationship.push({type: this.HAS, column, subject, relation, subjectTable, relationTable,   subjectSchema, relationSchema});
                    break;
                }
                if (column.column_name == `${subject.slice(0, -1)}_id`) {
                    relationship.push({type: this.HAS, column, subject, relation, subjectTable, relationTable,  subjectSchema, relationSchema });
                    break;
                }
                if (column.column_name == foreignKey) {
                    relationship.push({type: this.HAS, column, subject, relation, subjectTable, relationTable,   subjectSchema, relationSchema});
                    break;
                }
            }

            // Many to many

            // let projectTables = await this.getProjectTables();

            // let intermediateTable = projectTables.filter( table => {
            //     // author c
            //     return (
            //         table.table_name == `${this.getProjectId()}_${subject}_${relation}` ||
            //         table.table_name == `${this.getProjectId()}_${relation}_${subject}` 
            //         )
            // })

            // if (intermediateTable.length > 0) {
            //     let intermediateSchema = await this.getTableSchema(intermediateTable[0].table_name);
            // }


            
            return relationship;
        } catch (error) {
            throw new Error(error.message);
        }

    }


  
}