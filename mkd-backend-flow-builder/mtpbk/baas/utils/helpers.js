const crypto = require('crypto');

const stringChecker = {
    isDataValid: data => {
        let isValid = false;
        if (data !== undefined && data !== null) isValid = true;
        return isValid;
    },
    isStringNonEmpty: data => {

        let isNonEmpty = true;
        data.forEach( string => {
            if (stringChecker.isDataValid(string)) {
                if (string === '') isNonEmpty = false;
            }else{
                isNonEmpty = false;
            }
        })
        return isNonEmpty;
    }
};
const dateChecker = {
    isValidDate: (d) => {
        return d instanceof Date && !isNaN(d);
      }
};

const BackendSDK = require("../core/BackendSDK");
const config = require('./../config');

const getBackendSDK = async function (projectId) {
    
   let projectDB = config.databaseName;
    

    let sdk = new BackendSDK();
    sdk.setDatabase(projectDB)

    if(config.env == 'production') {
      sdk.setDatabase(config.databaseName);
    }
    else {
      

      try {
        await req.sdk.rawQuery(`select * from ${req.projectId}_permission`);
      }
      catch(err){
          // console.log(err);
          projectDB =`baas_${projectId}`;
      }
      
      sdk.setDatabase(projectDB);
    }
    return sdk;

};

const generateValidKey = (encryptionKey) => {
    const keyLength = 32; // Key length for AES-256-CBC
    const keyBuffer = Buffer.alloc(keyLength);
    const inputKeyBuffer = Buffer.from(encryptionKey);
  
    for (let i = 0; i < keyBuffer.length; i++) {
      keyBuffer[i] = inputKeyBuffer[i % inputKeyBuffer.length];
    }
  
    return keyBuffer;
}
  
  const encrypt = (value, key) => {
    const iv = crypto.randomBytes(16); 
    const cipherKey = generateValidKey(key);
    const cipher = crypto.createCipheriv('aes-256-cbc', cipherKey, iv);
    let encryptedValue = cipher.update(value, 'utf-8', 'hex');
    encryptedValue += cipher.final('hex');
    return JSON.stringify({
      iv: iv.toString('hex'),
      encryptedValue: encryptedValue
    });
  }
  
  const decrypt = (encryptedData, key) => {
    encryptedData = JSON.parse(encryptedData)
    const decipherKey = generateValidKey(key);
    const decipher = crypto.createDecipheriv('aes-256-cbc', decipherKey, Buffer.from(encryptedData.iv, 'hex'));
    let decryptedValue = decipher.update(encryptedData.encryptedValue, 'hex', 'utf-8');
    decryptedValue += decipher.final('utf-8');
    return decryptedValue;
  }

const getApiKeys = async function (sdk, projectId) {
    try {
      sdk.getDatabase();
      sdk.setProjectId(projectId);
      sdk.setTable("api_keys");
  
      const configs = await sdk.get();
      const apiKeys = configs.reduce((a, c) => {
        try {
            a[c.key] = decrypt(c.value, projectId);
            return a;
        } catch (error) {
            a[c.key] = "";
            return a;
        }
      }, {});
  
     return apiKeys;
    } catch (error) {
      return {}
    }
  };


module.exports = { stringChecker, dateChecker, getBackendSDK, getApiKeys, decrypt};