const fs = require("fs");
const path = require("path");
const JSON5 = require("json5");
const RoleTemplateSDK = require("./templates/RoleTemplateSDK");
const TreeSDKTemplate = require("./templates/TreeSDKTemplate");
const TemplateSDK = require("./templates/TemplateSDK");

const buildApiSdk = (config,projectName) => {
  let sdkRoutes = [];
  const routes = config.routes.map((route) => ({
    ...route,
    method: route.method ?? route.flowData?.nodes[0]?.data?.method,
    url: route.url ?? route.flowData?.nodes[0]?.data?.path,
    successBody: generateJsonBody(route.flowData?.nodes[0]?.data?.fields || []),
    successPayload: generateJsonBody(
      route.flowData?.nodes[0]?.data?.responseFields || []
    ),
    errors: [],
    needToken: route.flowData?.nodes[0]?.data?.authType == "bearer"
  }));

  routes.forEach((route) => {
    sdkRoutes.push({
      name: route.name,
      role: "mkd",
      content: generateFunctions(route)
    });
  });

  return sdkRoutes;
};

const buildLambdaSdk = (config, projectName) => {
  // TODO: loop through every
  let sdkRoutes = [];
  const routes = [];

  const lambdaPath = path.join(__dirname, "baas", "lambda");
  if (fs.existsSync(lambdaPath)) {
    // Read files in lambda directory
    fs.readdirSync(lambdaPath).forEach(function (file) {
      // Skip non-JavaScript files and index.js
      if (file === "index.js" || path.extname(file) !== ".js") {
        return;
      }

      // Require the lambda file
      const name = path.basename(file, ".js");
      try {
        if (!fs.existsSync(path.join(lambdaPath, name) + ".js")) {
          return;
        }

        const fileContent = fs.readFileSync(
          path.join(lambdaPath, name) + ".js",
          "utf8"
        );
        const match = fileContent.match(/return \[(.*?)\];/s);
        if (!match) {
          return;
        }
        const routeDefinition = JSON5.parse(`[${match[1]}]`);
        if (Array.isArray(routeDefinition)) {
          routes.push(...routeDefinition);
        }
      } catch (error) {
        return;
      }
    });
  }

  let templateRoutes = routes.filter((obj) => obj?.url?.includes("wxy"));
  let regularRoutes = routes.filter((obj) => !obj?.url?.includes("wxy"));

  for (const role of config.roles) {
    templateRoutes.forEach((route) => {
      sdkRoutes.push({
        name: route.name,
        role: role.slug,
        content: generateFunctions(
          route,
          role.slug,
          projectName
        )
      });
    });
  }
  regularRoutes.forEach((route) => {
    sdkRoutes.push({
      name: route.name,
      role: "mkd",
      content: generateFunctions(route)
    });
  });

  return sdkRoutes;
};

function generateFunctions(obj, role, project) {
  const functionName = obj?.name
    ?.split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join("");

  const nameParts = role ? role.split("_") : ["mkd"];
  const pascalCaseName = nameParts
    .map((part) => part.charAt(0).toUpperCase() + part.slice(1))
    .join("");

  const method = obj?.method?.toLowerCase();
  const url = role
    ? obj?.url?.replace("wxy", role).replace("xyz", project)
    : obj?.url;
  const needToken = obj?.needToken;

  let headers = "";
  if (needToken) {
    headers += `Authorization: "Bearer " + localStorage.getItem("token"),`;
  }

  let body = "";
  if (["post", "put", "patch"].includes(method)) {
    body = "body: data,";
  }

  // Get input and response examples
  const { example: inputExample, typeDef: inputTypeDef } = obj?.successBody
    ? formatJsonExample(obj.successBody)
    : { example: "{}", typeDef: "any" };
  const { example: responseExample } = obj?.successPayload
    ? formatJsonExample(obj.successPayload)
    : { example: "{}", typeDef: "any" };

  const functionString = `
  /**
   * ${obj?.name || ""}
   * ${obj?.description || ""}
   * 
   * @method ${method?.toUpperCase()}
   * @url ${url}
   * 
   * @example Request
   * ${inputExample}
   * 
   * @example Response
   * ${responseExample}
   */
  
  async ${functionName}(data: ${inputTypeDef}): Promise<${pascalCaseName}APIResponse>  {
    return this.request({
      endpoint: "${url}",
      method: RestAPIMethodEnum.${method?.toUpperCase()},
      ${body}
    });
  };
  `;

  return functionString;
}

function formatJsonExample(jsonStr) {
  try {
    // Parse the JSON string
    // jsonStr example = {"code": 234556,  "password": "password"}
    const obj = typeof jsonStr === "string" ? JSON.parse(jsonStr) : jsonStr;
    // construct type definition
    const typeDef = Object.entries(obj)
      .map(([key, value]) => `${key}: ${typeof value}`)
      .join(", ");

    // Pretty print with 2 space indentation
    return {
      example: JSON.stringify(obj, null, 2)
        // Add comment markers to each line
        .split("\n")
        .map((line) => " * " + line)
        .join("\n"),
      typeDef: `{${typeDef}}`
    };
  } catch (e) {
    // Return original if parsing fails
    return " * " + jsonStr;
  }
}

function generateJsonBody(fields) {
  if (!fields || !fields.length) return "{}";

  const result = {};

  fields.forEach((field) => {
    if (field.type === "array" && field.value) {
      // Handle array type with object values
      if (field.value.type === "object" && field.value.fields) {
        result[field.name] = [generateObjectExample(field.value.fields)];
      } else {
        result[field.name] = [getExampleValue(field.value.type)];
      }
    } else if (field.type === "object" && field.value) {
      // Handle object type
      result[field.name] = generateObjectExample(field.value);
    } else {
      // Handle primitive types with descriptions
      result[field.name] = {
        value: getExampleValue(field.type),
        description: field.description || `${field.name} field`
      };
    }
  });

  return JSON.stringify(result, null, 2);
}

function generateObjectExample(fields) {
  if (!Array.isArray(fields)) return {};

  const result = {};
  fields.forEach((field) => {
    result[field.name] = {
      value: getExampleValue(field.type),
      description: field.description || `${field.name} field`
    };
  });
  return result;
}

function getExampleValue(type) {
  switch (type?.toLowerCase()) {
    case "string":
      return "example";
    case "integer":
    case "number":
      return 123;
    case "boolean":
      return true;
    case "datetime":
    case "date":
      return "2024-03-15T10:00:00Z";
    case "array":
      return [];
    case "object":
      return {};
    default:
      return null;
  }
}

function getSDKWrapper(role, project) {
  if (role == "mkd") {
    return TemplateSDK(project);
  }
  return RoleTemplateSDK(role, project);
}

function getTreeQLSDKWrapper(config) {
  return `export default function TreeSDK() {

  this._baseurl = 'https://baas.mytechpassport.com';

   this.treeBaseUrl = function () {
    return this._baseurl + "/v1/api/records/${config.settings.model_namespace}";
  };

  function empty(value) {
    return (
      value === "" ||
      value === null ||
      value === undefined ||
      value === "undefined"
    );
  }

  function getJoins(options = {}) {
    let hasJoin = options.hasOwnProperty("join");
    let joins = options.join;
    if (hasJoin && typeof joins === "string") {
      joins = joins.split(",");
    } else {
      joins = [];
    }

    let joinQuery = "";
    joins.forEach((join) => {
      joinQuery += \`join=\${join}&\`;
    });

    return [hasJoin, joins, joinQuery];
  }

  function getOrdering(options) {
    let order = options.order ? options.order : "id";
    let direction = options.direction ? options.direction : "desc";

    return \`order=\${order},\${direction}&\`;
  }

  function getFilters(options) {
    let hasFilter = options.hasOwnProperty("filter");
    let filters = options.filter;

    let filterQuery = "";
    if (hasFilter && Array.isArray(filters)) {
      filters.forEach((filter) => {
        if (filter) { // Only add non-empty filters
          filterQuery += \`filter=\${encodeURIComponent(filter)}&\`;
        }
      });
    }

    return [hasFilter, filters, filterQuery];
  }


      /* CODE */

  return this;
      
}`;
}

function generateTreeqlFunctions() {
  return `
    /*
    Returns  one entry
    @params table : string - name of table to fetch
    @params id : number - id to fetch 
    @params options : object - optional parameters
            options.join - Array or comma separated list of tables to join

     let res = await (new TreeSDK).getOne('admin', 'author', 1 {
          join: ['book'],
        });

  */

  this.getOne = async function (role, table, id, options = {}) {
    if (empty(table) || empty(id)) throw new Error("table and id is required.");

    let [hasJoin, joins, joinQuery] = getJoins(options);
    const header = {
      "Content-Type": "application/json",
      Authorization: "Bearer " + localStorage.getItem("token"),
    };

    const getResult = await fetch(
      this.treeBaseUrl() + \`/\${role}\` + \`/\${table}/\${id}?\${joinQuery}\`,
  {
    method: "get",
      headers: header,
      }
    );
const json = await getResult.json();

if (getResult.status === 401) {
  throw new Error(json.message);
}

if (getResult.status === 403) {
  throw new Error(json.message);
}
return json;
  };

/*
  Returns one or more entries
  @params table : string - name of table to fetch
  @params ids : Array|string|number - array, comma separated list of ids or just a single id to fetch 
  @params options : object - optional parameters
          options.join - Array or comma separated list of tables to join

   let res = await (new TreeSDK).getMany('admin', 'author', [1,2] {
        join: ['book'],
      });

*/
this.getMany = async function (role, table, ids, options = {}) {
  if (empty(table) || empty(ids))
    throw new Error("table and id is required.");

  let [hasJoin, joins, joinQuery] = getJoins(options);
  let id = Array.isArray(ids) ? ids.join(",") : ids;
  const header = {
    "Content-Type": "application/json",
    Authorization: "Bearer " + localStorage.getItem("token"),
  };

  const getResult = await fetch(
    this.treeBaseUrl() + \`/\${role}\` + \`/\${table}/\${id}?\${joinQuery}\`,
    {
      method: "get",
      headers: header,
    }
  );
  const json = await getResult.json();

  if (getResult.status === 401) {
    throw new Error(json.message);
  }

  if (getResult.status === 403) {
    throw new Error(json.message);
  }
  return json;
};

/*
  Returns one or more entries with ordering and filters
  @params table : string - name of table to fetch
  @params options : object - optional parameters
          options.join - Array or comma separated list of tables to join
          options.filter - 
          options.order - field used to sort the result
          options.direction - direction of result asc|desc
          options.size - max number of entries

     let res = await (new TreeSDK).getList('admin', 'author', {
        filter: ['id,gt,2'],
        join: ['book']
      });

*/
this.getList = async function (role, table, options = {}) {
  if (empty(table)) throw new Error("table is required.");
  let [hasJoin, joins, joinQuery] = getJoins(options);
  let [hasFilter, filters, filterQuery] = getFilters(options);
  let orderQuery = getOrdering(options);
  let sizeQuery = options.hasOwnProperty("size")
    ? \`size=\${options.size}&\`
    : "";
  const header = {
    "Content-Type": "application/json",
    Authorization: "Bearer " + localStorage.getItem("token"),
  };

  const getResult = await fetch(
    this.treeBaseUrl() + \`/\${role}\` +
    \`/\${table}?\${joinQuery}\${orderQuery}\${sizeQuery}\${filterQuery}\`,
    {
      method: "get",
      headers: header,
    }
  );
  const json = await getResult.json();

  if (getResult.status === 401) {
    throw new Error(json.message);
  }

  if (getResult.status === 403) {
    throw new Error(json.message);
  }
  return json;
};

/*
  Returns a paginated list of entries
  @params table : string - name of table to fetch
  @params options : object - optional parameters
          options.join - Array or comma separated list of tables to join
          options.filter - 
          options.order - field used to sort the result
          options.direction - direction of result asc|desc
          options.page - page number
          options.size - max number of entries

 let res = await (new TreeSDK).getPaginate('admin', 'author', {
        filter: ['id,gt,2'],
        join: ['book']
      });

*/
 this.getPaginate = async function (role, table, options = {}) {
      if (empty(table)) throw new Error("table is required.");
      let [hasJoin, joins, joinQuery] = getJoins(options);
      let [hasFilter, filters, filterQuery] = getFilters(options);
      let orderQuery = getOrdering(options);
      let size = options.size ?? 20;
      let pageQuery = options.hasOwnProperty("page")
        ? \`page=\${options.page},\${size}&\`
        : \`page=1,\${size}&\`;

      const header = {
        "Content-Type": "application/json",
        Authorization: "Bearer " + localStorage.getItem("token"),
      };

      // Construct the URL with all parameters and remove any white spaces
      const url = (
        this.treeBaseUrl() + \`/\${role}\` +
        \`/\${table}?\${joinQuery}\${orderQuery}\${pageQuery}\${filterQuery}\`
      ).replace(/\\s+/g, '');

      const getResult = await fetch(url, {
        method: "get",
        headers: header,
      });

  const json = await getResult.json();

  if (getResult.status === 401) {
    throw new Error(json.message);
  }

  if (getResult.status === 403) {
    throw new Error(json.message);
  }
  return json;
};

/*
  Returns Creates a new entry
  @params table : string - name of table to fetch
  @params options : object - optional parameters

  
 let res = await (new TreeSDK).create('admin', 'author', {
        name: 'authro name',
        age: 23
      });
       

*/
this.create = async function (role, table, payload, options = {}) {
  if (empty(table)) throw new Error("table and id is required.");

  const header = {
    "Content-Type": "application/json",
    Authorization: "Bearer " + localStorage.getItem("token"),
  };
  const getResult = await fetch(this.treeBaseUrl() + \`/\${role}\` + \`/\${table}\`, {
    method: "post",
    headers: header,
    body: JSON.stringify(payload),
  });
  const json = await getResult.json();

  if (getResult.status === 401) {
    throw new Error(json.message);
  }

  if (getResult.status === 403) {
    throw new Error(json.message);
  }
  return json;
};

/*
  Returns Updates an entry
  @params table : string - name of table to update
  @params id : number - id of table entry to update
  @params payload  : object - key value pair for values to update
       
  let res = await (new TreeSDK).update('admin', 'author', 2 {
        name: 'updated author name',
      });

*/
this.update = async function (role, table, id, payload) {
  if (empty(table) || empty(id)) throw new Error("table and id is required.");

  const header = {
    "Content-Type": "application/json",
    Authorization: "Bearer " + localStorage.getItem("token"),
  };
  const getResult = await fetch(this.treeBaseUrl() + \`/\${role}\` + \`/\${table}/\${id}\`, {
    method: "put",
    headers: header,
    body: JSON.stringify(payload),
  });
  const json = await getResult.json();

  if (getResult.status === 401) {
    throw new Error(json.message);
  }

  if (getResult.status === 403) {
    throw new Error(json.message);
  }
  return json;
};

this.updateWhere = async function (role, table, where, payload) {
  if (empty(table) ) throw new Error("table is required.");
  if (Object.keys(where).length === 0)
    throw new Error("condition is required.");

  payload["updateCondition"] = where;
  const header = {
    "Content-Type": "application/json",
    Authorization: "Bearer " + localStorage.getItem("token"),
  };
  const getResult = await fetch(this.treeBaseUrl() + \`/\${role}\` + \`/\${table}\`, {
    method: "put",
    headers: header,
    body: JSON.stringify(payload),
  });
  const json = await getResult.json();

  if (getResult.status === 401) {
    throw new Error(json.message);
  }

  if (getResult.status === 403) {
    throw new Error(json.message);
  }
  return json;
};

/*
  Returns Delete an entry
  @params table : string - name of table to delete from
  @params id : number - id of table entry to delete

   let res = await (new TreeSDK).delete('author', 2);
*/
this.delete = async function (role, table, id, payload) {
  if (empty(table) || empty(id)) throw new Error("table and id is required.");

  const header = {
    "Content-Type": "application/json",
    Authorization: "Bearer " + localStorage.getItem("token"),
  };
  const getResult = await fetch(this.treeBaseUrl() + \`/\${role}\` + \`/\${table}/\${id}\`, {
    method: "delete",
    headers: header,
    body: JSON.stringify(payload),
  });
  const json = await getResult.json();

  if (getResult.status === 401) {
    throw new Error(json.message);
  }

  if (getResult.status === 403) {
    throw new Error(json.message);
  }
  return json;
};


  `;
}

function buildTreeqlSDK(config, projectName) {
  // let wrapper = getTreeQLSDKWrapper(config);
  // return wrapper.replace("/* CODE */", generateTreeqlFunctions());

  return TreeSDKTemplate(projectName);
}

module.exports = {
  buildLambdaSdk,
  buildApiSdk,
  buildTreeqlSDK,
  getSDKWrapper
};
