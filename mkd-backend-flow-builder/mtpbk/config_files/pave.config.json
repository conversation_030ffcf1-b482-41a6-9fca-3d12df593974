{"settings": {"globalKey": "key_1743109519711_z08jabn5s", "databaseType": "mysql", "authType": "session", "timezone": "UTC", "dbHost": "localhost", "dbPort": "3306", "dbUser": "root", "dbPassword": "root", "dbName": "database_2025-03-27", "id": "project_1743109519711_sqa7ioxvl", "isPWA": true, "isMultiTenant": false, "model_namespace": "pave", "payment_option": "none"}, "models": [{"id": "model_1743109539887_m3ib6q4sh", "name": "company", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "name", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "website", "type": "string", "defaultValue": "", "validation": ""}, {"name": "logo", "type": "string", "defaultValue": "", "validation": ""}, {"name": "owner_id", "type": "integer", "defaultValue": "", "validation": ""}, {"name": "address", "type": "string", "defaultValue": "", "validation": ""}, {"name": "city", "type": "string", "defaultValue": "", "validation": ""}, {"name": "state", "type": "string", "defaultValue": "", "validation": ""}, {"name": "zip", "type": "integer", "defaultValue": "", "validation": ""}, {"name": "country", "type": "string", "defaultValue": "", "validation": ""}, {"name": "phone", "type": "string", "defaultValue": "", "validation": "phone"}, {"name": "status", "type": "mapping", "mapping": "0:Active,1:Inactive,2:Suspend", "defaultValue": "0", "validation": ""}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}, {"id": "model_1743109539887_wqgj7x8b6", "name": "user", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "email", "type": "string", "defaultValue": "", "validation": "required,email"}, {"name": "company_id", "type": "foreign key", "defaultValue": "", "validation": "required"}, {"name": "password", "type": "password", "defaultValue": "", "validation": "required"}, {"name": "login_type", "type": "mapping", "mapping": "0:<PERSON>,1:Google,2:Microsoft,3:Apple,4:Twitter,5:Facebook", "defaultValue": "0", "validation": "required,enum:0,1,2,3,4,5"}, {"name": "role_id", "type": "string", "defaultValue": "", "validation": ""}, {"name": "data", "type": "json", "defaultValue": "", "validation": ""}, {"name": "status", "type": "mapping", "mapping": "0:Active,1:Inactive,2:Suspend", "defaultValue": "0", "validation": "required,enum:0,1,2"}, {"name": "verify", "type": "boolean", "defaultValue": "0", "validation": "required"}, {"name": "two_factor_authentication", "type": "boolean", "defaultValue": "0", "validation": ""}, {"name": "company_id", "type": "integer", "defaultValue": "0", "validation": ""}, {"name": "stripe_uid", "type": "string", "defaultValue": "", "validation": ""}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}, {"id": "model_1743109539887_iaynf1fs6", "name": "company_user", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "company_id", "type": "integer", "defaultValue": "", "validation": "required"}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "required"}, {"name": "role", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "email", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "status", "type": "mapping", "mapping": "0:Active,1:Inactive,2:Suspend", "defaultValue": "0", "validation": "required,enum:0,1,2"}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}, {"id": "model_1743109539887_6f9bw6dgi", "name": "company_admin", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "company_id", "type": "integer", "defaultValue": "", "validation": "required"}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "required"}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}, {"id": "model_1743109539887_1dftzwpld", "name": "company_employee_subscription", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "company_id", "type": "integer", "defaultValue": "", "validation": "required"}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "required"}, {"name": "stripe_subscription_id", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "status", "type": "mapping", "mapping": "0:Active,1:Inactive,2:Suspend", "defaultValue": "0", "validation": "required,enum:0,1,2"}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}, {"id": "model_1743109539887_mjeby379z", "name": "company_usage", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "company_id", "type": "integer", "defaultValue": "", "validation": "required"}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}, {"id": "model_1743109539887_yhroeg0sp", "name": "preference", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "first_name", "type": "string", "defaultValue": "", "validation": ""}, {"name": "last_name", "type": "string", "defaultValue": "", "validation": ""}, {"name": "phone", "type": "string", "defaultValue": "", "validation": ""}, {"name": "photo", "type": "string", "defaultValue": "", "validation": ""}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "required"}]}, {"id": "model_1743109539887_k8uxrk4ly", "name": "uploads", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "url", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "caption", "type": "string", "defaultValue": "", "validation": ""}, {"name": "user_id", "type": "foreign key", "defaultValue": "", "validation": ""}, {"name": "width", "type": "integer", "defaultValue": "", "validation": ""}, {"name": "height", "type": "integer", "defaultValue": "", "validation": ""}, {"name": "type", "type": "mapping", "mapping": "0:Image,1:s3,2:Video,3:base64", "defaultValue": "0", "validation": "required,enum:0,1,2,3"}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}, {"name": "updated_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}, {"id": "model_1743109539887_s66uerpmk", "name": "tokens", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "user_id", "type": "foreign key", "defaultValue": "", "validation": "required"}, {"name": "token", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "code", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "type", "type": "mapping", "mapping": "0:Access,1:<PERSON><PERSON><PERSON>,2:<PERSON><PERSON>,3:<PERSON><PERSON><PERSON>,4:<PERSON>", "defaultValue": "0", "validation": "required,enum:0,1,2,3,4"}, {"name": "data", "type": "json", "defaultValue": "", "validation": ""}, {"name": "status", "type": "mapping", "mapping": "0:Inactive,1:Active", "defaultValue": "1", "validation": "required,enum:0,1"}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}, {"name": "updated_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}, {"name": "expired_at", "type": "timestamp", "defaultValue": "", "validation": "date"}]}, {"id": "model_1743109539887_vlajxzpga", "name": "cms", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "label", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "type", "type": "mapping", "mapping": "0:Text,1:Number,2:Image,3:Raw", "defaultValue": "0", "validation": "required,enum:0,1,2,3"}, {"name": "value", "type": "long text", "defaultValue": "", "validation": "required"}]}, {"id": "model_1743109539887_m671hixyh", "name": "job", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "task", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "arguments", "type": "json", "defaultValue": "", "validation": ""}, {"name": "time_interval", "type": "string", "defaultValue": "once", "validation": ""}, {"name": "retries", "type": "integer", "defaultValue": "1", "validation": ""}, {"name": "status", "type": "mapping", "mapping": "0:Pending,1:Failed,2:Processing,3:Completed", "defaultValue": "0", "validation": ""}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}, {"name": "updated_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}], "routes": [{"id": "route_1743109564215_k6lcj1fpz", "name": "get all users", "flowData": {"nodes": [{"id": "url_node_1743109598204", "type": "mock-api", "position": {"x": 100, "y": 100}, "data": {"label": "Mock API", "apiname": "get all users", "path": "/v1/api/users", "method": "GET", "description": "", "fields": [], "queryFields": [], "responseFields": [], "authType": "none", "outputType": "json", "statusCode": 200}}], "edges": []}}, {"id": "route_1743109621927_ku9pmf6or", "name": "create a user", "flowData": {"nodes": [{"id": "url_node_1743109648090", "type": "mock-api", "position": {"x": 105, "y": 105}, "data": {"label": "Mock API", "apiname": "create a user", "path": "/v1/api/users", "method": "POST", "description": "", "fields": [{"name": "first_name", "type": "string", "validation": ""}, {"name": "last_name", "type": "string", "validation": ""}, {"name": "email", "type": "string", "validation": ""}, {"name": "status", "type": "integer", "validation": ""}], "queryFields": [], "responseFields": [], "authType": "bearer", "outputType": "json", "statusCode": 200}, "width": 180, "height": 56, "selected": false, "positionAbsolute": {"x": 105, "y": 105}, "dragging": false}], "edges": []}}, {"id": "route_1743109655139_vd6q11u61", "name": "get single users", "flowData": {"nodes": [{"id": "url_node_1743109677430", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "URL", "apiname": "get single users", "path": "/v1/api/users/:id", "fields": [], "queryFields": []}, "width": 180, "height": 56, "selected": false, "dragging": false}], "edges": []}}, {"id": "route_1743109861889_8akqq7dr8", "name": "delete single user", "flowData": {"nodes": [{"id": "url_node_1743109884217", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "URL", "apiname": "delete single user", "path": "/v1/api/users/:id", "method": "DELETE", "fields": [], "queryFields": []}, "width": 180, "height": 56, "selected": false, "dragging": false}], "edges": []}}, {"id": "route_1743109919336_8v9yxcndh", "name": "update single user", "flowData": {"nodes": [{"id": "url_node_1743109929812", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "URL", "apiname": "update single user", "path": "/v1/api/users/:id", "method": "PATCH", "fields": [{"name": "first_name", "type": "string", "validation": ""}, {"name": "last_name", "type": "string", "validation": ""}, {"name": "email", "type": "string", "validation": ""}, {"name": "status", "type": "integer", "validation": ""}], "queryFields": []}, "width": 180, "height": 56, "selected": true, "dragging": false}], "edges": []}}], "roles": [{"id": "role_admin_1743109539887", "name": "Super Admin", "slug": "super_admin", "permissions": {"routes": ["route_1743109564215_k6lcj1fpz", "route_1743109621927_ku9pmf6or", "route_1743109655139_vd6q11u61", "route_1743109861889_8akqq7dr8", "route_1743109919336_8v9yxcndh"], "canCreateUsers": true, "canEditUsers": true, "canDeleteUsers": true, "canManageRoles": true, "canLogin": true, "canRegister": true, "canForgot": false, "canReset": false, "canGoogleLogin": true, "canAppleLogin": true, "canMicrosoftLogin": true, "canMagicLinkLogin": false, "canTwitterLogin": false, "needs2FA": true, "canSetPermissions": true, "canPreference": true, "canVerifyEmail": true, "canUpload": true, "canStripe": true, "canStripeWebhook": true, "canRealTime": true, "canAI": true, "canUpdateEmail": true, "canUpdatePassword": true, "canUpdateOtherUsers": true, "treeql": {"enabled": true, "models": {"company": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "user": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "company_user": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "company_admin": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "company_employee_subscription": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "company_usage": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "job": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "cms": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "tokens": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "uploads": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "preference": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}}}}}, {"id": "role_company_admin_1743109539887", "name": "Company Admin", "slug": "company_admin", "permissions": {"routes": ["route_1743109564215_k6lcj1fpz", "route_1743109621927_ku9pmf6or", "route_1743109655139_vd6q11u61", "route_1743109861889_8akqq7dr8", "route_1743109919336_8v9yxcndh"], "canCreateUsers": true, "canEditUsers": true, "canDeleteUsers": true, "canManageRoles": true, "canLogin": true, "canRegister": true, "canForgot": false, "canReset": false, "canGoogleLogin": true, "canAppleLogin": true, "canMicrosoftLogin": true, "canMagicLinkLogin": false, "canTwitterLogin": false, "needs2FA": true, "canSetPermissions": true, "canPreference": true, "canVerifyEmail": true, "canUpload": true, "canStripe": true, "canStripeWebhook": true, "canRealTime": true, "canAI": true, "canUpdateEmail": true, "canUpdatePassword": true, "canUpdateOtherUsers": true, "treeql": {"enabled": true, "models": {"job": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "cms": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "tokens": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "uploads": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "preference": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "company_usage": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "company_employee_subscription": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "company_admin": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "company_user": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "user": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "company": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}}}, "companyScoped": true}}, {"id": "role_member_1743109539887", "name": "Member", "slug": "member", "permissions": {"routes": ["route_1743109564215_k6lcj1fpz", "route_1743109621927_ku9pmf6or", "route_1743109655139_vd6q11u61", "route_1743109861889_8akqq7dr8", "route_1743109919336_8v9yxcndh"], "canCreateUsers": false, "canEditUsers": false, "canDeleteUsers": false, "canManageRoles": true, "canLogin": true, "canRegister": true, "canForgot": false, "canReset": false, "canGoogleLogin": true, "canAppleLogin": true, "canMicrosoftLogin": true, "canMagicLinkLogin": false, "canTwitterLogin": false, "needs2FA": false, "canSetPermissions": false, "canPreference": true, "canVerifyEmail": true, "canUpload": true, "canStripe": false, "canStripeWebhook": false, "canRealTime": true, "canAI": true, "canUpdateEmail": true, "canUpdatePassword": true, "canUpdateOtherUsers": false, "treeql": {"enabled": true, "models": {"job": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "cms": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "tokens": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "uploads": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "preference": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "company_usage": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "company_employee_subscription": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "company_admin": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "company_user": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "user": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "company": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}}}, "companyScoped": true}}]}