const { StringCaser } = require("../utils");

const { PascalCase, camelCase } = new StringCaser();

const useSDKTemplate = (roles = []) => {
  return `


import { useMemo } from "react";
import { operations } from "@/utils";
import Tree<PERSON><PERSON> from "@/utils/TreeSDK";
import MkdSDK from "@/utils/MkdSDK";
${
  roles?.length
    ? roles
        .map((role) => {
          const roleNamePascal = PascalCase(role?.slug);
          return `import ${roleNamePascal}SDK from "@/utils/${roleNamePascal}SDK";`;
        })
        .join("\n")
    : ""
}

interface SdkConfig {
  baseurl?: string;
  fe_baseurl?: string;
  project_id?: string;
  secret?: string;
  table?: string;
}

interface UseSDKReturnType {
  sdk: MkdSDK;
  ${
    roles?.length
      ? roles
          .map((role) => {
            const roleName = camelCase(role?.slug);
            const roleNamePascal = PascalCase(role?.slug);
            return `${roleName}Sdk: ${roleNamePascal}SDK;`;
          })
          .join("\n")
      : ""
  }
  tdk: TreeSDK;
  projectId: string,
  operations: typeof operations;
}

const useSDK = (config: SdkConfig = {}): UseSDKReturnType => {
  const sdk = useMemo(() => {
    return new MkdSDK(config);
  }, [MkdSDK]);
  
  ${
    roles?.length
      ? roles
          .map((role) => {
            const roleNameCamel = camelCase(role?.slug);
            const roleNamePascal = PascalCase(role?.slug);
            return `
    const ${roleNameCamel}Sdk = useMemo(() => {
      return new ${roleNamePascal}SDK(config);
    }, [${roleNamePascal}SDK]);
    `;
          })
          .join("\n")
      : ""
  }

  const tdk = useMemo(() => {
    return new TreeSDK(config);
  }, [TreeSDK]);

  const projectId = sdk.getProjectId()

  return { 
  sdk,
  tdk,
  projectId,
  operations, 
  ${
    roles?.length
      ? roles
          .map((role) => {
            const roleName = camelCase(role?.slug);
            return `${roleName}Sdk`;
          })
          .join(",\n")
      : ""
  }
  };
};

export default useSDK;
`;
};

module.exports = useSDKTemplate;
