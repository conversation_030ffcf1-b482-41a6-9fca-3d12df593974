/**
 * Template for generating test files for mock APIs
 * This template will be used to generate test files for mock APIs in custom projects
 * The generated test files will be placed in <project_name>_backend/tests/unit
 */

function generateMockApiTest(apiConfig) {
  const {
    method,
    path,
    name,
    description,
    role,
    namespace,
    fields = [],
    queryFields = [],
    responseFields = [],
    authType,
  } = apiConfig;

  // Generate expected response schema based on response fields
  const responseSchema = responseFields.reduce((schema, field) => {
    schema[field.name] =
      field.type === "object"
        ? "object"
        : field.type === "array"
        ? "array"
        : "string";
    return schema;
  }, {});

  // Generate request body based on fields
  const requestBody = fields.reduce((body, field) => {
    body[field.name] = generateMockValue(field);
    return body;
  }, {});

  // Generate query parameters based on queryFields
  const queryParams = queryFields.reduce((params, field) => {
    params[field.name] = generateMockValue(field);
    return params;
  }, {});

  // Generate query string for URL
  const queryString =
    Object.keys(queryParams).length > 0
      ? "?" +
        Object.entries(queryParams)
          .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
          .join("&")
      : "";

  // Generate URL with path parameters replaced with mock values
  let url = path;
  const pathParams = path.match(/:[a-zA-Z_][a-zA-Z0-9_]*/g) || [];
  pathParams.forEach((param) => {
    const paramName = param.substring(1); // Remove the colon
    url = url.replace(param, `mock_${paramName}`);
  });

  // Generate test file content
  return `const APITestFramework = require("../../../../tests/apitesting.base.js");

class ${formatClassName(name)}Tests {
  constructor() {
    this.framework = new APITestFramework();
    this.baseUrl = 'http://localhost:5172';
    this.setupTests();
  }

  setupTests() {
    this.framework.describe("${name} API Tests", () => {
      // Setup before running tests
      this.framework.beforeEach(async () => {
        // Setup mock data and authentication
        ${
          authType === "bearer"
            ? "this.authToken = 'Bearer mock_auth_token';"
            : "// No authentication required"
        }
      });

      // Test case for successful request
      this.framework.addTestCase("${name} - Success Path", async () => {
        // Mock the API response
        this.framework.mockRequest(
          \`\${this.baseUrl}${url}${queryString}\`,
          {
            error: false,
            ${
              Object.keys(responseSchema).length > 0
                ? generateMockResponseBody(responseSchema)
                : 'message: "Operation successful"'
            }
          },
          {
            status: 200,
            headers: {
              "Content-Type": "application/json"
            }
          }
        );

        // Make the request
        const response = await this.framework.makeRequest(
          \`\${this.baseUrl}${url}${queryString}\`,
          {
            method: "${method.toUpperCase()}",
            headers: {
              "Content-Type": "application/json"${
                authType === "bearer"
                  ? ',\n              "Authorization": this.authToken'
                  : ""
              }
            }${
              method.toUpperCase() === "GET"
                ? ""
                : ",\n            body: JSON.stringify(" +
                  JSON.stringify(requestBody, null, 6) +
                  ")"
            }
          }
        );

        // Assertions
        this.framework.assert(
          response.status === 200,
          "${name} should return 200 status"
        );
        this.framework.assert(
          response.body.error === false,
          "${name} error flag should be false"
        );
      });

      ${generateValidationTestCases(
        fields,
        queryFields,
        name,
        url,
        method,
        authType
      )}

      ${
        authType === "bearer"
          ? generateAuthenticationTestCase(name, url, method, queryString)
          : ""
      }
    });
  }

  async runTests() {
    try {
      const results = await this.framework.runTests();
      this.framework.generateTestReport();
      return results;
    } catch (error) {
      console.error("Test execution failed:", error);
      throw error;
    }
  }
}

// Create and run tests
const tests = new ${formatClassName(name)}Tests();
module.exports = tests
  .runTests()
  .then((report) => {
    if (report.failed > 0) {
      process.exit(1);
    }
    return report; // Important: return the report for the test runner
  })
  .catch((error) => {
    console.error("Test framework error:", error);
    process.exit(1);
  });
`;
}

// Helper function to format class name (camelCase to PascalCase)
function formatClassName(name) {
  return name
    .replace(/[-_\s](.)/g, (_, c) => c.toUpperCase())
    .replace(/^(.)/g, (_, c) => c.toUpperCase())
    .replace(/[^a-zA-Z0-9]/g, "");
}

// Helper function to generate mock value based on field type
function generateMockValue(field) {
  switch (field.type) {
    case "string":
      return "mock_string";
    case "integer":
    case "number":
      return 123;
    case "boolean":
      return true;
    case "date":
      return "2023-01-01";
    case "datetime":
      return "2023-01-01T00:00:00Z";
    case "array":
      return [];
    case "object":
      return {};
    default:
      return "mock_value";
  }
}

// Helper function to generate mock response body
function generateMockResponseBody(schema) {
  return Object.entries(schema)
    .map(([key, type]) => {
      if (type === "object") {
        return `${key}: {}`;
      } else if (type === "array") {
        return `${key}: []`;
      } else if (type === "number" || type === "integer") {
        return `${key}: 123`;
      } else if (type === "boolean") {
        return `${key}: true`;
      } else {
        return `${key}: "mock_${key}"`;
      }
    })
    .join(",\n            ");
}

// Helper function to generate validation test cases
function generateValidationTestCases(
  fields,
  queryFields,
  name,
  url,
  method,
  authType
) {
  let testCases = [];

  // Generate test cases for required fields
  const requiredFields = fields.filter(
    (field) => field.validation && field.validation.includes("required")
  );
  if (requiredFields.length > 0) {
    requiredFields.forEach((field) => {
      testCases.push(`
      // Test case for missing required field
      this.framework.addTestCase("${name} - Missing Required Field: ${
        field.name
      }", async () => {
        // Create request body without the required field
        const requestBody = ${JSON.stringify(
          fields.reduce((body, f) => {
            if (f.name !== field.name) {
              body[f.name] = generateMockValue(f);
            }
            return body;
          }, {}),
          null,
          10
        )};

        // Mock the API response for error case
        this.framework.mockRequest(
          \`\${this.baseUrl}${url}\`,
          {
            error: true,
            message: "${field.name} is required"
          },
          {
            status: 400,
            headers: {
              "Content-Type": "application/json"
            }
          }
        );

        // Make the request
        const response = await this.framework.makeRequest(
          \`\${this.baseUrl}${url}\`,
          {
            method: "${method.toUpperCase()}",
            headers: {
              "Content-Type": "application/json"${
                authType === "bearer"
                  ? ',\n              "Authorization": this.authToken'
                  : ""
              }
            },
            body: JSON.stringify(requestBody)
          }
        );

        // Assertions for error case
        this.framework.assertions.assertEquals(
          response.status,
          400,
          "Should return 400 status for missing required field"
        );
        this.framework.assertions.assertEquals(
          response.body.error,
          true,
          "Error flag should be true"
        );
        this.framework.assertions.assertEquals(
          response.body.message,
          "${field.name} is required",
          "Should return correct error message"
        );
      });`);
    });
  }

  return testCases.join("");
}

// Helper function to generate authentication test case
function generateAuthenticationTestCase(name, url, method, queryString) {
  return `
      // Test case for missing authentication
      this.framework.addTestCase("${name} - Missing Authentication", async () => {
        // Mock the API response for error case
        this.framework.mockRequest(
          \`\${this.baseUrl}${url}${queryString}\`,
          {
            error: true,
            message: "Authentication required"
          },
          {
            status: 401,
            headers: {
              "Content-Type": "application/json"
            }
          }
        );

        // Make the request without authentication
        const response = await this.framework.makeRequest(
          \`\${this.baseUrl}${url}${queryString}\`,
          {
            method: "${method.toUpperCase()}",
            headers: {
              "Content-Type": "application/json"
              // No Authorization header
            }
          }
        );

        // Assertions for error case
        this.framework.assertions.assertEquals(
          response.status,
          401,
          "Should return 401 status for missing authentication"
        );
        this.framework.assertions.assertEquals(
          response.body.error,
          true,
          "Error flag should be true"
        );
        this.framework.assertions.assertEquals(
          response.body.message,
          "Authentication required",
          "Should return correct error message"
        );
      });`;
}

module.exports = generateMockApiTest;
