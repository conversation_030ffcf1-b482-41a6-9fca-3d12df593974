const config = {
  // Global Application Key
  globalKey: process.env.GLOBAL_KEY || "key_1731956956484_fs2brczmy",
  jwt_key: process.env.JWT_KEY || "key_1731956956484_fs2brczmy",
  refresh_jwt_expire: process.env.REFRESH_JWT_EXPIRE || 60 * 60 * 24 * 7, // 7 days
  access_jwt_expire: process.env.ACCESS_JWT_EXPIRE || 60 * 60, // 1 hour
  verification_token_expire:
    process.env.VERIFICATION_TOKEN_EXPIRE || 60 * 60 * 24, // 24 hours
  app_url: process.env.APP_URL || "http://localhost:5172",
  // Database Configuration
  database: {
    type: process.env.DB_TYPE || "MySQL",
    host: process.env.DB_HOST || "***************",
    port: 3306,
    user: "baas",
    password: "en8elqeiqxR)",
    database: "baas_db",
    timezone: "UTC",
  },
  mail: {
    mail_host: process.env.MAIL_HOST || "smtp.zoho.com",
    mail_port: process.env.MAIL_PORT || 465,
    mail_user: process.env.MAIL_USER || "<EMAIL>",
    mail_pass: process.env.MAIL_PASS || "dJwg7uw2uxQX",
    from_mail: process.env.FROM_MAIL || "<EMAIL>",
  },
  // Authentication Configuration
  auth: {
    type: "session",
    secret: process.env.SESSION || "your-secret-key",
    cookie: {
      secure: "production",
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
    },
  },

  // Server Configuration
  server: {
    port: process.env.PORT || 5172,
    cors: {
      origin: process.env.CORS_ORIGIN || "*",
      methods: ["GET", "POST", "PUT", "DELETE", "PATCH"],
    },
  },

  // API Configuration
  api: {
    prefix: "/api/v1",
    rateLimit: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // limit each IP to 100 requests per windowMs
    },
  },

  // Logging Configuration
  logging: {
    level: process.env.LOG_LEVEL || "info",
    format: "combined",
  },

  // Stripe Configuration
  stripe: {
    publish_key: "pk_test_51Ll5ukBgOlWo0lDUrBhA2W7EX2MwUH9AR5Y3KQoujf7PTQagZAJylWP1UOFbtH4UwxoufZbInwehQppWAq53kmNC00UIKSmebO",
    secret_key: "sk_test_51Ll5ukBgOlWo0lDU83gVbZQHnEFerz8t1BfWdhljzwP1CBNsqa6HRXwMm7fQrYYMHC1F2M0WyGOvZLmInXTE9nE900EgqSdmn6",
    endpoint_secret: "",
    webhook_secret: "",
    currency: "usd",
    ieti_secret:"sk_test_51NMBwQByl7gZJNSNUfVwnrSufjsdpeCtSHAvOvyNjFijrY0F6r7n7FMxUKoknUechYiGj21XVOmTphuN6PLEUYtc00a3YPl43t"
  },


  // Twilio Configuration
  twilio: {
    account_sid: process.env.TWILIO_ACCOUNT_SID || "AC_whatever_key", // Replace with your Twilio Account SID
    auth_token: process.env.TWILIO_AUTH_TOKEN || "your_auth_token_here", // Replace with your Twilio Auth Token
    phoneNumber:
      process.env.TWILIO_PHONE_NUMBER || "your_twilio_phone_number_here", // Replace with your Twilio phone number
  },
  aws_key: process.env.AWS_ACCESS_KEY_ID || "********************",
  aws_secret:
    process.env.AWS_SECRET_ACCESS_KEY ||
    "RBWDW9Jpk9YGlaA0foCDgcN7sC5ERIQBmHcz/YBa",
  aws_bucket: process.env.AWS_BUCKET_NAME || "com.mkdlabs.images",
  aws_region: process.env.AWS_REGION || "us-east-2",
  basic_auth: process.env.BASIC_AUTH || "your-basic-auth-key",
  bearer_auth: process.env.BEARER_AUTH || "your-bearer-auth-key",
  api_key: process.env.API_KEY || "your-api-key",
  oauth2: process.env.OAUTH2 || "your-oauth2-key",
  digest: process.env.DIGEST || "your-digest-key",
  session: process.env.SESSION || "your-session-key",
  hmac: process.env.HMAC || "your-hmac-key",
  ldap: process.env.LDAP || "your-ldap-key",
  saml: process.env.SAML || "your-saml-key",
  git: {
    token: process.env.GIT_TOKEN || 'git_token'
  },
  frontend_server: {
    ip: process.env.FRONTEND_SERVER_IP || '',
    username: process.env.FRONTEND_SERVER_USERNAME || '',
    password: process.env.FRONTEND_SERVER_PASSWORD || ''
  },
  digital_ocean: {
    token: process.env.DIGITAL_OCEAN_TOKEN || 'digital_ocean_token'
  },
  jenkins: {
    user: 'emmy',
    token: process.env.JENKINS_TOKEN || 'jenkins_token'
  },
google: {
    client_id: '1007493621952-svk7dmqtl7cimkb5ecuc1jvvbesr1ofl.apps.googleusercontent.com',
    client_secret: '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',
    //client_id: '356934742115-bntd5isbkf9q9an3vejknu14khmp6nm7.apps.googleusercontent.com',
    //client_secret: '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',
    redirect_url: 'https://mkdlabs.com/v2/api/lambda/google/code',
    calendar_client_id: "1598329696-fh3hprfb8gthh1sj3hfbo5613gkb156l.apps.googleusercontent.com",
    calendar_client_secret: "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",
    calendar_redirect_uri: "https://mkdlabs.com/v2/api/lambda/scheduling/google-auth",
    drive: {
      client_id: '356934742115-c707dqbhct9b7gj64eo9rqfdfi47rb8o.apps.googleusercontent.com',
      client_secret: '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',
      redirect_url: 'https://mkdlabs.com/v2/api/lambda/google-drive/callback',
      api_key: 'AIzaSyB7JhbYloABBC-jZebyjHoiXUiM-s_7sBA'
    }
  },
  apple: {
    client_id: process.env.APPLE_CLIENT_ID || 'client_id',
    client_secret: process.env.APPLE_CLIENT_SECRET || 'client_secret',
    redirect_url: process.env.APPLE_REDIRECT_URL || 'redirect_url',
    team_id: process.env.APPLE_TEAM_ID || 'team_id',
    private_key: process.env.APPLE_PRIVATE_KEY || ` private_key`,
    key_id: process.env.APPLE_KEY_ID || 'key_id'
  }, 
  facebook: {
    client_id: process.env.FACEBOOK_CLIENT_ID || 'client_id',
    client_secret: process.env.FACEBOOK_CLIENT_SECRET || 'client_secret',
    callback_uri: process.env.FACEBOOK_CALLBACK_URI || 'callback_uri'
  },
  microsoft: {
    client_secret: process.env.MICROSOFT_CLIENT_SECRET || 'client_secret',
    client_values: process.env.MICROSOFT_CLIENT_VALUES || 'client_values',
    application_id: process.env.MICROSOFT_APPLICATION_ID || 'application_id',
    object_id: process.env.MICROSOFT_OBJECT_ID || 'object_id',
    redirect_url: process.env.MICROSOFT_REDIRECT_URL || 'redirect_url',
 }
};

module.exports = config;
