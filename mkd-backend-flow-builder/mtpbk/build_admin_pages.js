function buildAddPage(config) {
  const { name, fields } = config;
  const capitalizedName = name.charAt(0).toUpperCase() + name.slice(1);

  // Filter out primary key and timestamp fields
  const editableFields = fields.filter(
    (f) =>
      f.type !== "primary key" && !["created_at", "updated_at"].includes(f.name)
  );

  const formFields = editableFields
    .map(
      (field) => `
    <div className="mb-4">
    <LazyLoad>
    <MkdInput
      type={"text"}
      page={"add"}
      name={"${field.name}"}
      errors={errors}
      label={"${field.name.charAt(0).toUpperCase() + field.name.slice(1)}"}
      placeholder={"${
        field.name.charAt(0).toUpperCase() + field.name.slice(1)
      }"}
      register={register}
      className={""}
    />
    </LazyLoad>
    </div>
    `
    )
    .join("\n");

  const validationSchema = editableFields
    .map(
      (field) =>
        `${field.name}: yup.string()${
          field.validation?.includes("required") ? ".required()" : ""
        }`
    )
    .join(",\n");

  const submitData = editableFields
    .map((field) => `${field.name}: _data.${field.name}`)
    .join(",\n");

  return `
import React from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "@/utils/MkdSDK";
import { useNavigate } from "react-router-dom";
import { AuthContext, tokenExpireError } from "@/context/Auth";
import { GlobalContext, showToast } from "@/context/Global";
import { MkdInput } from "@/components/MkdInput";
import { InteractiveButton } from "@/components/InteractiveButton";
import { LazyLoad } from "@/components/LazyLoad";

interface AddAdmin${capitalizedName}PageProps {
  setSidebar: (sidebar: boolean) => void;
}

const AddAdmin${capitalizedName}Page = ({setSidebar}: AddAdmin${capitalizedName}PageProps) => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const schema = yup
    .object({
      ${validationSchema}
    })
    .required();

  const { dispatch } = React.useContext(AuthContext);
  const [isSubmitLoading, setIsSubmitLoading] = React.useState(false);
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const onSubmit = async (_data) => {
    setIsSubmitLoading(true);
    try {
      let sdk = new MkdSDK();
      sdk.setTable("${name}");

      const result = await sdk.callRestAPI(
        {
          ${submitData}
        },
        "POST"
      );

      if (!result.error) {
        showToast(globalDispatch, "Added");
        navigate("/admin/${name}");
        setSidebar(false);
        globalDispatch({
          type: "REFRESH_DATA",
          payload: {
            refreshData: true,
          },
        });
      }
      setIsSubmitLoading(false);
    } catch (error) {
      setIsSubmitLoading(false);
      console.log("Error", error);
      tokenExpireError(dispatch, error.message);
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "${name}",
      },
    });
  }, []);

  return (
    <div className="shadow-md rounded mx-auto p-5">
      <h4 className="text-2xl font-medium">Add ${capitalizedName}</h4>
      <form className="w-full max-w-lg" onSubmit={handleSubmit(onSubmit)}>
        ${formFields}
        
        <InteractiveButton
          type="submit"
          loading={isSubmitLoading}
          disabled={isSubmitLoading}
          className="bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
        >
          Submit
        </InteractiveButton>
      </form>
    </div>
  );
};

export default AddAdmin${capitalizedName}Page;`;
}

function buildEditPage(config) {
  const { name, fields } = config;
  const capitalizedName = name.charAt(0).toUpperCase() + name.slice(1);

  const editableFields = fields.filter(
    (f) =>
      f.type !== "primary key" && !["created_at", "updated_at"].includes(f.name)
  );

  const formFields = editableFields
    .map(
      (field) => `
    <div className="mb-4">
    <LazyLoad>
    <MkdInput
      type={"text"} 
      page={"edit"}
      name={"${field.name}"}
      errors={errors}
      label={"${field.name.charAt(0).toUpperCase() + field.name.slice(1)}"}
      placeholder={"${
        field.name.charAt(0).toUpperCase() + field.name.slice(1)
      }"}
      register={register}
      className={""}
      />
    </LazyLoad>
    </div>
    `
    )
    .join("\n");

  const validationSchema = editableFields
    .map(
      (field) =>
        `${field.name}: yup.string()${
          field.validation?.includes("required") ? ".required()" : ""
        }`
    )
    .join(",\n");

  const submitData = editableFields
    .map((field) => `${field.name}: _data.${field.name}`)
    .join(",\n");

  const stateSetters = editableFields
    .map((field) => `setValue('${field.name}', result.model.${field.name});`)
    .join("\n");

  return `
import React from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "@/utils/MkdSDK";
import { useNavigate, useParams } from "react-router-dom";
import { AuthContext, tokenExpireError } from "@/context/Auth";
import { GlobalContext, showToast } from "@/context/Global";
import { MkdInput } from "@/components/MkdInput";
import { InteractiveButton } from "@/components/InteractiveButton";
import { LazyLoad } from "@/components/LazyLoad";
import { SkeletonLoader } from "@/components/Skeleton";

let sdk = new MkdSDK();

interface EditAdmin${capitalizedName}PageProps {
  activeId: number | null;
  setSidebar: (sidebar: boolean) => void;
}

const EditAdmin${capitalizedName}Page = ({activeId, setSidebar}: EditAdmin${capitalizedName}PageProps) => {
  const { dispatch } = React.useContext(AuthContext);
  
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  const [loading, setLoading] = React.useState<boolean>(false);
  const navigate = useNavigate();
  
  
  const schema = yup
    .object({
      ${validationSchema}
    })
    .required();
  
    const {
    register,
    handleSubmit,
    setError,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const params = useParams();

  const fetchData = async () => {
    try {
      setLoading(true);
      sdk.setTable("${name}");
      const result = await sdk.callRestAPI(
        { id: activeId ? activeId : Number(params?.id) },
        "GET"
      );
      
      if (!result.error) {
        ${stateSetters}
        setLoading(false);
      }
    } catch (error) {
      setLoading(false);
      console.log("error", error);
      tokenExpireError(dispatch, error.message);
    }
  };


  const onSubmit = async (_data: yup.InferType<typeof schema>) => {
    setIsLoading(true);
    try {
      sdk.setTable("${name}");
      const result = await sdk.callRestAPI(
        {
          id: activeId ? activeId : Number(params?.id),
          ${submitData}
        },
        "PUT"
      );

      if (!result.error) {
        showToast(globalDispatch, "Updated");
        navigate("/admin/${name}");
        props.setSidebar(false);
        globalDispatch({
          type: "REFRESH_DATA",
          payload: {
            refreshData: true,
          },
        });
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      console.log("Error", error);
      tokenExpireError(dispatch, error.message);
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "${name}",
      },
    });
  }, []);
  
  React.useEffect(function() {
    fetchData();
  }, [activeId, params?.id]);

  return (
    <div className="shadow-md rounded mx-auto p-5">
      <h4 className="text-2xl font-medium">Edit ${capitalizedName}</h4>
      {loading ? (
        <SkeletonLoader />
      ) : (
        <form className="w-full max-w-lg" onSubmit={handleSubmit(onSubmit)}>
          ${formFields}
          
          <InteractiveButton
            type="submit"
            loading={isLoading}
            disabled={isLoading}
            className="bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
          >
            Submit
          </InteractiveButton>
        </form>
      )}
    </div>
  );
};

export default EditAdmin${capitalizedName}Page;`;
}

function buildListPage(config) {
  const { name, fields } = config;
  const capitalizedName = name.charAt(0).toUpperCase() + name.slice(1);

  const columns = fields
    .map(
      (field) => `{
    header: '${field.name.charAt(0).toUpperCase() + field.name.slice(1)}',
    accessor: '${field.name}',
    isSorted: false,
    isSortedDesc: false,
    mappingExist: ${field.type === "mapping" ? "true" : "false"},
    mappings: ${
      field.type === "mapping"
        ? JSON.stringify(
            field.mapping.split(",").reduce((acc, curr) => {
              const [key, value] = curr.split(":");
              acc[key] = value;
              return acc;
            }, {})
          )
        : "{}"
    }
  }`
    )
    .join(",\n");

  return `
import React, { useRef } from "react";
import MkdSDK from "@/utils/MkdSDK";
import { AuthContext } from "@/context/Auth";
import { GlobalContext } from "@/context/Global";
import { useNavigate } from "react-router-dom";
import { LazyLoad } from "@/components/LazyLoad";
import { ModalSidebar } from "@/components/ModalSidebar";
import { MkdListTableV2 } from "@/components/MkdListTable";
import { AdminEdit${capitalizedName}Page, AdminAdd${capitalizedName}Page } from "@/routes/LazyLoad";

let sdk = new MkdSDK();

const columns = [
  {
    header: "Row",
    accessor: "row",
  },
  ${columns},
  {
    header: "Action",
    accessor: "",
  }
];

interface Admin${capitalizedName}ListPageProps {}

const Admin${capitalizedName}ListPage = ({}: Admin${capitalizedName}ListPageProps) => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const navigate = useNavigate();

  const [showAddSidebar, setShowAddSidebar] = React.useState<boolean>(false);
  const [showEditSidebar, setShowEditSidebar] = React.useState<boolean>(false);
  const [activeEditId, setActiveEditId] = React.useState<number | null>(null);
  const refreshRef = useRef(null);

  const [selectedItems, setSelectedItems] = React.useState<number[]>([]);

  const onToggleModal = (modal: string, toggle: boolean, ids: number[] = []) => {
    switch (modal) {
      case "add":
        setShowAddSidebar(toggle);
        break;
      case "edit":
        setShowEditSidebar(toggle);
        setSelectedItems(ids);
        setActiveEditId(ids[0]);
        break;
    }
  };

  return (
    <>
      <div className="grid h-full max-h-full min-h-full w-full grid-cols-1 grid-rows-1 p-8">
        <LazyLoad>
          <MkdListTableV2
            columns={columns}
            useDefaultColumns={true}
            tableRole={"admin"}
            table={"${name}"}
            actionId={"id"}
            actions={{
              view: { show: true, action: null, multiple: false },
              edit: {
                show: true,
                multiple: false,
                action: (ids) => onToggleModal("edit", true, ids),
              },
              delete: { show: true, action: null, multiple: false },
              select: { show: true, action: null, multiple: false },
              add: {
                show: true,
                action: () => onToggleModal("add", true),
                multiple: false,
                children: "Add New",
                showChildren: true,
              },
              export: { show: false, action: null, multiple: true },
            }}
            actionPosition={["dropdown", "overlay"]}
            refreshRef={refreshRef}
            maxHeight={"grid-rows-[auto_1fr_auto]"}
          />
        </LazyLoad>
      </div>

      <LazyLoad>
        <ModalSidebar
          isModalActive={showAddSidebar}
          closeModalFn={() => setShowAddSidebar(false)}
        >
          <AdminAdd${capitalizedName}Page setSidebar={setShowAddSidebar} />
        </ModalSidebar>
      </LazyLoad>

      {showEditSidebar && (
        <LazyLoad>
          <ModalSidebar
            isModalActive={showEditSidebar}
            closeModalFn={() => setShowEditSidebar(false)}
          >
            <AdminEdit${capitalizedName}Page
              activeId={activeEditId}
              setSidebar={setShowEditSidebar}
            />
          </ModalSidebar>
        </LazyLoad>
      )}
    </>
  );
};

export default Admin${capitalizedName}ListPage;`;
}

function buildViewPage(config) {
  const { name, fields } = config;
  const capitalizedName = name.charAt(0).toUpperCase() + name.slice(1);

  const viewFields = fields
    .map(
      (field) => `
    <div className="mb-4 mt-4">
      <div className="flex mb-4">
        <div className="flex-1">${
          field.name.charAt(0).toUpperCase() + field.name.slice(1)
        }</div>
        <div className="flex-1">{viewModel?.${field.name}}</div>
      </div>
    </div>`
    )
    .join("\n");

  return `
import React from "react";
import MkdSDK from "@/utils/MkdSDK";
import { useParams } from "react-router-dom";
import { AuthContext, tokenExpireError } from "@/context/Auth";
import { GlobalContext } from "@/context/Global";
import { SkeletonLoader } from "@/components/Skeleton";

let sdk = new MkdSDK();

interface ViewAdmin${capitalizedName}PageProps {}

const ViewAdmin${capitalizedName}Page = ({}: ViewAdmin${capitalizedName}PageProps) => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [viewModel, setViewModel] = React.useState({});
  const [loading, setLoading] = React.useState(true);
  const params = useParams();
  
  const fetchData = async () => {
    try {
      setLoading(true);
      sdk.setTable("${name}");
      const result = await sdk.callRestAPI(
        { id: Number(params?.id), join: "" },
        "GET"
      );
      if (!result.error) {
        setViewModel(result.model);
        setLoading(false);
      }
    } catch (error) {
      setLoading(false);
      console.log("error", error);
      tokenExpireError(dispatch, error.message);
    }
  }

  React.useEffect(function() {
    fetchData();
  }, []);

  return (
    <div className="shadow-md rounded mx-auto p-5">
      {loading ? (
        <SkeletonLoader />
      ) : (
        <>
          <h4 className="text-2xl font-medium">View ${capitalizedName}</h4>
          ${viewFields}
        </>
      )}
    </div>
  );
};

export default ViewAdmin${capitalizedName}Page;`;
}

function buildRoutes(config) {
  const { name } = config;
  const capitalizedName = name.charAt(0).toUpperCase() + name.slice(1);

  return `
<Route
  exact
  path="/admin/${name}"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/${name}"
      element={
        <AdminWrapper>
          <Admin${capitalizedName}ListPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/add-${name}"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/add-${name}"
      element={
        <AdminWrapper>
          <AdminAdd${capitalizedName}Page />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/edit-${name}/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/edit-${name}/:id"
      element={
        <AdminWrapper>
          <AdminEdit${capitalizedName}Page />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/view-${name}/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/view-${name}/:id"
      element={
        <AdminWrapper>
          <AdminView${capitalizedName}Page />
        </AdminWrapper>
      }
    />
  }
/>`;
}

function buildNavItems(config) {
  const { name } = config;
  const capitalizedName = name.charAt(0).toUpperCase() + name.slice(1);

  return `{
    to: "/admin/${name}",
    text: "  ${capitalizedName}s",
    icon: <MdDashboard className="text-xl text-[#A8A8A8]" />,
    value: "${name}",
    visible: true
  }`;
}

function buildLazyLoadRoutes(config) {
  const { name } = config;
  const capitalizedName = name.charAt(0).toUpperCase() + name.slice(1);

  return `
export const AdminList${capitalizedName}Page = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminList${capitalizedName}Page");
  __import.finally(() => {});
  return __import;
});

export const AdminAdd${capitalizedName}Page = lazy(() => {
  const __import = import("@/pages/Admin/Add/AdminAdd${capitalizedName}Page");
  __import.finally(() => {});
  return __import;
});

export const AdminEdit${capitalizedName}Page = lazy(() => {
  const __import = import("@/pages/Admin/Edit/AdminEdit${capitalizedName}Page");
  __import.finally(() => {});
  return __import;
});

export const AdminView${capitalizedName}Page = lazy(() => {
  const __import = import("@/pages/Admin/View/AdminView${capitalizedName}Page");
  __import.finally(() => {});
  return __import;
});`;
}

function generateAdminPages(config) {
  const result = {
    pages: [],
    routes: [],
    header: null,
    lazyLoad: []
  };

  const navItems = config.models.map((model) => {
    const { name } = model;
    return buildNavItems({ name });
  });

  // Generate pages for each model
  config.models.forEach((model) => {
    const { name, fields } = model;
    const capitalizedName = name.charAt(0).toUpperCase() + name.slice(1);

    result.pages.push({
      file: `pages/Admin/List/AdminList${capitalizedName}Page.tsx`,
      content: buildListPage({ name, fields })
    });

    result.pages.push({
      file: `pages/Admin/Add/AdminAdd${capitalizedName}Page.tsx`,
      content: buildAddPage({ name, fields })
    });

    result.pages.push({
      file: `pages/Admin/Edit/AdminEdit${capitalizedName}Page.tsx`,
      content: buildEditPage({ name, fields })
    });

    result.pages.push({
      file: `pages/Admin/View/AdminView${capitalizedName}Page.tsx`,
      content: buildViewPage({ name, fields })
    });

    // Add routes for this model
    result.routes.push(buildRoutes({ name, fields }));

    // Add lazy load routes
    result.lazyLoad.push(buildLazyLoadRoutes({ name, fields }));
  });

  // Combine all routes into one file
  const lazyImports = config.models
    .map((model) => {
      const capitalizedName =
        model.name.charAt(0).toUpperCase() + model.name.slice(1);
      return `
    AdminList${capitalizedName}Page,
    AdminAdd${capitalizedName}Page,
    AdminEdit${capitalizedName}Page,
    AdminView${capitalizedName}Page`;
    })
    .join(",\n");

  result.routes = {
    file: "routes/Routes.tsx",
    content: `

    
import { AdminWrapper } from "Components/AdminWrapper";

import { 
${lazyImports}
} from "./LazyLoad";

${result.routes.join("\n")}
    `
  };

  // Combine all lazy load routes
  result.lazyLoad = {
    file: "routes/LazyLoad.ts",
    content: `
import { lazy } from 'react';
${result.lazyLoad.join("\n\n")}
    `
  };

  // Create single AdminHeader file
  result.header = {
    file: "components/AdminHeader/AdminHeader.tsx",
    content: `
import React from "react";
import { Link, NavLink } from "react-router-dom";
import { PiUsersThreeFill } from "react-icons/pi";
import MkdSDK from "@/utils/MkdSDK";
import { MdDashboard } from "react-icons/md";
import { GlobalContext } from "@/context/Global";
import { AuthContext, tokenExpireError } from "@/context/Auth";

let sdk = new MkdSDK();

const NAV_ITEMS = [
  {
    to: "/admin/dashboard",
    text: "Dashboard",
    icon: <MdDashboard className="text-xl text-[#A8A8A8]" />,
    value: "admin",
    visible: true
  },
  ${navItems.join(",\n  ")},
  {
    to: "/admin/profile",
    text: "Profile",
    icon: <PiUsersThreeFill className="text-xl text-[#A8A8A8]" />,
    value: "profile",
    visible: true
  }
];

export const AdminHeader = () => {
  const {
    state: { isOpen, path },
    dispatch: globalDispatch,
  } = React.useContext(GlobalContext);
  const { state: authState, dispatch } = React.useContext(AuthContext);
  const [openDropdown, setOpenDropdown] = React.useState(false);
  const [isHovering, setIsHovering] = React.useState(false);

  let toggleOpen = (open) => {
    globalDispatch({
      type: "OPEN_SIDEBAR",
      payload: { isOpen: open },
    });
  };
  
  async function fetchData() {
    try {
      const result = await sdk.getProfile();
      dispatch({
        type: "UPDATE_PROFILE",
        payload: result,
      });
    } catch (error) {
      console.log("Error", error);
      tokenExpireError(dispatch, error.message);
    }
  }

  React.useEffect(() => {
    fetchData();
  }, []);

  return (
    <>
      <div
        className={\`z-50 flex max-h-screen flex-1 flex-col border border-[#E0E0E0] bg-white py-4 text-[#A8A8A8] transition-all \${
          isOpen
            ? "fixed h-screen w-[15rem] min-w-[15rem] max-w-[15rem] md:relative"
            : "relative min-h-screen w-[4.2rem] min-w-[4.2rem] max-w-[4.2rem] bg-black text-white"
        }\`}
      >
        <div className={\`text-[#393939] \${isOpen ? "flex w-full" : "flex items-center justify-center"}\`}>
          <div></div>
          {isOpen && (
            <div className="text-2xl font-bold">
              <Link to="/">
                <h4 className="flex cursor-pointer items-center px-4 pb-4 font-sans font-bold">
                  Baas Brand{" "}
                </h4>
              </Link>
            </div>
          )}
        </div>

        <div className="h-fit w-auto flex-1">
          <div className="sidebar-list w-auto">
            <ul className="flex flex-wrap px-2 text-sm">
              {NAV_ITEMS.filter(it => it.visible).map((item) => (
                <li className="block w-full list-none" key={item.value}>
                  <NavLink
                    to={item.to}
                    className={\`\${path === item.value ? "active-nav" : ""}\`}
                  >
                    <div className="flex items-center gap-3">
                      {item.icon}
                      {isOpen && <span>{item.text}</span>}
                    </div>
                  </NavLink>
                </li>
              ))}
            </ul>
          </div>
        </div>

        <div className="flex justify-end">
          <div className="mr-3 cursor-pointer rounded-lg border border-[#E0E0E0] bg-white p-2 text-2xl text-gray-400">
            <span onClick={() => toggleOpen(!isOpen)}>
              <svg
                className={\`transition-transform \${!isOpen ? "rotate-180" : ""}\`}
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
              >
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12ZM10.4142 11L11.7071 9.70711C12.0976 9.31658 12.0976 8.68342 11.7071 8.29289C11.3166 7.90237 10.6834 7.90237 10.2929 8.29289L7.82322 10.7626C7.13981 11.446 7.13981 12.554 7.82322 13.2374L10.2929 15.7071C10.6834 16.0976 11.3166 16.0976 11.7071 15.7071C12.0976 15.3166 12.0976 14.6834 11.7071 14.2929L10.4142 13H16C16.5523 13 17 12.5523 17 12C17 11.4477 16.5523 11 16 11H10.4142Z"
                  fill="#A8A8A8"
                />
              </svg>
            </span>
          </div>
        </div>
      </div>
    </>
  );
};

export default AdminHeader;
    `
  };

  return result;
}

module.exports = {
  buildAddPage,
  buildEditPage,
  buildListPage,
  buildViewPage,
  buildRoutes,
  buildNavItems,
  buildLazyLoadRoutes,
  generateAdminPages
};
