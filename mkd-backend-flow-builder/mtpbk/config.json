{"settings": {"globalKey": "key_1734835041254_5nflydnk6", "databaseType": "mysql", "authType": "jwt", "timezone": "America/New_York", "dbHost": "localhost", "dbPort": "3306", "dbUser": "root", "dbPassword": "root", "dbName": "database_2024-12-22", "id": "project_1734835041254_zydcxsqwv", "model_namespace": "_singingbird", "payment_option": "none", "isPWA": true}, "models": [{"id": "model_1734835047616_c01kea7q9", "name": "company", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "name", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "logo", "type": "string", "defaultValue": "", "validation": ""}, {"name": "address", "type": "string", "defaultValue": "", "validation": ""}, {"name": "phone", "type": "string", "defaultValue": "", "validation": "phone"}, {"name": "status", "type": "mapping", "mapping": "0:Active,1:Inactive,2:Suspend", "defaultValue": "0", "validation": ""}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}, {"id": "model_1734835047616_al96tijas", "name": "user", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "email", "type": "string", "defaultValue": "", "validation": "required,email"}, {"name": "password", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "login_type", "type": "mapping", "mapping": "0:Regular,1:Google,2:Microsoft,3:Apple", "defaultValue": "0", "validation": "required,min:0,max:3"}, {"name": "role_id", "type": "string", "defaultValue": "", "validation": ""}, {"name": "data", "type": "json", "defaultValue": "", "validation": ""}, {"name": "status", "type": "mapping", "mapping": "0:Active,1:Inactive,2:Suspend", "defaultValue": "0", "validation": "required,positive"}, {"name": "verify", "type": "boolean", "defaultValue": 0, "validation": "required"}, {"name": "company_id", "type": "integer", "defaultValue": "0", "validation": ""}, {"name": "created_at", "type": "date", "defaultValue": "", "validation": "date"}]}, {"id": "model_1734835047616_dabn11p58", "name": "cms", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "label", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "type", "type": "mapping", "mapping": "0:Text,1:Number,2:Image,3:Raw", "defaultValue": "0", "validation": "required"}, {"name": "value", "type": "long text", "defaultValue": "", "validation": "required"}]}, {"id": "model_1734835052283_jjrxf2c9p", "name": "inventory", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "name", "type": "string", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "quantity", "type": "integer", "defaultValue": "", "validation": "required,integer", "validationOptions": {}, "relation": ""}, {"name": "status", "type": "mapping", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": "", "mapping": "0:inactive,1:active,2:intransit"}, {"name": "description", "type": "long text", "defaultValue": "", "validation": "", "validationOptions": {}, "relation": ""}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "expiry", "type": "integer", "defaultValue": "", "validation": "required,negative", "validationOptions": {}, "relation": ""}, {"name": "manufactured_version", "type": "double", "defaultValue": "", "validation": "required,decimal", "validationOptions": {}, "relation": ""}, {"name": "warehouse_id", "type": "integer", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}], "relations": []}, {"id": "model_1734835224274_r5q1pophi", "name": "warehouse", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "name", "type": "string", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "website", "type": "string", "defaultValue": "", "validation": "required,url", "validationOptions": {}, "relation": ""}], "relations": []}, {"id": "model_1734835257622_i0q1yh3fz", "name": "accounting", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "inventory_id", "type": "integer", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "description", "type": "string", "defaultValue": "", "validation": "required,pattern:^[a-zA-Z]+$", "validationOptions": {}, "relation": ""}, {"name": "quantity", "type": "integer", "defaultValue": "", "validation": "required,integer", "validationOptions": {}, "relation": ""}, {"name": "status", "type": "mapping", "defaultValue": "0", "validation": "required", "validationOptions": {}, "relation": "", "mapping": "0:new,1:completed"}], "relations": []}, {"id": "model_1734835336446_qi3vtvaa4", "name": "accounting_inventory_secret", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "inventory_id", "type": "integer", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": "inventory"}, {"name": "accounting_id", "type": "integer", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": "accounting"}], "relations": ["accounting"]}, {"id": "model_1734835541220_edyudr05b", "name": "hidden", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "name", "type": "string", "defaultValue": "", "validation": "required,length:10,alphanumeric", "validationOptions": {}, "relation": ""}, {"name": "code", "type": "integer", "defaultValue": "", "validation": "required,enum:1,2,3", "validationOptions": {}, "relation": ""}], "relations": []}, {"name": "ValidationTest", "fields": [{"name": "id", "type": "primary key"}, {"name": "requiredField", "type": "string", "validation": "required"}, {"name": "emailField", "type": "string", "validation": "email"}, {"name": "lengthField", "type": "string", "validation": ["length", {"length": 5}]}, {"name": "enum<PERSON><PERSON>", "type": "string", "validation": ["enum", {"enum": ["a", "b", "c"]}]}, {"name": "patternField", "type": "string", "validation": ["pattern", {"pattern": "^[A-Z]+$"}]}, {"name": "positiveField", "type": "integer", "validation": "positive"}, {"name": "negativeField", "type": "integer", "validation": "negative"}, {"name": "integerField", "type": "integer", "validation": "integer"}, {"name": "decimalField", "type": "double", "validation": "decimal"}, {"name": "alphanumericField", "type": "string", "validation": "alphanumeric"}, {"name": "uuidField", "type": "uuid", "validation": "uuid"}, {"name": "jsonField", "type": "json", "validation": "json"}, {"name": "dateField", "type": "date", "validation": "date"}, {"name": "phoneField", "type": "string", "validation": "phone"}, {"name": "relatedField", "type": "mapping", "relation": "related"}]}, {"id": "model_1735148740278_aox522fx7", "name": "tokens", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "user_id", "type": "foreign key", "defaultValue": "", "validation": "required"}, {"name": "token", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "type", "type": "mapping", "mapping": "0:Access,1:<PERSON><PERSON><PERSON>,2:<PERSON><PERSON>,3:<PERSON><PERSON><PERSON>,4:<PERSON>", "defaultValue": "0", "validation": "required,enum:0,1,2,3,4"}, {"name": "data", "type": "json", "defaultValue": "", "validation": ""}, {"name": "status", "type": "mapping", "mapping": "0:Inactive,1:Active", "defaultValue": "1", "validation": "required,enum:0,1"}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}, {"name": "updated_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}, {"name": "expired_at", "type": "timestamp", "defaultValue": "", "validation": "date"}]}, {"id": "model_1736076082026_0l0c39rkq", "name": "preference", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "first_name", "type": "string", "defaultValue": "", "validation": ""}, {"name": "last_name", "type": "string", "defaultValue": "", "validation": ""}, {"name": "phone", "type": "string", "defaultValue": "", "validation": ""}, {"name": "photo", "type": "string", "defaultValue": "", "validation": ""}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "required"}]}, {"id": "model_1736076082026_30nfu8b2d", "name": "uploads", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "url", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "caption", "type": "string", "defaultValue": "", "validation": ""}, {"name": "user_id", "type": "foreign key", "defaultValue": "", "validation": ""}, {"name": "width", "type": "integer", "defaultValue": "", "validation": ""}, {"name": "height", "type": "integer", "defaultValue": "", "validation": ""}, {"name": "type", "type": "mapping", "mapping": "0:Image,1:s3,2:Video,3:base64", "defaultValue": "0", "validation": "required,enum:0,1,2,3"}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}, {"name": "updated_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}, {"id": "model_1736076082026_4lmglmtjq", "name": "job", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "task", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "arguments", "type": "json", "defaultValue": "", "validation": ""}, {"name": "time_interval", "type": "string", "defaultValue": "once", "validation": ""}, {"name": "retries", "type": "integer", "defaultValue": "1", "validation": ""}, {"name": "status", "type": "mapping", "mapping": "0:Pending,1:Failed,2:Processing,3:Completed", "defaultValue": "0", "validation": ""}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}, {"name": "updated_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}, {"id": "model_1736076134011_4l370qe0d", "name": "switchs", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": "", "validationOptions": {}}, {"name": "label", "type": "string", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "options", "type": "integer", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": ""}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "required", "validationOptions": {}, "relation": "user"}, {"name": "status", "type": "mapping", "defaultValue": "0", "validation": "required", "validationOptions": {}, "relation": "", "mapping": "0:off,1:on"}], "relations": []}], "routes": [{"id": "route_1734835558351_p7t0wlpbr", "name": "Get All hidden", "method": "GET", "url": "/api/hidden", "flowData": {"nodes": [{"id": "url_node_1734835558351", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Get All hidden", "path": "/api/hidden", "method": "GET"}}, {"id": "db_find_node_1734835558351", "type": "db-find", "position": {"x": 100, "y": 200}, "data": {"label": "Database Find", "model": "hidden", "operation": "find<PERSON>any", "query": "SELECT * FROM hidden", "resultVar": "hiddenResult"}}, {"id": "output_node_1734835558351", "type": "outputs", "position": {"x": 100, "y": 300}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "id", "type": "number"}, {"name": "name", "type": "string"}, {"name": "code", "type": "integer"}], "statusCode": 200}}], "edges": [{"id": "url-to-db_1734835558351", "source": "url_node_1734835558351", "target": "db_find_node_1734835558351"}, {"id": "db-to-output_1734835558351", "source": "db_find_node_1734835558351", "target": "output_node_1734835558351"}]}}, {"id": "route_1734835558351_umk8qzgaq", "name": "Get One hidden", "method": "GET", "url": "/api/hidden/:id", "flowData": {"nodes": [{"id": "url_node_1734835558351_1", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Get One hidden", "path": "/api/hidden/:id", "method": "GET", "queryFields": [{"name": "id", "type": "number", "validation": "required"}]}}, {"id": "db_query_node_1734835558351_1", "type": "db-query", "position": {"x": 100, "y": 200}, "data": {"label": "Database Find", "model": "hidden", "operation": "findOne", "query": "SELECT * FROM hidden WHERE id=id", "resultVar": "hiddenOneResult"}}, {"id": "output_node_1734835558351_1", "type": "outputs", "position": {"x": 100, "y": 300}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "id", "type": "number"}, {"name": "name", "type": "string"}, {"name": "code", "type": "integer"}, {"name": "error", "type": "boolean"}], "resultVar": "hiddenOneResult", "statusCode": 200}}], "edges": [{"id": "url-to-db_1734835558351_1", "source": "url_node_1734835558351_1", "target": "db_query_node_1734835558351_1"}, {"id": "db-to-output_1734835558351_1", "source": "db_query_node_1734835558351_1", "target": "output_node_1734835558351_1"}]}}, {"id": "route_1734835558351_vq257bfqw", "name": "Create hidden", "method": "POST", "url": "/api/hidden", "flowData": {"nodes": [{"id": "url_node_1734835558351_3", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Create hidden", "path": "/api/hidden", "method": "POST", "fields": [{"name": "name", "type": "string", "validation": ""}, {"name": "code", "type": "integer", "validation": ""}]}}, {"id": "db_insert_node_1734835558351_3", "type": "db-insert", "position": {"x": 100, "y": 200}, "data": {"label": "Database Insert", "model": "hidden", "operation": "create", "query": "INSERT INTO hidden (name, code)\n                      VALUES (:name, :code)", "resultVar": "hiddenCreateResult"}}, {"id": "output_node_1734835558351_3", "type": "outputs", "position": {"x": 100, "y": 300}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "number"}], "statusCode": 200}}], "edges": [{"id": "url-to-db_1734835558351_3", "source": "url_node_1734835558351_3", "target": "db_insert_node_1734835558351_3"}, {"id": "db-to-output_1734835558351_3", "source": "db_insert_node_1734835558351_3", "target": "output_node_1734835558351_3"}]}}, {"id": "route_1734835558352_dna6umo13", "name": "Update hidden", "method": "PUT", "url": "/api/hidden/:id", "flowData": {"nodes": [{"id": "url_node_1734835558351_4", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Update hidden", "path": "/api/hidden/:id", "method": "PUT", "queryFields": [{"name": "id", "type": "number", "validation": "required"}], "fields": [{"name": "name", "type": "string", "validation": "required"}, {"name": "code", "type": "integer", "validation": "required"}]}}, {"id": "db_update_node_1734835558351_4", "type": "db-update", "position": {"x": 100, "y": 200}, "data": {"label": "Database Update", "model": "hidden", "operation": "update", "idField": "id", "query": "UPDATE hidden SET name=:name, code=:code WHERE id=:id", "resultVar": "hiddenUpdateResult"}}, {"id": "output_node_1734835558351_4", "type": "outputs", "position": {"x": 100, "y": 300}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "number"}], "statusCode": 200}}], "edges": [{"id": "url-to-db_1734835558351_4", "source": "url_node_1734835558351_4", "target": "db_update_node_1734835558351_4"}, {"id": "db-to-output_1734835558351_4", "source": "db_update_node_1734835558351_4", "target": "output_node_1734835558351_4"}]}}, {"id": "route_1734835558351_578vyr31y", "name": "Delete One hidden", "method": "DELETE", "url": "/api/hidden/:id", "flowData": {"nodes": [{"id": "url_node_1734835558351_1", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Delete One hidden", "path": "/api/hidden/:id", "method": "DELETE", "queryFields": [{"name": "id", "type": "number", "validation": "required"}]}}, {"id": "db_delete_node_1734835558351_2", "type": "db-delete", "position": {"x": 100, "y": 200}, "data": {"label": "Database Delete", "model": "hidden", "operation": "delete", "query": "DELETE FROM hidden WHERE id=id", "resultVar": "hiddenDeleteResult"}}, {"id": "output_node_1734835558351_2", "type": "outputs", "position": {"x": 100, "y": 300}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "integer"}], "statusCode": 200}}], "edges": [{"id": "url-to-db_1734835558351_1", "source": "url_node_1734835558351_1", "target": "db_delete_node_1734835558351_2"}, {"id": "db-to-output_1734835558351_2", "source": "db_delete_node_1734835558351_2", "target": "output_node_1734835558351_2                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              "}]}}, {"id": "route_1735706765196_fd7leiser", "name": "mock", "flowData": {"nodes": [{"id": "url_node_1735706776881", "type": "mock-api", "position": {"x": 100, "y": 100}, "data": {"label": "Mock API", "apiname": "mock", "path": "/api/mockapi", "method": "POST", "description": "this is mock api", "fields": [{"name": "a1", "type": "string", "validation": ""}, {"name": "a2", "type": "integer", "validation": ""}, {"name": "a3", "type": "boolean", "validation": ""}, {"name": "a4", "type": "date", "validation": ""}, {"name": "a5", "type": "datetime", "validation": ""}, {"name": "a6", "type": "file", "validation": ""}, {"name": "a7", "type": "array", "value": "string"}, {"name": "a8", "type": "array", "value": {"type": "object", "fields": [{"name": "a9", "type": "string"}, {"name": "a10", "type": "number"}, {"name": "a11", "type": "object", "fields": [{"name": "a12", "type": "string"}]}]}}, {"name": "a9", "type": "object", "value": [{"name": "a13", "type": "string"}, {"name": "a14", "type": "object", "fields": [{"name": "a15", "type": "string"}]}]}], "queryFields": [{"name": "a16", "type": "integer", "validation": ""}], "responseFields": [{"name": "error", "type": "boolean", "validation": ""}, {"name": "model", "type": "object", "value": [{"name": "message", "type": "string"}, {"name": "page", "type": "integer"}, {"name": "obj", "type": "object", "fields": [{"name": "a16", "type": "string"}, {"name": "a17", "type": "number"}, {"name": "obj2", "type": "object"}, {"name": "a18", "type": "string"}]}]}], "authType": "bearer", "outputType": "json", "statusCode": "201"}, "width": 180, "height": 56, "selected": false, "dragging": false}], "edges": []}}, {"id": "route_1735711320512_e5xtvptwe", "name": "a", "flowData": {"nodes": [{"id": "url_node_1735711325783", "type": "url", "position": {"x": -30, "y": -15}, "data": {"label": "URL", "apiname": "a", "path": "/api/a", "method": "GET", "fields": [], "queryFields": [{"name": "id", "type": "integer", "validation": "required"}]}, "width": 180, "height": 56, "selected": false, "dragging": false, "positionAbsolute": {"x": -30, "y": -15}}, {"id": "node_1735711337597", "type": "auth", "position": {"x": -45, "y": 75}, "data": {"label": "<PERSON><PERSON>", "fields": [], "queryFields": [], "authType": "<PERSON><PERSON><PERSON><PERSON>"}, "width": 180, "height": 56, "selected": false, "positionAbsolute": {"x": -45, "y": 75}, "dragging": false}, {"id": "node_1735711347758", "type": "logic", "position": {"x": -45, "y": 150}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//step 1 do x\n\n//step 2 do y"}, "width": 180, "height": 56, "selected": false, "dragging": false}, {"id": "node_1735711363929", "type": "db-query", "position": {"x": -45, "y": 240}, "data": {"label": "Db query", "fields": [], "queryFields": [], "model": "company", "operation": "findOne", "query": "SELECT * FROM company WHERE id=:id", "resultVar": "result"}, "width": 180, "height": 56, "selected": false, "dragging": false}, {"id": "node_1735711386199", "type": "outputs", "position": {"x": -30, "y": 330}, "data": {"label": "Outputs", "fields": [], "queryFields": [], "outputType": "json", "resultVar": "result", "statusCode": "201"}, "width": 180, "height": 56, "selected": false, "dragging": false}], "edges": [{"source": "url_node_1735711325783", "sourceHandle": null, "target": "node_1735711337597", "targetHandle": null, "id": "reactflow__edge-url_node_1735711325783-node_1735711337597"}, {"source": "node_1735711337597", "sourceHandle": null, "target": "node_1735711347758", "targetHandle": null, "id": "reactflow__edge-node_1735711337597-node_1735711347758"}, {"source": "node_1735711347758", "sourceHandle": null, "target": "node_1735711363929", "targetHandle": null, "id": "reactflow__edge-node_1735711347758-node_1735711363929"}, {"source": "node_1735711363929", "sourceHandle": null, "target": "node_1735711386199", "targetHandle": null, "id": "reactflow__edge-node_1735711363929-node_1735711386199"}]}}, {"id": "route_1736076210652_jefx076w5", "name": "Get All switchs", "method": "GET", "url": "/api/switchs", "flowData": {"nodes": [{"id": "url_node_1736076210652", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Get All switchs", "path": "/api/switchs", "method": "GET"}}, {"id": "auth_node_1736076210652", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_find_node_1736076210652", "type": "db-find", "position": {"x": 100, "y": 300}, "data": {"label": "Database Find", "model": "switchs", "operation": "find<PERSON>any", "query": "SELECT * FROM switchs", "resultVar": "switchResult"}}, {"id": "logic_node_1736076210652", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1736076210652", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "id", "type": "number"}, {"name": "label", "type": "string"}, {"name": "options", "type": "integer"}, {"name": "user_id", "type": "integer"}, {"name": "status", "type": "mapping"}], "resultVar": "switchResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1736076210652", "source": "url_node_1736076210652", "target": "auth_node_1736076210652"}, {"id": "auth-to-db_1736076210652", "source": "auth_node_1736076210652", "target": "db_find_node_1736076210652"}, {"id": "db-to-logic_1736076210652", "source": "db_find_node_1736076210652", "target": "logic_node_1736076210652"}, {"id": "logic-to-output_1736076210652", "source": "logic_node_1736076210652", "target": "output_node_1736076210652"}]}}, {"id": "route_1736076210652_gr0s2nqxf", "name": "Get One switchs", "method": "GET", "url": "/api/switchs/:id", "flowData": {"nodes": [{"id": "url_node_1736076210652_1", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Get One switchs", "path": "/api/switchs/:id", "method": "GET", "queryFields": [{"name": "id", "type": "number", "validation": "required"}]}}, {"id": "auth_node_1736076210652_1", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_query_node_1736076210652_1", "type": "db-query", "position": {"x": 100, "y": 300}, "data": {"label": "Database Find", "model": "switchs", "operation": "findOne", "query": "SELECT * FROM switchs WHERE id=id", "resultVar": "switchOneResult"}}, {"id": "logic_node_1736076210652_1", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1736076210652_1", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "id", "type": "number"}, {"name": "label", "type": "string"}, {"name": "options", "type": "integer"}, {"name": "user_id", "type": "integer"}, {"name": "status", "type": "mapping"}, {"name": "error", "type": "boolean"}], "resultVar": "switchOneResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1736076210652_1", "source": "url_node_1736076210652_1", "target": "auth_node_1736076210652_1"}, {"id": "auth-to-db_1736076210652_1", "source": "auth_node_1736076210652_1", "target": "db_query_node_1736076210652_1"}, {"id": "db-to-logic_1736076210652_1", "source": "db_query_node_1736076210652_1", "target": "logic_node_1736076210652_1"}, {"id": "logic-to-output_1736076210652_1", "source": "logic_node_1736076210652_1", "target": "output_node_1736076210652_1"}]}}, {"id": "route_1736076210653_ybk49v9ou", "name": "Create switchs", "method": "POST", "url": "/api/switchs", "flowData": {"nodes": [{"id": "url_node_1736076210652_3", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Create switchs", "path": "/api/switchs", "method": "POST", "fields": [{"name": "label", "type": "string", "validation": ""}, {"name": "options", "type": "integer", "validation": ""}, {"name": "user_id", "type": "integer", "validation": ""}, {"name": "status", "type": "mapping", "validation": ""}]}}, {"id": "auth_node_1736076210652_3", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_insert_node_1736076210652_3", "type": "db-insert", "position": {"x": 100, "y": 300}, "data": {"label": "Database Insert", "model": "switchs", "operation": "create", "query": "INSERT INTO switchs (label, options, user_id, status)\n                      VALUES (:label, :options, :user_id, :status)", "resultVar": "switchCreateResult"}}, {"id": "logic_node_1736076210652_3", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1736076210652_3", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "number"}], "resultVar": "switchCreateResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1736076210652_3", "source": "url_node_1736076210652_3", "target": "auth_node_1736076210652_3"}, {"id": "auth-to-db_1736076210652_3", "source": "auth_node_1736076210652_3", "target": "db_insert_node_1736076210652_3"}, {"id": "db-to-logic_1736076210652_3", "source": "db_insert_node_1736076210652_3", "target": "logic_node_1736076210652_3"}, {"id": "logic-to-output_1736076210652_3", "source": "logic_node_1736076210652_3", "target": "output_node_1736076210652_3"}]}}, {"id": "route_1736076210653_xs33phrt5", "name": "Update switchs", "method": "PUT", "url": "/api/switchs/:id", "flowData": {"nodes": [{"id": "url_node_1736076210652_4", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Update switchs", "path": "/api/switchs/:id", "method": "PUT", "queryFields": [{"name": "id", "type": "number", "validation": "required"}], "fields": [{"name": "label", "type": "string", "validation": "required"}, {"name": "options", "type": "integer", "validation": "required"}, {"name": "user_id", "type": "integer", "validation": "required"}, {"name": "status", "type": "mapping", "validation": "required"}]}}, {"id": "auth_node_1736076210652_4", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_update_node_1736076210652_4", "type": "db-update", "position": {"x": 100, "y": 300}, "data": {"label": "Database Update", "model": "switchs", "operation": "update", "idField": "id", "query": "UPDATE switchs SET label=:label, options=:options, user_id=:user_id, status=:status WHERE id=:id", "resultVar": "switchUpdateResult"}}, {"id": "logic_node_1736076210652_4", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1736076210652_4", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "number"}], "resultVar": "switchUpdateResult", "statusCode": 200}}], "edges": [{"id": "url-to-auth_1736076210652_4", "source": "url_node_1736076210652_4", "target": "auth_node_1736076210652_4"}, {"id": "auth-to-db_1736076210652_4", "source": "auth_node_1736076210652_4", "target": "db_update_node_1736076210652_4"}, {"id": "db-to-logic_1736076210652_4", "source": "db_update_node_1736076210652_4", "target": "logic_node_1736076210652_4"}, {"id": "logic-to-output_1736076210652_4", "source": "logic_node_1736076210652_4", "target": "output_node_1736076210652_4"}]}}, {"id": "route_1736076210653_m886sh3ha", "name": "Delete One switchs", "method": "DELETE", "url": "/api/switchs/:id", "flowData": {"nodes": [{"id": "url_node_1736076210652_2", "type": "url", "position": {"x": 100, "y": 100}, "data": {"label": "Url", "apiname": "Delete One switchs", "path": "/api/switchs/:id", "method": "DELETE", "queryFields": [{"name": "id", "type": "number", "validation": "required"}]}}, {"id": "auth_node_1736076210652_2", "type": "auth", "position": {"x": 100, "y": 200}, "data": {"label": "<PERSON><PERSON>", "authType": "none"}}, {"id": "db_delete_node_1736076210652_2", "type": "db-delete", "position": {"x": 100, "y": 300}, "data": {"label": "Database Delete", "model": "switchs", "operation": "delete", "query": "DELETE FROM switchs WHERE id=id", "resultVar": "switchDeleteResult"}}, {"id": "logic_node_1736076210652_2", "type": "logic", "position": {"x": 100, "y": 400}, "data": {"label": "Logic", "fields": [], "queryFields": [], "code": "//commented logic"}}, {"id": "output_node_1736076210652_2", "type": "outputs", "position": {"x": 100, "y": 500}, "data": {"label": "Response", "outputType": "json", "fields": [{"name": "error", "type": "boolean"}, {"name": "id", "type": "integer"}], "statusCode": 200, "resultVar": "switchDeleteResult"}}], "edges": [{"id": "url-to-auth_1736076210652_2", "source": "url_node_1736076210652_2", "target": "auth_node_1736076210652_2"}, {"id": "auth-to-db_1736076210652_2", "source": "auth_node_1736076210652_2", "target": "db_delete_node_1736076210652_2"}, {"id": "db-to-logic_1736076210652_2", "source": "db_delete_node_1736076210652_2", "target": "logic_node_1736076210652_2"}, {"id": "logic-to-output_1736076210652_2", "source": "logic_node_1736076210652_2", "target": "output_node_1736076210652_2"}]}}], "roles": [{"id": "role_admin_1736076082026", "name": "Super Admin", "slug": "super_admin", "permissions": {"routes": ["route_1736076210652_jefx076w5", "route_1736076210652_gr0s2nqxf", "route_1736076210653_ybk49v9ou", "route_1736076210653_xs33phrt5", "route_1736076210653_m886sh3ha", "route_1734835558351_p7t0wlpbr", "route_1734835558351_umk8qzgaq", "route_1734835558351_vq257bfqw", "route_1734835558352_dna6umo13", "route_1734835558351_578vyr31y"], "canCreateUsers": true, "canEditUsers": true, "canDeleteUsers": true, "canManageRoles": true, "canLogin": true, "canRegister": true, "canForgot": false, "canReset": false, "canGoogleLogin": true, "canAppleLogin": false, "canMicrosoftLogin": false, "canMagicLinkLogin": false, "needs2FA": true, "canSetPermissions": true, "canPreference": true, "canVerifyEmail": false, "canUpload": true, "canStripe": false, "canStripeWebhook": false, "canRealTime": true, "canAI": true, "canUpdateEmail": true, "canUpdatePassword": true, "canUpdateOtherUsers": true, "treeql": {"enabled": true, "models": {"user": {"allowed": true, "blacklistedFields": ["password"], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "preference": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "uploads": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "cms": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "switchs": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}}}}}, {"id": "role_company_admin_1734835047616", "name": "Company Admin", "slug": "company_admin", "permissions": {"authRequired": true, "routes": [], "canCreateUsers": true, "canEditUsers": true, "canDeleteUsers": true, "canManageRoles": true, "canLogin": true, "canRegister": true, "canForgot": false, "canReset": false, "canGoogleLogin": true, "canAppleLogin": false, "canMicrosoftLogin": false, "canMagicLinkLogin": false, "needs2FA": true, "canSetPermissions": true, "canPreference": true, "canVerifyEmail": true, "canUpload": true, "canStripe": true, "canStripeWebhook": true, "canRealTime": true, "canAI": true, "canUpdateEmail": true, "canUpdatePassword": true, "canUpdateOtherUsers": true, "treeql": {"enabled": true, "models": {"accounting": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "warehouse": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "inventory": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "cms": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "company": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "user": {"allowed": true, "blacklistedFields": ["password"], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}}}, "companyScoped": true}}, {"id": "role_member_1736076082026", "name": "Member", "slug": "member", "permissions": {"routes": ["route_1736076210652_jefx076w5", "route_1736076210652_gr0s2nqxf"], "canCreateUsers": false, "canEditUsers": false, "canDeleteUsers": false, "canManageRoles": false, "canLogin": true, "canRegister": true, "canForgot": false, "canReset": false, "canGoogleLogin": true, "canAppleLogin": false, "canMicrosoftLogin": false, "canMagicLinkLogin": false, "needs2FA": false, "canSetPermissions": false, "canPreference": true, "canVerifyEmail": true, "canUpload": true, "canStripe": false, "canStripeWebhook": false, "canRealTime": true, "canAI": true, "canUpdateEmail": true, "canUpdatePassword": true, "canUpdateOtherUsers": false, "treeql": {"enabled": true, "models": {"cms": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": false, "put": false, "delete": false, "paginate": true, "join": false}}, "uploads": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": false, "delete": false, "paginate": true, "join": true}}, "preference": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": false, "paginate": true, "join": true}}, "switchs": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": false, "put": false, "delete": false, "paginate": false, "join": false}}, "user": {"allowed": false, "blacklistedFields": ["password"], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}}}, "companyScoped": false}}]}