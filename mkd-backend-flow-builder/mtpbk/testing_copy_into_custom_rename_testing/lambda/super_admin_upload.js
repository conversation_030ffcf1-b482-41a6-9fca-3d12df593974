const UploadService = require("../../../baas/services/UploadService");
const { getLocalPath, sqlDateFormat, sqlDateTimeFormat, sizeOfRemote } = require("../../../baas/services/UtilService");
const sizeOf = require("image-size");
const TokenMiddleware = require("../../../baas/middleware/TokenMiddleware");
const upload = UploadService.local_upload();
const uploadS3 = UploadService.s3_upload();
const uploadS3Public = UploadService.s3_upload_public();
const fs = require("fs");

const imageMiddlewares = [
  TokenMiddleware(),
  upload.single("file")
];

const imageMiddlewaresS3 = [
  TokenMiddleware(),
  uploadS3.single("file"),
];

const imagesMiddlewares = [
  TokenMiddleware(),
  upload.array("files"),
];
const imagesMiddlewaresS3 = [
  TokenMiddleware(),
  uploadS3.array("files"),
];

const imagesPublicMiddlewaresS3 = [
  uploadS3Public.array("files"),
];

module.exports = function (app) {
  app.post("/v1/api/testing/super_admin/lambda/upload", imageMiddlewares, async function (req, res) {
    try {

      const Role = require(`../roles/super_admin`);
      if (!Role.canUpload()) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      const url = getLocalPath(req.file.path);

      let params = {
        url: url,
        user_id: req.user_id || null,
        caption: req.body.caption || null,
        type: 1,
        width: 0,
        height: 0,
      };
      const whitelist = ["image/png", "image/jpeg", "image/jpg"];

      const uploadedfile = fs.readFileSync(req.file.path);

      if (whitelist.includes(req.file.mimetype)) {
        const dimensions = sizeOf(uploadedfile);
        params.width = dimensions.width;
        params.height = dimensions.height;
        params.type = 0;
      }

      let sdk = req.sdk;
      sdk.projectId("testing");
      sdk.setTable("uploads");

      const result = await sdk.create("uploads", {
        url: params.url,
        caption: params.caption,
        user_id: req.user_id,
        width: params.width,
        height: params.height,
        type: params.type,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date()),
      });

      return res.status(201).json({ id: result, url });
    } catch (error) {
      return res.status(500).json({ error: true, message: error.message });
    }
  });

  app.post("/v1/api/lambda/s3/upload", imageMiddlewaresS3, async function (req, res) {
    try {

      const Role = require(`../roles/super_admin`);
      if (!Role.canUpload()) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      const url = req.file.location;

      let params = {
        url: url,
        user_id: req.user_id || null,
        caption: req.body.caption || null,
        type: 1,
        width: 0,
        height: 0,
      };
      const whitelist = ["image/png", "image/jpeg", "image/jpg"];

      if (whitelist.includes(req.file.mimetype)) {
        const dimensions = await sizeOfRemote(url);
        params.width = dimensions.width;
        params.height = dimensions.height;
        params.type = 1;
      }

      let sdk = req.sdk;
      sdk.projectId("testing");
      sdk.setTable("uploads");

      const result = sdk.create("uploads", {
        url: params.url,
        caption: params.caption,
        user_id: req.user_id,
        width: params.width,
        height: params.height,
        type: params.type,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date()),
      });

      res.set('Content-Type', 'application/json');
      return res.status(201).json({ id: result, url });
    } catch (error) {
      return res.status(500).json({ error: true, message: error.message });
    }
  });

  /**
   * uploads
   */
  app.post("/v1/api/testing/super_admin/lambda/uploads/only", imagesMiddlewares, function (req, res, next) {
    try {
      const Role = require(`../roles/super_admin`);
      if (!Role.canUpload()) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }
      let urlArray = [];
      for (const file of req.files) {
        const url = getLocalPath(file.path);
        urlArray.push(url);
      }
      return res.status(201).json({ success: true, attachments: JSON.stringify(urlArray) });
    } catch (error) {
      console.log(error);
      return res.status(500).json({ success: false, error: true, message: error.message });
    }
  });

  /**
   * uploads S3
   */
  app.post("/v1/api/testing/super_admin/lambda/s3/uploads/only", imagesMiddlewaresS3, function (req, res, next) {
    try {
      const Role = require(`../roles/super_admin`);
      if (!Role.canUpload()) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }
      let urlArray = [];
      for (const file of req.files) {
        const url = file.location;
        urlArray.push(url);
      }
      return res.status(201).json({ success: true, attachments: JSON.stringify(urlArray) });
    } catch (error) {
      console.log(error);
      return res.status(500).json({ success: false, error: true, message: error.message });
    }
  });

  /**
   * uploads
   */
  app.post("/v1/api/testing/super_admin/lambda/uploads", imagesMiddlewares, async function (req, res, next) {
    try {
      const Role = require(`../roles/super_admin`);
      if (!Role.canUpload()) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }
      let urlArray = [];
      let urlArrayObject = [];
      let sdk = req.sdk;
      sdk.projectId("testing");
      sdk.table("uploads");

      for (const file of req.files) {
        const url = getLocalPath(file.path);
        urlArray.push(url);
        let width = 0;
        let height = 0;
        let type = 1;

        if (!req.file.mimetype.includes("video")) {
          const uploadedfile = fs.readFileSync(file.path);
          const dimensions = sizeOf(uploadedfile);
          width = dimensions.width;
          height = dimensions.height;
          type = 0;
        }

        const result = await sdk.create("uploads", {
          url: url,
          caption: "",
          user_id: req.user_id,
          width: width,
          height: height,
          type: type,
          create_at: sqlDateFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date()),
        });

        urlArrayObject.push({ id: result, url: url });
      }
      return res.status(201).json({ success: true, attachments: urlArrayObject });
    } catch (error) {
      console.log(error);
      return res.status(500).json({ success: false, error: true, message: error.message });
    }
  });

  /**
   * uploads S3
   */
  app.post("/v1/api/testing/super_admin/lambda/s3/uploads", imagesMiddlewaresS3, async function (req, res, next) {
    try {
      const Role = require(`../roles/super_admin`);
      if (!Role.canUpload()) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }
      let urlArray = [];
      let urlArrayObject = [];
      let sdk = req.sdk;
      sdk.projectId("testing");
      sdk.table("uploads");

      for (const file of req.files) {
        const url = file.location;
        urlArray.push(url);
        let width = 0;
        let height = 0;
        let type = 1;

        if (!file.mimetype.includes("video")) {
          const dimensions = await sizeOfRemote(file.location);
          width = dimensions.width;
          height = dimensions.height;
          type = 0;
        }

        const result = await sdk.create("uploads", {
          url: url,
          caption: "",
          user_id: req.user_id,
          width: width,
          height: height,
          type: type,
          create_at: sqlDateFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date()),
        });

        urlArrayObject.push({ id: result, url: url });
      }
      return res.status(201).json({ success: true, attachments: urlArrayObject });
    } catch (error) {
      return res.status(500).json({ success: false, error: true, message: error.message });
    }
  });

  /**
   * uploads file
   */
  app.post("/v1/api/testing/super_admin/lambda/uploads/file", imagesMiddlewares, async function (req, res, next) {
    try {
      const Role = require(`../roles/super_admin`);
      if (!Role.canUpload()) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      let urlArray = [];
      let urlArrayObject = [];
      let sdk = req.sdk;
      sdk.projectId("testing")
        .table("uploads");

      for (const file of req.files) {
        const url = getLocalPath(file.path);
        urlArray.push(url);
        let width = 0;
        let height = 0;
        let type = 2;

        const result = await sdk.create("uploads", {
          url: url,
          caption: "",
          user_id: req.user_id,
          width: width,
          height: height,
          type: type,
          create_at: sqlDateFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date()),
        });

        urlArrayObject.push({ id: result, url: url });
      }
      return res.status(201).json({ success: true, attachments: urlArrayObject });
    } catch (error) {
      return res.status(500).json({ success: false, error: true, message: error.message });
    }
  });

  /**
   * uploads S3
   */
  app.post("/v1/api/testing/super_admin/lambda/s3/uploads/file", imagesMiddlewaresS3, async function (req, res, next) {
    try {
      let urlArray = [];
      let urlArrayObject = [];
      let sdk = req.sdk;
      sdk.projectId("testing");
      sdk.table("uploads");

      const Role = require(`../roles/super_admin`);
      if (!Role.canUpload()) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      for (const file of req.files) {
        const url = file.location;
        urlArray.push(url);
        let width = 0;
        let height = 0;
        let type = 2;

        const result = await sdk.create("uploads", {
          url: url,
          caption: "",
          user_id: req.user_id,
          width: width,
          height: height,
          type: type,
          create_at: sqlDateFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date()),
        });

        urlArrayObject.push({ id: result, url: url });
      }
      return res.status(201).json({ success: true, attachments: urlArrayObject });
    } catch (error) {
      return res.status(500).json({ success: false, error: true, message: error.message });
    }
  });
};
