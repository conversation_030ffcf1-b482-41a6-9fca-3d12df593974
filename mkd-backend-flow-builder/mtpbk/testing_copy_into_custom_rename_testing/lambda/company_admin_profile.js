const TokenMiddleware = require("../../../baas/middleware/TokenMiddleware");

const middlewares = [
  TokenMiddleware()
];

const handleGetProfile = async (req, res, sdk) => {
  try {
    // Get user data
    sdk.setProjectId("testing");
    sdk.setTable("user");
    const user = await sdk.findOne('user', { id: req.user_id });

    if (!user) {
      return res.status(401).json({
        error: true,
        message: "Invalid Credentials"
      });
    }

    // Get user preferences
    sdk.setTable("preference");
    const preferences = await sdk.findOne('preference', { user_id: req.user_id });

    return res.status(200).json({
      error: false,
      model: {
        id: user.id,
        email: user.email,
        status: user.status,
        role_id: user.role_id,
        ...(preferences && {
          first_name: preferences.first_name ?? "",
          last_name: preferences.last_name ?? "",
          phone: preferences.phone ?? "",
          photo: preferences.photo ?? ""
        })
      }
    });
  } catch (err) {
    console.error('Get profile error:', err);
    return res.status(403).json({
      error: true,
      message: err.message
    });
  }
};

const handleUpdateProfile = async (req, res, sdk) => {
  try {
    // Check if user exists
    sdk.setProjectId("testing");
    sdk.setTable("user");
    const user = await sdk.findOne('user', { id: req.user_id });

    if (!user) {
      return res.status(401).json({
        error: true,
        message: "Invalid Credentials"
      });
    }

    const payload = req.body.payload || req.body;
    const updateData = {
      status: payload.status ?? user.status
    };

    const updateResult = await sdk.updateById(req.user_id, updateData);

    if (!updateResult) {
      return res.status(403).json({
        error: true,
        message: "Update failed"
      });
    }

    return res.status(200).json({
      error: false,
      message: "Updated"
    });
  } catch (err) {
    console.error('Update profile error:', err);
    return res.status(403).json({
      error: true,
      message: err.message
    });
  }
};

module.exports = function (app) {
  app.get("/v1/api/testing/company_admin/lambda/profile", middlewares, async (req, res) => {
    await handleGetProfile(req, res, req.sdk);
  });

  app.post("/v1/api/testing/company_admin/lambda/profile", middlewares, async (req, res) => {
    await handleUpdateProfile(req, res, req.sdk);
  });
};
