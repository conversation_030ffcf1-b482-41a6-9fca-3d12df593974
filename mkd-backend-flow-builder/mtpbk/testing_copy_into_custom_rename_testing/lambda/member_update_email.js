const TokenMiddleware = require("../../../baas/middleware/TokenMiddleware");

const handleEmailUpdate = async (req, res, sdk, userId) => {
  try {
    // Validate email
    if (!req.body.email) {
      return res.status(403).json({
        error: true,
        message: "<PERSON><PERSON> missing",
        validation: [{ field: "email", message: "<PERSON><PERSON> missing" }]
      });
    }

    // Check role permissions
    const Role = require(`../roles/member`);
    if (!Role.permissions.canUpdateEmail) {
      return res.status(403).json({
        error: true,
        message: "Forbidden access"
      });
    }

    sdk.setProjectId("testing");
    sdk.setTable("user");

    // Find and validate user
    const result = await sdk.findOne('user', { id: userId, login_type: 0, status: 1 });

    if (result.length !== 1) {
      return res.status(401).json({
        error: true,
        message: "Invalid Credentials"
      });
    }

    const currentUserRole = req.role;
    const userRole = result[0].role; // Assuming the user role is stored in the result

    if (currentUserRole !== userRole && !Role.canUpdateOtherUsers()) {
      return res.status(403).json({
        error: true,
        message: "Forbidden: You do not have permission to update this email"
      });
    }
    //TODO: validate email is valid email
    await sdk.updateById(
      'user',
      {
        email: email
      },
      userId
    );

    return res.status(200).json({
      error: false,
      message: "Updated"
    });

  } catch (err) {
    console.error('Password update error:', err);
    return res.status(403).json({
      error: true,
      message: err.message
    });
  }
};

const middlewares = [
  TokenMiddleware()
];

module.exports = function (app) {
  // User password update endpoint
  app.post("/v1/api/testing/member/lambda/update/email", middlewares, async function (req, res) {
    await handleEmailUpdate(req, res, req.sdk, req.user_id);
  });
};
