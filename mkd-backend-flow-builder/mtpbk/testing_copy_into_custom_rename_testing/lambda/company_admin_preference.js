const TokenMiddleware = require("../../../baas/middleware/TokenMiddleware");


const middlewares = [
  TokenMiddleware(),
];

module.exports = function (app) {
  app.get("/v1/api/testing/company_admin/lambda/preference", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;

      sdk.setProjectId("testing");
      sdk.setTable("user");

      const Role = require(`../roles/company_admin`);
      if (!Role.canPreference()) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      const result = await sdk.findOne("user", {
        user_id: req.user_id,
        role_id: req.role,
        status: 1
      });

      if (!result) {
        return res.status(401).json({
          error: true,
          message: "Invalid Credentials",
        });
      }

      const preferenceResult = await sdk.findOne("preference", {
        user_id: req.user_id
      });

      return res.status(200).json({
        error: false,
        model: preferenceResult
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message,
      });
    }
  });

  app.post("/v1/api/testing/company_admin/lambda/preference", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;

      const Role = require(`../roles/company_admin`);
      if (!Role.canPreference()) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      sdk.setProjectId("testing");
      sdk.setTable("user");

      const result = await sdk.findOne("user", {
        user_id: req.user_id,
        role_id: req.role,
        status: 1
      });

      if (!result) {
        return res.status(401).json({
          error: true,
          message: "Invalid Credentials",
        });
      }

      if (result.role_id !== req.role) {
        if (!Role.canUpdateOtherUsers()) {
          return res.status(403).json({
            error: true,
            message: "Forbidden access to update other users' preferences"
          });
        }
      }

      sdk.setTable("preference");
      const preferenceModel = require("../models/preference");
      const preference = new preferenceModel(req.body.payload);
      if (!preference.validate()) {
        return res.status(403).json({
          error: true,
          message: "Invalid preference",
          errors: preference.getErrors()
        });
      }

      const updateResult = await sdk.updateById("preference", result.id, req.body.payload);

      if (!updateResult) {
        return res.status(403).json({
          error: true,
          message: "Update failed",
        });
      }

      return res.status(200).json({
        error: false,
        message: "Updated",
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message,
      });
    }
  });
};
