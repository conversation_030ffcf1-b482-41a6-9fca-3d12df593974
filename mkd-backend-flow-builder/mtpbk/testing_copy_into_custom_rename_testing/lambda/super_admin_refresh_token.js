const JwtService = require("../../../baas/services/JwtService");

const middlewares = [
];

module.exports = function (app) {
  app.post("/v1/api/testing/super_admin/lambda/refresh_token", middlewares, async function (req, res) {

    const Role = require(`../roles/super_admin`);
    if (!Role.canLogin()) {
      return res.status(403).json({
        error: true,
        message: "Forbidden access"
      });
    }
    try {
      const token = req.body.refresh_token;
      if (!token) {
        // user did not pass refreshToken
        return res.status(403).json({
          error: true,
          message: "Refresh token missing",
          validation: [{ field: "refresh_token", message: "Refresh token missing" }],
        });
      } else {
        const verify = JwtService.verifyRefreshToken(token, config.jwt_key);
        if (!verify) {
          // Delete invalid token from database
          const sdk = req.sdk;
          sdk.setProjectId("testing");
          sdk.setTable("tokens");

          await sdk.delete("tokens", {
            token: req.body.refresh_token,
            user_id: req.body.user_id
          });

          return res.status(401).json({
            error: true,
            message: "Token Invalid",
          });
        } else {
          // Check if token exists in database
          const sdk = req.sdk;
          sdk.setProjectId("testing");
          sdk.setTable("tokens");

          const tokenRecord = await sdk.findOne("tokens", {
            token: req.body.refresh_token,
            user_id: req.body.user_id
          });

          if (tokenRecord) {
            return res.status(200).json({
              error: false,
              access_token: JwtService.createAccessToken(
                {
                  user_id: req.body.user_id,
                  role: req.body.role,
                },
                config.jwt_expire,
                config.jwt_key
              ),
              refresh_token: token,
            });
          } else {
            return res.status(401).json({
              error: true,
              message: "Token Invalid",
            });
          }
        }
      }
    } catch (error) {
      return res.status(403).json({
        error: true,
        message: error.message,
      });
    }
  });
};
