const JwtService = require("../../../baas/services/JwtService");

module.exports = function (app) {
  const config = app.get("configuration");

  app.post("/v1/api/testing/member/lambda/verify", async function (req, res) {
    try {
      const { token } = req.body;

      if (!token) {
        return res.status(400).json({
          error: true,
          message: "Verification token is required"
        });
      }

      const RoleClass = require(`../roles/member`);
      if (!RoleClass.permissions.canVerifyEmail) {
        return res.status(403).json({
          error: true,
          message: `${RoleClass.name} cannot verify email`
        });
      }

      // Verify the token
      let decoded;
      try {
        decoded = JwtService.verifyToken(token, config.jwt_key);
      } catch (err) {
        return res.status(400).json({
          error: true,
          message: "Invalid or expired verification token"
        });
      }

      // Get the verification token from database
      req.sdk.setProjectId("testing");
      const verificationToken = await req.sdk.findOne('tokens', {
        where: {
          token: token,
          user_id: decoded.user_id
        }
      });

      if (!verificationToken) {
        return res.status(400).json({
          error: true,
          message: "Verification token not found"
        });
      }

      // Check if token is expired
      if (new Date() > new Date(verificationToken.expires_at)) {
        return res.status(400).json({
          error: true,
          message: "Verification token has expired"
        });
      }

      // Update user verify status
      await req.sdk.update('user',
        { verify: 1, status: 1 },
        { where: { id: decoded.user_id, status: 0 } }
      );

      // Delete the used verification token
      await req.sdk.delete('verification_tokens', {
        where: { token: token }
      });

      return res.status(200).json({
        error: false,
        message: "Email verified successfully"
      });

    } catch (err) {
      res.status(500).json({
        error: true,
        message: err.message
      });
    }
  });
}; 