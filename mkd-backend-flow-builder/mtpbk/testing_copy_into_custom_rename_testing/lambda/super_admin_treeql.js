const QueryBuilder = require("../../../baas/core/QueryBuilder");
const RequestUtils = require("../../../baas/core/RequestUtils");
const TokenMiddleware = require("../../../baas/middleware/TokenMiddleware");
/**
 * Examples
 * joins.

GET /records/news/1?join=comments,users

pagination

GET /records/news?order=id,desc&page=1,50

filters

GET /records/news?filter=id,gt,1&filter=id,lt,3


 */


module.exports = function (app) {
  // GET single record
  app.get("/api/v1/records/testing/super_admin/:table/:id", [TokenMiddleware()], async function (req, res) {
    try {
      let sdk = req.sdk; //uses backendSDK
      const table = req.params.table;
      const id = req.params.id;
      const role = req.role;

      // Validate input
      if (!table || !id) {
        return res.status(403).json({
          error: true,
          message: "Invalid table or ID"
        });
      }

      // Check role permissions
      const RoleClass = require(`../roles/super_admin`);
      console.log(RoleClass.slug, role);
      if (RoleClass.slug !== role) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      // Permission checks
      if (!RoleClass.canPerformOperation(table, 'getOne')) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access to get single record"
        });
      }

      // Process joins
      let { joins, hasJoin } = RequestUtils.getRequestJoins(req);
      let joinData = [];

      if (hasJoin) {
        if (!RoleClass.canPerformOperation(table, 'join')) {
          return res.status(403).json({
            error: true,
            message: "Forbidden join access"
          });
        }

        // Implement join data collection
        for (let relation of joins) { //TODO fix this
          let relationData = relation.split("|");
          let joinTable = relationData[0];
          let joinForeignKey = relationData[1];
          let relationInfo = await guessRelationInfo(table, joinTable, joinForeignKey); //TODO doesnt exist implement
          joinData = [...joinData, ...relationInfo];
        }
      }

      // Set project and table
      sdk.setProjectId("testing");
      sdk.setTable(table);

      // Process IDs (support single and multiple)
      let ids = id.split(",");

      // Get records with optional joins, TODO, check it works
      let record = await getResult(sdk, ids, joinData, RoleClass.getBlacklistedFields(table));

      // Return response
      return res.status(200).json({
        status: 200,
        response: {
          model: record[0] || null,
          // mapping: await getFieldMappings(table, record[0])
        }
      });
    } catch (error) {
      console.error('Error fetching record:', error);
      return res.status(500).json({
        error: true,
        message: "Internal server error",
      });
    }
  });

  // LIST/PAGINATE records
  app.get("/api/v1/records/testing/super_admin/:table", [TokenMiddleware()], async function (req, res) {
    try {
      let sdk = req.sdk;
      const table = req.params.table;
      const role = req.role;

      // Validate input
      if (!table) {
        return res.status(400).json({
          error: true,
          message: "Invalid table name"
        });
      }

      // Check role permissions
      const RoleClass = require(`../roles/super_admin`);
      console.log(RoleClass.slug, role);
      if (RoleClass.slug !== role) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      // Permission checks
      if (!RoleClass.canPerformOperation(table, 'list')) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access to list records"
        });
      }

      let { order, direction } = RequestUtils.getRequestOrdering(req);
      let { filterData, hasFilter } = RequestUtils.getRequestFilters(req);
      let { joins, hasJoin } = RequestUtils.getRequestJoins(req);
      let { page, limit, hasPagination } = RequestUtils.getRequestPagination(req);
      let size = req.query.size ?? 2000;
      let joinData = [];

      sdk.setProjectId("testing");
      sdk.setTable(table);

      // Process joins if present
      if (hasJoin) {
        if (!RoleClass.canPerformOperation(table, 'join')) {
          return res.status(403).json({
            error: true,
            message: "Forbidden join access"
          });
        }

        joinData = await Promise.all(joins.map(async (relation) => {
          const [joinTable, joinForeignKey] = relation.split("|");
          return await guessRelationInfo(table, joinTable, joinForeignKey);
        }));
        joinData = joinData.flat();
      }

      // Get results based on pagination
      let result;
      if (hasPagination) {
        result = await getPaginated(
          sdk,
          page,
          limit,
          order,
          direction,
          filterData,
          joinData,
          RoleClass.getBlacklistedFields(table)
        );
      } else {
        let list = await getList(
          sdk,
          size,
          order,
          direction,
          filterData,
          joinData,
          RoleClass.getBlacklistedFields(table)
        );
        result = { list };
      }

      // Get field mappings
      //const mappings = await getFieldMappings(table, result.list[0]);

      return res.json({
        status: 200,
        response: {
          ...result,
          // mapping: mappings
        }
      });

    } catch (error) {
      console.error('Error in list/paginate endpoint:', error);
      return res.status(500).json({
        error: true,
        message: "Internal server error"
      });
    }
  });

  // CREATE record
  app.post("/api/v1/records/testing/super_admin/:table", [TokenMiddleware()], async function (req, res) {
    try {
      let sdk = req.sdk;
      const table = req.params.table;
      const payload = req.body;
      const role = req.role;

      // Validate input
      if (!table) {
        return res.status(400).json({
          error: true,
          message: "Invalid table name"
        });
      }

      // Check role permissions
      const RoleClass = require(`../roles/super_admin`);
      if (RoleClass.slug !== role) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      // Permission checks
      if (!RoleClass.canPerformOperation(table, 'create')) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access to create records"
        });
      }

      // Load and validate model
      const ModelClass = require(`../models/${table}`);
      const model = new ModelClass(payload);

      if (!model.isValid()) {
        return res.status(400).json({
          error: true,
          message: "Validation errors",
          errors: model.getErrors()
        });
      }

      // Remove any blacklisted fields from payload before insert
      const blacklistedFields = RoleClass.getBlacklistedFields(table) || [];
      // const sanitizedPayload = Object.keys(payload).reduce((acc, key) => {
      //   if (!blacklistedFields.includes(key)) {
      //     acc[key] = payload[key];
      //   }
      //   return acc;
      // }, {});

      // Perform insert
      sdk.setProjectId("testing");
      sdk.setTable(table);

      const result = await sdk.insert(payload);

      // Remove blacklisted fields from response
      const sanitizedResult = Object.keys(result).reduce((acc, key) => {
        if (!blacklistedFields.includes(key)) {
          acc[key] = result[key];
        }
        return acc;
      }, {});

      return res.json({
        status: 200,
        response: {
          model: sanitizedResult
        }
      });

    } catch (error) {
      console.error('Error in create endpoint:', error);
      return res.status(500).json({
        error: true,
        message: "Internal server error"
      });
    }
  });

  // UPDATE record
  app.put("/api/v1/records/testing/super_admin/:table/:id?", [TokenMiddleware()], async function (req, res) {
    try {
      let sdk = req.sdk;
      const table = req.params.table;
      const id = req.params.id;
      const payload = req.body;
      const role = req.role;

      // Validate input
      if (!table) {
        return res.status(400).json({
          error: true,
          message: "Invalid table name"
        });
      }

      // Check role permissions
      const RoleClass = require(`../roles/super_admin`);
      if (RoleClass.slug !== role) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      // Permission checks
      if (!RoleClass.canPerformOperation(table, 'update')) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access to update records"
        });
      }

      // Load and validate model
      const ModelClass = require(`../models/${table}`);
      const model = new ModelClass(payload);

      if (!model.isValid()) {
        return res.status(400).json({
          error: true,
          message: "Validation errors",
          errors: model.getErrors()
        });
      }

      // Set project and table
      sdk.setProjectId("testing");
      sdk.setTable(table);

      let record = await sdk.update({
        ...payload,
      }, id);

      // Remove blacklisted fields from response
      const blacklistedFields = RoleClass.getBlacklistedFields(table) || [];
      const sanitizedResult = Object.keys(record).reduce((acc, key) => {
        if (!blacklistedFields.includes(key)) {
          acc[key] = record[key];
        }
        return acc;
      }, {});

      return res.json({
        status: 200,
        response: {
          model: sanitizedResult,
          message: "Record updated successfully"
        }
      });

    } catch (error) {
      console.error('Error in update endpoint:', error);
      return res.status(500).json({
        error: true,
        message: "Internal server error"
      });
    }
  });

  // DELETE record
  app.delete("/api/v1/records/testing/super_admin/:table/:id?", [TokenMiddleware()], async function (req, res) {
    try {
      let sdk = req.sdk;
      const table = req.params.table;
      const id = req.params.id;
      const role = req.role;

      // Validate input
      if (!table || !id) {
        return res.status(400).json({
          error: true,
          message: "Invalid table name or ID"
        });
      }

      // Check role permissions
      const RoleClass = require(`../roles/super_admin`);
      if (RoleClass.slug !== role) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      // Permission checks
      if (!RoleClass.canPerformOperation(table, 'delete')) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access to delete records"
        });
      }

      // Set project and table
      sdk.setProjectId("testing");
      sdk.setTable(table);

      // Perform delete
      const result = await sdk.delete(id);

      return res.json({
        status: 200,
        response: {
          success: true,
          message: "Record deleted successfully",
          affectedRows: result?.affectedRows || 0
        }
      });

    } catch (error) {
      console.error('Error in delete endpoint:', error);
      return res.status(500).json({
        error: true,
        message: "Internal server error"
      });
    }
  });
};

/**
 * Guess relation information between tables
 * @param {string} sourceTable 
 * @param {string} joinTable 
 * @param {string} joinForeignKey 
 * @returns {Promise<Array>} Relation information
 */
async function guessRelationInfo(sourceTable, joinTable, joinForeignKey) {
  try {
    // If explicit foreign key is provided, use it
    if (joinForeignKey) {
      return [{
        sourceTable,
        joinTable,
        foreignKey: joinForeignKey,
        joinType: 'LEFT', // Default to LEFT JOIN for flexibility
        alias: joinTable
      }];
    }

    // Otherwise, try to guess based on common patterns
    const possibleKeys = [
      `${sourceTable}_id`,
      `${joinTable}_id`,
      'id'
    ];

    // Return the most likely relationship
    return [{
      sourceTable,
      joinTable,
      foreignKey: possibleKeys[0], // Use first pattern as default
      joinType: 'LEFT',
      alias: joinTable
    }];
  } catch (error) {
    console.error('Error in guessRelationInfo:', error);
    throw error;
  }
}

/**
 * Get field mappings for UI representation
 * @param {string} table 
 * @param {Object} record 
 * @returns {Promise<Object>} Field mappings
 */
async function getFieldMappings(table, record) {
  // TODO: Implement dynamic field mapping retrieval
  // Could be from:
  // 1. Database metadata
  // 2. Configuration files
  // 3. Role-based field mappings
  return {
    id: "Identifier",
    name: "Full Name"
  };
}

/**
 * Get database results with optional joins
 * @param {BackendSDK} sdk 
 * @param {Array} ids 
 * @param {Array} joins 
 * @param {Object} select 
 * @returns {Promise<Array>} Query results
 */
async function getResult(sdk, ids = [], joins = [], select = {}, blacklistedFields) {
  try {
    let queryBuilder = new QueryBuilder(sdk).whereIn(`${sdk.getTable()}.id`, ids);

    // Add base table fields to select
    const baseFields = select[sdk.getTable()] || ['*'];
    queryBuilder.select(baseFields.map(field => `${sdk.getTable()}.${field}`));

    // Add joins if present
    if (joins.length > 0) {
      queryBuilder = addBelongsToJoins(queryBuilder, joins, select);
    }

    // Execute query
    let result = await sdk.rawQuery(queryBuilder.get());

    // Process relations
    if (joins.length > 0) {
      result = await matchRelation(sdk, result, joins);
    }

    if (blacklistedFields && Array.isArray(blacklistedFields)) {
      result = result.map(item => {
        blacklistedFields.forEach(field => {
          delete item[field];
        });
        return item;
      });
    }

    return result;
  } catch (error) {
    console.error('Error in getResult:', error);
    throw error;
  }
}

/**
 * Add belongs-to joins to query
 * @param {QueryBuilder} queryBuilder 
 * @param {Array} joins 
 * @param {Object} select 
 * @returns {QueryBuilder} Updated query builder
 */
function addBelongsToJoins(queryBuilder, joins, select) {
  if (!joins || joins.length === 0) return queryBuilder;

  joins.forEach(join => {
    const { sourceTable, joinTable, foreignKey, joinType, alias } = join;

    // Add join clause to query
    queryBuilder.join(
      `${joinTable} AS ${alias}`,
      `${sourceTable}.${foreignKey}`,
      '=',
      `${alias}.id`,
      joinType
    );

    // Add selected fields from joined table if specified
    if (select && select[joinTable]) {
      select[joinTable].forEach(field => {
        queryBuilder.select(`${alias}.${field} AS ${alias}_${field}`);
      });
    }
  });

  return queryBuilder;
}

/**
 * Match and resolve relations in query results
 * @param {BackendSDK} sdk 
 * @param {Array} result 
 * @param {Array} joins 
 * @returns {Promise<Array>} Resolved results
 */
async function matchRelation(sdk, results, joins) {
  if (!results || !joins || joins.length === 0) return results;

  try {
    const processedResults = await Promise.all(results.map(async (record) => {
      const enrichedRecord = { ...record };

      for (const join of joins) {
        const { joinTable, alias } = join;
        const joinedData = {};

        // Extract joined fields from the result
        Object.keys(record).forEach(key => {
          if (key.startsWith(`${alias}_`)) {
            const fieldName = key.replace(`${alias}_`, '');
            joinedData[fieldName] = record[key];
            delete enrichedRecord[key]; // Remove the prefixed field
          }
        });

        // Add joined data as a nested object
        if (Object.keys(joinedData).length > 0) {
          enrichedRecord[joinTable] = joinedData;
        }
      }

      return enrichedRecord;
    }));

    return processedResults;
  } catch (error) {
    console.error('Error in matchRelation:', error);
    throw error;
  }
}

//TODO: check and fix if needed
async function getList(sdk, limit = 20, order = "id", direction = "desc", filters = [], joins = [], blacklistedFields = []) {
  try {
    let queryBuilder = (new QueryBuilder(sdk)).orderBy(order, direction);
    queryBuilder = addQueryFilters(queryBuilder, filters);
    queryBuilder = addBelongsToJoins(queryBuilder, joins);

    // Exclude blacklisted fields from select
    const tableFields = await sdk.getTableFields();
    const selectFields = tableFields.filter(field => !blacklistedFields.includes(field));
    queryBuilder.select(selectFields);

    let result = await sdk.rawQuery(queryBuilder.get(limit));
    result = await matchRelation(sdk, result, joins);

    return result;
  } catch (error) {
    console.error('Error in getList:', error);
    throw error;
  }
}

//TODO: check and fix if needed
async function getPaginated(sdk, page = 1, limit = 20, order = "id", direction = "desc", filters = [], joins = [], blacklistedFields = []) {
  try {
    let paginationService = new PaginationService(page, limit);
    paginationService.setSortField(order);
    paginationService.setSortDirection(direction);

    // Count query
    let countBuilder = (new QueryBuilder(sdk));
    countBuilder = addQueryFilters(countBuilder, filters);
    countBuilder = addBelongsToJoins(countBuilder, joins);
    let count = await sdk.rawQuery(countBuilder.count());
    paginationService.setCount(count);

    // Data query
    let offset = (page - 1) * limit;
    let queryBuilder = (new QueryBuilder(sdk))
      .limit(limit, offset)
      .orderBy(order, direction);

    queryBuilder = addQueryFilters(queryBuilder, filters);
    queryBuilder = addBelongsToJoins(queryBuilder, joins);

    // Exclude blacklisted fields from select
    const tableFields = await sdk.getTableFields();
    const selectFields = tableFields.filter(field => !blacklistedFields.includes(field));
    queryBuilder.select(selectFields);

    let result = await sdk.rawQuery(queryBuilder.get());
    result = await matchRelation(sdk, result, joins);

    return {
      list: result,
      page: paginationService.getPage(),
      limit: paginationService.getLimit(),
      total: paginationService.getCount(),
      num_pages: paginationService.getNumPages(),
    };
  } catch (error) {
    console.error('Error in getPaginated:', error);
    throw error;
  }
}

//TODO: check and fix if needed
function addQueryFilters(query, filters = []) {
  filters.forEach(filter => {
    let filterTokens = filter.split(",");
    let column = filterTokens[0];
    let operator = filterTokens[1];
    let operand = filterTokens[2];

    if (column === 'id') column = `${query.table}.id`;

    if (operator == "cs") query = query.whereContains(column, operand)
    else if (operator == "ncs") query = query.whereNotContains(column, operand)
    else if (operator == "sw") query = query.whereStartsWith(column, operand)
    else if (operator == "nsw") query = query.whereNotStartsWith(column, operand)
    else if (operator == "ew") query = query.whereEndsWith(column, operand)
    else if (operator == "new") query = query.whereNotEndsWith(column, operand)
    else if (operator == "eq") query = query.where(column, operand)
    else if (operator == "neq") query = query.whereNot(column, operand)
    else if (operator == "lt") query = query.where(column, "<", operand)
    else if (operator == "nlt") query = query.whereNot(column, "<", operand)
    else if (operator == "le") query = query.where(column, "<=", operand)
    else if (operator == "nle") query = query.whereNot(column, "<=", operand)
    else if (operator == "gt") query = query.where(column, ">", operand)
    else if (operator == "ngt") query = query.whereNot(column, ">", operand)
    else if (operator == "ge") query = query.where(column, ">=", operand)
    else if (operator == "nge") query = query.whereNot(column, ">=", operand)
    else if (operator == "is") query = query.whereNull(column)
    else if (operator == "nis") query = query.whereNotNull(column)
    else if (operator == "in") query = query.whereIn(column, filterTokens.slice(2))
    else if (operator == "nin") query = query.whereNotIn(column, filterTokens.slice(2))
    else if (operator == "bt") query = query.whereBetween(column, operand, filterTokens[3]);
    else if (operator == "nbt") query = query.whereNotBetween(column, operand, filterTokens[3]);
    //OR Conditions
    else if (operator == "ocs") query = query.orWhere(column, "LIKE", `'%${operand}%'`)
    else if (operator == "oncs") query = query.orWhere(column, "NOT LIKE", `'%${operand}%'`)
    else if (operator == "osw") query = query.orWhere(column, "LIKE", `'${operand}%'`)
    else if (operator == "onsw") query = query.orWhere(column, "NOT LIKE", `'${operand}%'`)
    else if (operator == "oew") query = query.orWhere(column, "LIKE", `'%${operand}'`)
    else if (operator == "onew") query = query.orWhere(column, "NOT LIKE", `'%${operand}'`)
    else if (operator == "oeq") query = query.orWhere(column, operand)
    else if (operator == "oneq") query = query.orWhere(column, "!=", operand)
    else if (operator == "olt") query = query.orWhere(column, "<", operand)
    else if (operator == "ole") query = query.orWhere(column, "<=", operand)
    else if (operator == "ogt") query = query.orWhere(column, ">", operand)
    else if (operator == "oge") query = query.orWhere(column, ">=", operand)
  })

  return query;
}