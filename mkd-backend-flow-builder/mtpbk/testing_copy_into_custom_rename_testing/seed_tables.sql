INSERT INTO testing_company VALUES (NULL, 'random_string_0', 'random_string_0', 'random_string_0', 'random_string_0', 0, '2025-01-05}');
INSERT INTO testing_company VALUES (NULL, 'random_string_1', 'random_string_1', 'random_string_1', 'random_string_1', 0, '2025-01-05}');
INSERT INTO testing_company VALUES (NULL, 'random_string_2', 'random_string_2', 'random_string_2', 'random_string_2', 0, '2025-01-05}');
INSERT INTO testing_user VALUES (NULL, '<EMAIL>', '$2a$10$aGzFdNa5YNs0ZWKPawgZ3OCYi2.zg2y5od25wPDkyVYwA9KuqVNzC', 0, 'super_admin', '{"key": "value"}', 0, 1, 27, '2025-01-05');
INSERT INTO testing_user VALUES (NULL, '<EMAIL>', '$2a$10$xjLoQJ9RA0FFR1rd4ktIruIms2fAFP4/CipQfEyEbBjHw/dsS/I5O', 0, 'company_admin', '{"key": "value"}', 0, 0, 70, '2025-01-05');
INSERT INTO testing_user VALUES (NULL, '<EMAIL>', '$2a$10$VYzEn7VtFNUcSdEWmPg7z..qtVf0YEslU4CD6rys5mzTiPbUC8cFS', 0, 'member', '{"key": "value"}', 0, 1, 25, '2025-01-05');
INSERT INTO testing_cms VALUES (NULL, 'random_string_0', 0, 'This is a long text entry for record 0');
INSERT INTO testing_cms VALUES (NULL, 'random_string_1', 0, 'This is a long text entry for record 1');
INSERT INTO testing_cms VALUES (NULL, 'random_string_2', 0, 'This is a long text entry for record 2');
INSERT INTO testing_inventory VALUES (NULL, 'random_string_0', 20, 0, 'This is a long text entry for record 0', 72, 67, 0.7378720843404905, 39);
INSERT INTO testing_inventory VALUES (NULL, 'random_string_1', 54, 0, 'This is a long text entry for record 1', 50, 90, 0.****************, 81);
INSERT INTO testing_inventory VALUES (NULL, 'random_string_2', 71, 0, 'This is a long text entry for record 2', 49, 27, 0.****************, 18);
INSERT INTO testing_warehouse VALUES (NULL, 'random_string_0', 'random_string_0');
INSERT INTO testing_warehouse VALUES (NULL, 'random_string_1', 'random_string_1');
INSERT INTO testing_warehouse VALUES (NULL, 'random_string_2', 'random_string_2');
INSERT INTO testing_accounting VALUES (NULL, 10, 'random_string_0', 40, 0);
INSERT INTO testing_accounting VALUES (NULL, 40, 'random_string_1', 30, 0);
INSERT INTO testing_accounting VALUES (NULL, 14, 'random_string_2', 15, 0);
INSERT INTO testing_accounting_inventory_secret VALUES (NULL, 19, 53);
INSERT INTO testing_accounting_inventory_secret VALUES (NULL, 41, 32);
INSERT INTO testing_accounting_inventory_secret VALUES (NULL, 85, 2);
INSERT INTO testing_hidden VALUES (NULL, 'random_string_0', 38);
INSERT INTO testing_hidden VALUES (NULL, 'random_string_1', 39);
INSERT INTO testing_hidden VALUES (NULL, 'random_string_2', 85);
INSERT INTO testing_ValidationTest VALUES (NULL, 'random_string_0', 'random_string_0', 'random_string_0', 'random_string_0', 'random_string_0', 37, 5, 89, 0.****************, 'random_string_0', NULL, '{"key": "value"}', '2025-01-05', 'random_string_0', 0);
INSERT INTO testing_ValidationTest VALUES (NULL, 'random_string_1', 'random_string_1', 'random_string_1', 'random_string_1', 'random_string_1', 96, 31, 12, 0.****************, 'random_string_1', NULL, '{"key": "value"}', '2025-01-05', 'random_string_1', 0);
INSERT INTO testing_ValidationTest VALUES (NULL, 'random_string_2', 'random_string_2', 'random_string_2', 'random_string_2', 'random_string_2', 0, 32, 8, 0.****************, 'random_string_2', NULL, '{"key": "value"}', '2025-01-05', 'random_string_2', 0);
INSERT INTO testing_tokens VALUES (NULL, 1, 'random_string_0', 0, '{"key": "value"}', 0, '2025-01-05}', '2025-01-05}', '2025-01-05}');
INSERT INTO testing_tokens VALUES (NULL, 1, 'random_string_1', 0, '{"key": "value"}', 0, '2025-01-05}', '2025-01-05}', '2025-01-05}');
INSERT INTO testing_tokens VALUES (NULL, 1, 'random_string_2', 0, '{"key": "value"}', 0, '2025-01-05}', '2025-01-05}', '2025-01-05}');
INSERT INTO testing_preference VALUES (NULL, 'random_string_0', 'random_string_0', 'random_string_0', 'random_string_0', 6);
INSERT INTO testing_preference VALUES (NULL, 'random_string_1', 'random_string_1', 'random_string_1', 'random_string_1', 77);
INSERT INTO testing_preference VALUES (NULL, 'random_string_2', 'random_string_2', 'random_string_2', 'random_string_2', 37);
INSERT INTO testing_uploads VALUES (NULL, 'random_string_0', 'random_string_0', 1, 74, 46, 0, '2025-01-05}', '2025-01-05}');
INSERT INTO testing_uploads VALUES (NULL, 'random_string_1', 'random_string_1', 1, 58, 35, 0, '2025-01-05}', '2025-01-05}');
INSERT INTO testing_uploads VALUES (NULL, 'random_string_2', 'random_string_2', 1, 98, 16, 0, '2025-01-05}', '2025-01-05}');
INSERT INTO testing_job VALUES (NULL, 'random_string_0', '{"key": "value"}', 'random_string_0', 84, 0, '2025-01-05}', '2025-01-05}');
INSERT INTO testing_job VALUES (NULL, 'random_string_1', '{"key": "value"}', 'random_string_1', 20, 0, '2025-01-05}', '2025-01-05}');
INSERT INTO testing_job VALUES (NULL, 'random_string_2', '{"key": "value"}', 'random_string_2', 61, 0, '2025-01-05}', '2025-01-05}');
INSERT INTO testing_switchs VALUES (NULL, 'random_string_0', 12, 1, 0);
INSERT INTO testing_switchs VALUES (NULL, 'random_string_1', 86, 2, 0);
INSERT INTO testing_switchs VALUES (NULL, 'random_string_2', 7, 1, 0);
