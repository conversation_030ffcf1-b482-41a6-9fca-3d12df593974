
const BaseModel = require('../../../baas/core/BaseModel');

class hidden extends BaseModel {
  static schema() {
    return [
      {
        "name": "id",
        "type": "primary key",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "name",
        "type": "string",
        "validation": "required,length:10,alphanumeric",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "code",
        "type": "integer",
        "validation": "required,enum:1,2,3",
        "defaultValue": null,
        "mapping": null
      }
    ];
  }




}

module.exports = hidden;
