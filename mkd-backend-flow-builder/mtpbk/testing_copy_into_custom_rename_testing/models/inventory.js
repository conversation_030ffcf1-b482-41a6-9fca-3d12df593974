
const BaseModel = require('../../../baas/core/BaseModel');

class inventory extends BaseModel {
  static schema() {
    return [
      {
        "name": "id",
        "type": "primary key",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "name",
        "type": "string",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "quantity",
        "type": "integer",
        "validation": "required,integer",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "status",
        "type": "mapping",
        "validation": "required",
        "defaultValue": null,
        "mapping": "0:inactive,1:active,2:intransit"
      },
      {
        "name": "description",
        "type": "long text",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "user_id",
        "type": "integer",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "expiry",
        "type": "integer",
        "validation": "required,negative",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "manufactured_version",
        "type": "double",
        "validation": "required,decimal",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "warehouse_id",
        "type": "integer",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      }
    ];
  }


  transformStatus(value) {
    const mappings = {
      '0': 'inactive',
      '1': 'active',
      '2': 'intransit'
    };
    return mappings[value] || value;
  }

  static mapping () {
    return {
      "status": {
        "0": "inactive",
        "1": "active",
        "2": "intransit"
      }
    };
  }

}

module.exports = inventory;
