
const BaseModel = require('../../../baas/core/BaseModel');

class cms extends BaseModel {
  static schema() {
    return [
      {
        "name": "id",
        "type": "primary key",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "label",
        "type": "string",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "type",
        "type": "mapping",
        "validation": "required",
        "defaultValue": "0",
        "mapping": "0:Text,1:Number,2:Image,3:Raw"
      },
      {
        "name": "value",
        "type": "long text",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      }
    ];
  }


  transformType(value) {
    const mappings = {
      '0': 'Text',
      '1': 'Number',
      '2': 'Image',
      '3': 'Raw'
    };
    return mappings[value] || value;
  }

  static mapping () {
    return {
      "type": {
        "0": "Text",
        "1": "Number",
        "2": "Image",
        "3": "Raw"
      }
    };
  }

}

module.exports = cms;
