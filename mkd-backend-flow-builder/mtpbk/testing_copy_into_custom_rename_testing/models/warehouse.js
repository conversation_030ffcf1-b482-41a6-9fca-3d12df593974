
const BaseModel = require('../../../baas/core/BaseModel');

class warehouse extends BaseModel {
  static schema() {
    return [
      {
        "name": "id",
        "type": "primary key",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "name",
        "type": "string",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "website",
        "type": "string",
        "validation": "required,url",
        "defaultValue": null,
        "mapping": null
      }
    ];
  }




}

module.exports = warehouse;
