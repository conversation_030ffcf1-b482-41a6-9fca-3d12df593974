
const BaseModel = require('../../../baas/core/BaseModel');

class ValidationTest extends BaseModel {
  static schema() {
    return [
      {
        "name": "id",
        "type": "primary key",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "requiredField",
        "type": "string",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "emailField",
        "type": "string",
        "validation": "email",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "lengthField",
        "type": "string",
        "validation": [
          "length",
          {
            "length": 5
          }
        ],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "enumField",
        "type": "string",
        "validation": [
          "enum",
          {
            "enum": [
              "a",
              "b",
              "c"
            ]
          }
        ],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "patternField",
        "type": "string",
        "validation": [
          "pattern",
          {
            "pattern": "^[A-Z]+$"
          }
        ],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "positiveField",
        "type": "integer",
        "validation": "positive",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "negativeField",
        "type": "integer",
        "validation": "negative",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "integerField",
        "type": "integer",
        "validation": "integer",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "decimalField",
        "type": "double",
        "validation": "decimal",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "alphanumericField",
        "type": "string",
        "validation": "alphanumeric",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "uuidField",
        "type": "uuid",
        "validation": "uuid",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "jsonField",
        "type": "json",
        "validation": "json",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "dateField",
        "type": "date",
        "validation": "date",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "phoneField",
        "type": "string",
        "validation": "phone",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "relatedField",
        "type": "mapping",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      }
    ];
  }




  getRelated(fields) {
    const relatedModel = require('./related.js');
    return new relatedModel(fields);
  }

}

module.exports = ValidationTest;
