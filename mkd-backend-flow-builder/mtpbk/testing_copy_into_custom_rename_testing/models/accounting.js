
const BaseModel = require('../../../baas/core/BaseModel');

class accounting extends BaseModel {
  static schema() {
    return [
      {
        "name": "id",
        "type": "primary key",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "inventory_id",
        "type": "integer",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "description",
        "type": "string",
        "validation": "required,pattern:^[a-zA-Z]+$",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "quantity",
        "type": "integer",
        "validation": "required,integer",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "status",
        "type": "mapping",
        "validation": "required",
        "defaultValue": "0",
        "mapping": "0:new,1:completed"
      }
    ];
  }


  transformStatus(value) {
    const mappings = {
      '0': 'new',
      '1': 'completed'
    };
    return mappings[value] || value;
  }

  static mapping () {
    return {
      "status": {
        "0": "new",
        "1": "completed"
      }
    };
  }

}

module.exports = accounting;
