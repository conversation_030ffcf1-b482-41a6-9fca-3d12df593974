
const BaseModel = require('../../../baas/core/BaseModel');

class accounting_inventory_secret extends BaseModel {
  static schema() {
    return [
      {
        "name": "id",
        "type": "primary key",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "inventory_id",
        "type": "integer",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "accounting_id",
        "type": "integer",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      }
    ];
  }




  getInventory(fields) {
    const inventoryModel = require('./inventory.js');
    return new inventoryModel(fields);
  }


  getAccounting(fields) {
    const accountingModel = require('./accounting.js');
    return new accountingModel(fields);
  }

}

module.exports = accounting_inventory_secret;
