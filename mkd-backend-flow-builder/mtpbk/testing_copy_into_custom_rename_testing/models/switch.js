const BaseModel = require('../../../baas/core/BaseModel');

class SwitchModel extends BaseModel {
  static schema() {
    return [
      {
        "name": "id",
        "type": "primary key",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "label",
        "type": "string",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "options",
        "type": "integer",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "user_id",
        "type": "integer",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "status",
        "type": "mapping",
        "validation": "required",
        "defaultValue": "0",
        "mapping": "0:off,1:on"
      }
    ]; 
  }

  transformStatus(value) {
    const mappings = {
      '0': 'off',
      '1': 'on'
    };
    return mappings[value] || value;
  }

  static mapping() { 
    return {
      "status": {
        "0": "off",
        "1": "on"
      }
    }; 
  }

  getUser(fields) {
    const userModel = require('./user.js');
    return new userModel(fields);
  }
}

module.exports = SwitchModel;
  