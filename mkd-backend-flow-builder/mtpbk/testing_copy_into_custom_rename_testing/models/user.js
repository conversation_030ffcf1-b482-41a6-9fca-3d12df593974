
const BaseModel = require('../../../baas/core/BaseModel');

class user extends BaseModel {
  static schema() {
    return [
      {
        "name": "id",
        "type": "primary key",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "email",
        "type": "string",
        "validation": "required,email",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "login_type",
        "type": "mapping",
        "validation": "required,min:0,max:3",
        "defaultValue": "0",
        "mapping": "0:Regular,1:Google,2:Microsoft,3:Apple"
      },
      {
        "name": "role_id",
        "type": "string",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "data",
        "type": "json",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "status",
        "type": "mapping",
        "validation": "required,positive",
        "defaultValue": "0",
        "mapping": "0:Active,1:Inactive,2:Suspend"
      },
      {
        "name": "verify",
        "type": "boolean",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "company_id",
        "type": "integer",
        "validation": [],
        "defaultValue": "0",
        "mapping": null
      },
      {
        "name": "created_at",
        "type": "date",
        "validation": "date",
        "defaultValue": null,
        "mapping": null
      }
    ];
  }


  transformLoginType(value) {
    const mappings = {
      '0': 'Regular',
      '1': 'Google',
      '2': 'Microsoft',
      '3': 'Apple'
    };
    return mappings[value] || value;
  }


  transformStatus(value) {
    const mappings = {
      '0': 'Active',
      '1': 'Inactive',
      '2': 'Suspend'
    };
    return mappings[value] || value;
  }

  static mapping () {
    return {
      "login_type": {
        "0": "Regular",
        "1": "Google",
        "2": "Microsoft",
        "3": "Apple"
      },
      "status": {
        "0": "Active",
        "1": "Inactive",
        "2": "Suspend"
      }
    };
  }

}

module.exports = user;
