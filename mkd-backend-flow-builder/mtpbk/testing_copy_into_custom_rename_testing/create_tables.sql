CREATE TABLE IF NOT EXISTS testing_company (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
  logo VARCHAR(255),
  address VARCHAR(255),
  phone VARCHAR(255),
  status INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS testing_user (
  id INT AUTO_INCREMENT PRIMARY KEY,
  email VARCHAR(255) NOT NULL,
  password VARCHAR(255) NOT NULL,
  login_type INT DEFAULT 0 NOT NULL,
  role_id VARCHAR(255),
  data TEXT,
  status INT DEFAULT 0 NOT NULL,
  verify BOOLEAN NOT NULL,
  company_id INT DEFAULT '0',
  created_at DATE
);

CREATE TABLE IF NOT EXISTS testing_cms (
  id INT AUTO_INCREMENT PRIMARY KEY,
  label VARCHAR(255) NOT NULL,
  type INT DEFAULT 0 NOT NULL,
  value TEXT NOT NULL
);

CREATE TABLE IF NOT EXISTS testing_inventory (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VA<PERSON>HA<PERSON>(255) NOT NULL,
  quantity INT NOT NULL,
  status INT NOT NULL,
  description TEXT,
  user_id INT NOT NULL,
  expiry INT NOT NULL,
  manufactured_version DOUBLE NOT NULL,
  warehouse_id INT NOT NULL
);

CREATE TABLE IF NOT EXISTS testing_warehouse (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  website VARCHAR(255) NOT NULL
);

CREATE TABLE IF NOT EXISTS testing_accounting (
  id INT AUTO_INCREMENT PRIMARY KEY,
  inventory_id INT NOT NULL,
  description VARCHAR(255) NOT NULL,
  quantity INT NOT NULL,
  status INT DEFAULT 0 NOT NULL
);

CREATE TABLE IF NOT EXISTS testing_accounting_inventory_secret (
  id INT AUTO_INCREMENT PRIMARY KEY,
  inventory_id INT NOT NULL,
  accounting_id INT NOT NULL
);

CREATE TABLE IF NOT EXISTS testing_hidden (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  code INT NOT NULL
);

CREATE TABLE IF NOT EXISTS testing_ValidationTest (
  id INT AUTO_INCREMENT PRIMARY KEY,
  requiredField VARCHAR(255) NOT NULL,
  emailField VARCHAR(255),
  lengthField VARCHAR(255),
  enumField VARCHAR(255),
  patternField VARCHAR(255),
  positiveField INT,
  negativeField INT,
  integerField INT,
  decimalField DOUBLE,
  alphanumericField VARCHAR(255),
  uuidField VARCHAR(36),
  jsonField TEXT,
  dateField DATE,
  phoneField VARCHAR(255),
  relatedField INT
);

CREATE TABLE IF NOT EXISTS testing_tokens (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  token VARCHAR(255) NOT NULL,
  type INT DEFAULT 0 NOT NULL,
  data TEXT,
  status INT DEFAULT 1 NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  expired_at TIMESTAMP
);

CREATE TABLE IF NOT EXISTS testing_preference (
  id INT AUTO_INCREMENT PRIMARY KEY,
  first_name VARCHAR(255),
  last_name VARCHAR(255),
  phone VARCHAR(255),
  photo VARCHAR(255),
  user_id INT NOT NULL
);

CREATE TABLE IF NOT EXISTS testing_uploads (
  id INT AUTO_INCREMENT PRIMARY KEY,
  url VARCHAR(255) NOT NULL,
  caption VARCHAR(255),
  user_id INT,
  width INT,
  height INT,
  type INT DEFAULT 0 NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS testing_job (
  id INT AUTO_INCREMENT PRIMARY KEY,
  task VARCHAR(255) NOT NULL,
  arguments TEXT,
  time_interval VARCHAR(255) DEFAULT 'once',
  retries INT DEFAULT '1',
  status INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS testing_switchs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  label VARCHAR(255) NOT NULL,
  options INT NOT NULL,
  user_id INT NOT NULL,
  status INT DEFAULT 0 NOT NULL
);

