
const BaseRole = require('../../../../baas/core/BaseRole');

class super_admin extends BaseRole {
  static id = 'role_admin_1736076082026';
  static name = 'Super Admin';
  static slug = 'super_admin';
  static permissions = {
    "routes": [
      "route_1736076210652_jefx076w5",
      "route_1736076210652_gr0s2nqxf",
      "route_1736076210653_ybk49v9ou",
      "route_1736076210653_xs33phrt5",
      "route_1736076210653_m886sh3ha",
      "route_1734835558351_p7t0wlpbr",
      "route_1734835558351_umk8qzgaq",
      "route_1734835558351_vq257bfqw",
      "route_1734835558352_dna6umo13",
      "route_1734835558351_578vyr31y"
    ],
    "canCreateUsers": true,
    "canEditUsers": true,
    "canDeleteUsers": true,
    "canManageRoles": true,
    "canLogin": true,
    "canRegister": true,
    "canForgot": false,
    "canReset": false,
    "canGoogleLogin": true,
    "canAppleLogin": false,
    "canMicrosoftLogin": false,
    "canMagicLinkLogin": false,
    "needs2FA": true,
    "canSetPermissions": true,
    "canPreference": true,
    "canVerifyEmail": false,
    "canUpload": true,
    "canStripe": false,
    "canStripeWebhook": false,
    "canRealTime": true,
    "canAI": true,
    "canUpdateEmail": true,
    "canUpdatePassword": true,
    "canUpdateOtherUsers": true,
    "treeql": {
      "enabled": true,
      "models": {
        "user": {
          "allowed": true,
          "blacklistedFields": [
            "password"
          ],
          "operations": {
            "get": true,
            "getOne": true,
            "getAll": true,
            "post": true,
            "put": true,
            "delete": true,
            "paginate": true,
            "join": true
          }
        },
        "preference": {
          "allowed": true,
          "blacklistedFields": [],
          "operations": {
            "get": true,
            "getOne": true,
            "getAll": true,
            "post": true,
            "put": true,
            "delete": true,
            "paginate": true,
            "join": true
          }
        },
        "uploads": {
          "allowed": true,
          "blacklistedFields": [],
          "operations": {
            "get": true,
            "getOne": true,
            "getAll": true,
            "post": true,
            "put": true,
            "delete": true,
            "paginate": true,
            "join": true
          }
        },
        "cms": {
          "allowed": true,
          "blacklistedFields": [],
          "operations": {
            "get": true,
            "getOne": true,
            "getAll": true,
            "post": true,
            "put": true,
            "delete": true,
            "paginate": true,
            "join": true
          }
        },
        "switchs": {
          "allowed": true,
          "blacklistedFields": [],
          "operations": {
            "get": true,
            "getOne": true,
            "getAll": true,
            "post": true,
            "put": true,
            "delete": true,
            "paginate": true,
            "join": true
          }
        }
      }
    }
  };
  static index = 0;

  // Helper method to check route permission
  static hasRoutePermission(routeId) {
    return this.permissions?.routes?.includes(routeId) || false;
  }

  // List of models this role can access
  static allowedModels = [
    "user",
    "preference",
    "uploads",
    "cms",
    "switchs"
  ];

  /**
  * Check if role can access a specific model
  * @param {string} modelName - Name of the model to check
  * @returns {boolean} Whether model access is allowed
  */
  static canAccessModel(modelName) {
    return this.permissions?.treeql?.models?.[modelName]?.allowed || false;
  }

  /**
  * Get blacklisted fields for a model
  * @param {string} modelName - Name of the model
  * @returns {string[]} Array of blacklisted field names
  */
  static getBlacklistedFields(modelName) {
    return this.permissions?.treeql?.models?.[modelName]?.blacklistedFields || [];
  }

  /**
  * Check if role can perform an operation on a model
  * @param {string} modelName - Name of the model
  * @param {string} operation - Operation to check (get, getOne, getAll, post, put, delete, paginate, join)
  * @returns {boolean} Whether operation is allowed
  */
  static canPerformOperation(modelName, operation) {
    const modelConfig = this.permissions?.treeql?.models?.[modelName];
    if (!modelConfig?.allowed) {
      return false;
    }
    return modelConfig.operations?.[operation] || false;
  }

  /**
  * Get all allowed operations for a model
  * @param {string} modelName - Name of the model
  * @returns {Object<string, boolean>} Object mapping operations to permission status
  */
  static getAllowedOperations(modelName) {
    return this.permissions?.treeql?.models?.[modelName]?.operations || {};
  }

  /**
  * Check if TreeQL is enabled for this role
  * @returns {boolean} Whether TreeQL is enabled
  */
  static isTreeQLEnabled() {
    return this.permissions?.treeql?.enabled || false;
  }
}

module.exports = super_admin;
