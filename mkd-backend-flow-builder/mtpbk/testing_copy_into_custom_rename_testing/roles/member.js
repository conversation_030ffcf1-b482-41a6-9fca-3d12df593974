
const BaseRole = require('../../../../baas/core/BaseRole');

class member extends BaseRole {
  static id = 'role_member_1736076082026';
  static name = 'Member';
  static slug = 'member';
  static permissions = {
    "routes": [
      "route_1736076210652_jefx076w5",
      "route_1736076210652_gr0s2nqxf"
    ],
    "canCreateUsers": false,
    "canEditUsers": false,
    "canDeleteUsers": false,
    "canManageRoles": false,
    "canLogin": true,
    "canRegister": true,
    "canForgot": false,
    "canReset": false,
    "canGoogleLogin": true,
    "canAppleLogin": false,
    "canMicrosoftLogin": false,
    "canMagicLinkLogin": false,
    "needs2FA": false,
    "canSetPermissions": false,
    "canPreference": true,
    "canVerifyEmail": true,
    "canUpload": true,
    "canStripe": false,
    "canStripeWebhook": false,
    "canRealTime": true,
    "canAI": true,
    "canUpdateEmail": true,
    "canUpdatePassword": true,
    "canUpdateOtherUsers": false,
    "treeql": {
      "enabled": true,
      "models": {
        "cms": {
          "allowed": true,
          "blacklistedFields": [],
          "operations": {
            "get": true,
            "getOne": true,
            "getAll": true,
            "post": false,
            "put": false,
            "delete": false,
            "paginate": true,
            "join": false
          }
        },
        "uploads": {
          "allowed": true,
          "blacklistedFields": [],
          "operations": {
            "get": true,
            "getOne": true,
            "getAll": true,
            "post": true,
            "put": false,
            "delete": false,
            "paginate": true,
            "join": true
          }
        },
        "preference": {
          "allowed": true,
          "blacklistedFields": [],
          "operations": {
            "get": true,
            "getOne": true,
            "getAll": true,
            "post": true,
            "put": true,
            "delete": false,
            "paginate": true,
            "join": true
          }
        },
        "switchs": {
          "allowed": true,
          "blacklistedFields": [],
          "operations": {
            "get": true,
            "getOne": true,
            "getAll": true,
            "post": false,
            "put": false,
            "delete": false,
            "paginate": false,
            "join": false
          }
        },
        "user": {
          "allowed": false,
          "blacklistedFields": [
            "password"
          ],
          "operations": {
            "get": true,
            "getOne": true,
            "getAll": true,
            "post": true,
            "put": true,
            "delete": true,
            "paginate": true,
            "join": true
          }
        }
      }
    },
    "companyScoped": false
  };
  static index = 2;

  // Helper method to check route permission
  static hasRoutePermission(routeId) {
    return this.permissions?.routes?.includes(routeId) || false;
  }

  // List of models this role can access
  static allowedModels = [
    "cms",
    "uploads",
    "preference",
    "switchs"
  ];

  /**
  * Check if role can access a specific model
  * @param {string} modelName - Name of the model to check
  * @returns {boolean} Whether model access is allowed
  */
  static canAccessModel(modelName) {
    return this.permissions?.treeql?.models?.[modelName]?.allowed || false;
  }

  /**
  * Get blacklisted fields for a model
  * @param {string} modelName - Name of the model
  * @returns {string[]} Array of blacklisted field names
  */
  static getBlacklistedFields(modelName) {
    return this.permissions?.treeql?.models?.[modelName]?.blacklistedFields || [];
  }

  /**
  * Check if role can perform an operation on a model
  * @param {string} modelName - Name of the model
  * @param {string} operation - Operation to check (get, getOne, getAll, post, put, delete, paginate, join)
  * @returns {boolean} Whether operation is allowed
  */
  static canPerformOperation(modelName, operation) {
    const modelConfig = this.permissions?.treeql?.models?.[modelName];
    if (!modelConfig?.allowed) {
      return false;
    }
    return modelConfig.operations?.[operation] || false;
  }

  /**
  * Get all allowed operations for a model
  * @param {string} modelName - Name of the model
  * @returns {Object<string, boolean>} Object mapping operations to permission status
  */
  static getAllowedOperations(modelName) {
    return this.permissions?.treeql?.models?.[modelName]?.operations || {};
  }

  /**
  * Check if TreeQL is enabled for this role
  * @returns {boolean} Whether TreeQL is enabled
  */
  static isTreeQLEnabled() {
    return this.permissions?.treeql?.enabled || false;
  }
}

module.exports = member;
