
const BaseRole = require('../../../../baas/core/BaseRole');

class company_admin extends BaseRole {
  static id = 'role_company_admin_1734835047616';
  static name = 'Company Admin';
  static slug = 'company_admin';
  static permissions = {
    "authRequired": true,
    "routes": [],
    "canCreateUsers": true,
    "canEditUsers": true,
    "canDeleteUsers": true,
    "canManageRoles": true,
    "canLogin": true,
    "canRegister": true,
    "canForgot": false,
    "canReset": false,
    "canGoogleLogin": true,
    "canAppleLogin": false,
    "canMicrosoftLogin": false,
    "canMagicLinkLogin": false,
    "needs2FA": true,
    "canSetPermissions": true,
    "canPreference": true,
    "canVerifyEmail": true,
    "canUpload": true,
    "canStripe": true,
    "canStripeWebhook": true,
    "canRealTime": true,
    "canAI": true,
    "canUpdateEmail": true,
    "canUpdatePassword": true,
    "canUpdateOtherUsers": true,
    "treeql": {
      "enabled": true,
      "models": {
        "accounting": {
          "allowed": true,
          "blacklistedFields": [],
          "operations": {
            "get": true,
            "getOne": true,
            "getAll": true,
            "post": true,
            "put": true,
            "delete": true,
            "paginate": true,
            "join": true
          }
        },
        "warehouse": {
          "allowed": true,
          "blacklistedFields": [],
          "operations": {
            "get": true,
            "getOne": true,
            "getAll": true,
            "post": true,
            "put": true,
            "delete": true,
            "paginate": true,
            "join": true
          }
        },
        "inventory": {
          "allowed": true,
          "blacklistedFields": [],
          "operations": {
            "get": true,
            "getOne": true,
            "getAll": true,
            "post": true,
            "put": true,
            "delete": true,
            "paginate": true,
            "join": true
          }
        },
        "cms": {
          "allowed": true,
          "blacklistedFields": [],
          "operations": {
            "get": true,
            "getOne": true,
            "getAll": true,
            "post": true,
            "put": true,
            "delete": true,
            "paginate": true,
            "join": true
          }
        },
        "company": {
          "allowed": true,
          "blacklistedFields": [],
          "operations": {
            "get": true,
            "getOne": true,
            "getAll": true,
            "post": true,
            "put": true,
            "delete": true,
            "paginate": true,
            "join": true
          }
        },
        "user": {
          "allowed": true,
          "blacklistedFields": [
            "password"
          ],
          "operations": {
            "get": true,
            "getOne": true,
            "getAll": true,
            "post": true,
            "put": true,
            "delete": true,
            "paginate": true,
            "join": true
          }
        },
        "switchs": {
          "allowed": true,
          "blacklistedFields": [
          ],
          "operations": {
            "get": true,
            "getOne": true,
            "getAll": true,
            "post": true,
            "put": true,
            "delete": true,
            "paginate": true,
            "join": true
          }
        }
      }
    },
    "companyScoped": true
  };
  static index = 1;

  // Helper method to check route permission
  static hasRoutePermission(routeId) {
    return this.permissions?.routes?.includes(routeId) || false;
  }

  // List of models this role can access
  static allowedModels = [
    "accounting",
    "warehouse",
    "inventory",
    "cms",
    "company",
    "user"
  ];

  /**
  * Check if role can access a specific model
  * @param {string} modelName - Name of the model to check
  * @returns {boolean} Whether model access is allowed
  */
  static canAccessModel(modelName) {
    return this.permissions?.treeql?.models?.[modelName]?.allowed || false;
  }

  /**
  * Get blacklisted fields for a model
  * @param {string} modelName - Name of the model
  * @returns {string[]} Array of blacklisted field names
  */
  static getBlacklistedFields(modelName) {
    return this.permissions?.treeql?.models?.[modelName]?.blacklistedFields || [];
  }

  /**
  * Check if role can perform an operation on a model
  * @param {string} modelName - Name of the model
  * @param {string} operation - Operation to check (get, getOne, getAll, post, put, delete, paginate, join)
  * @returns {boolean} Whether operation is allowed
  */
  static canPerformOperation(modelName, operation) {
    const modelConfig = this.permissions?.treeql?.models?.[modelName];
    if (!modelConfig?.allowed) {
      return false;
    }
    return modelConfig.operations?.[operation] || false;
  }

  /**
  * Get all allowed operations for a model
  * @param {string} modelName - Name of the model
  * @returns {Object<string, boolean>} Object mapping operations to permission status
  */
  static getAllowedOperations(modelName) {
    return this.permissions?.treeql?.models?.[modelName]?.operations || {};
  }

  /**
  * Check if TreeQL is enabled for this role
  * @returns {boolean} Whether TreeQL is enabled
  */
  static isTreeQLEnabled() {
    return this.permissions?.treeql?.enabled || false;
  }
}

module.exports = company_admin;
