var fs = require("fs");
var path = require("path");

module.exports = function (app) {
    // Check if lambda folder exists
    const lambdaPath = path.join(__dirname, "lambda");
    
    if (fs.existsSync(lambdaPath)) {
        // Read files in lambda directory
        fs.readdirSync(lambdaPath).forEach(function (file) {
            // Skip non-JavaScript files and index.js
            if (file === "index.js" || path.extname(file) !== ".js") {
                return;
            }
            
            // Skip .DS_Store or other system files
            if (file === ".DS_Store") {
                return;
            }
            
            // Require the lambda file
            const name = path.basename(file, ".js");
            require(path.join(lambdaPath, name))(app);
        });
    }
    
    // Original logic for other files in the directory
    fs.readdirSync(__dirname).forEach(function (file) {
        if (file === "index.js" || file === "lambda" || file.substr(file.lastIndexOf(".") + 1) !== "js") {
            return;
        }
        
        if (file === ".DS_Store") {
            return;
        }
        
        var name = file.substr(0, file.indexOf("."));
        require("./" + name)(app);
    });
}

